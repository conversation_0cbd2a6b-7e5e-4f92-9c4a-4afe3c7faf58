/* ===== ESTILOS DEL CONTENEDOR DE DATOS DE LA EMPRESA ===== */

/* ===== TÍTULO EXTERNO DE DATOS DE LA EMPRESA ===== */
.empresa-external-title {
    font-size: 2.5rem;              /* Tamaño grande igual que otros títulos */
    font-weight: bold;              /* Texto en negrita igual que otros */
    color: #5b21b6;                 /* Color morado oscuro igual que otros */
    margin-bottom: 8px;             /* Espacio pequeño debajo del título */
    text-align: left;               /* Alineación a la izquierda */
    max-width: 1200px;              /* Ancho máximo igual al contenedor */
    margin: 30px auto 8px auto;     /* Margen igual que otros contenedores */
}

/* ===== CONTENEDOR PRINCIPAL DE DATOS DE LA EMPRESA ===== */
.empresa-container {
    background: #2d3748;            /* Fondo gris azulado igual que otros */
    padding: 20px;                  /* Espaciado interno igual a otros */
    margin: 30px auto;              /* Margen igual a otros */
    max-width: 1200px;              /* Ancho máximo */
}

/* ===== CONTENIDO DE DATOS DE LA EMPRESA ===== */
.empresa-content {
    display: grid;                  /* Grid layout */
    grid-template-columns: 1fr 1fr; /* Dos columnas iguales */
    gap: 30px;                      /* Espacio entre columnas */
    align-items: start;             /* Alineación superior */
}

/* ===== COLUMNAS DE DATOS DE LA EMPRESA ===== */
.empresa-column {
    background: #1a202c;            /* Fondo azul oscuro igual que otros */
    border-radius: 12px;            /* Bordes redondeados igual que otros */
    border-left: 4px solid #6b46c1; /* Borde izquierdo morado igual que otros */
    padding: 25px;                  /* Espaciado interno */
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3); /* Sombra igual que otros */
}

/* ===== TÍTULOS DE COLUMNA ===== */
.column-title {
    color: #ffffff;                 /* Color blanco */
    font-size: 1.3rem;              /* Tamaño mediano */
    font-weight: 600;               /* Peso semi-bold */
    margin-bottom: 20px;            /* Espacio debajo */
    text-align: center;             /* Centrado */
    border-bottom: 2px solid #6b46c1; /* Línea morada debajo */
    padding-bottom: 10px;           /* Espacio interno debajo */
}

/* ===== GRUPOS DE CAMPOS ===== */
.field-group {
    margin-bottom: 20px;            /* Espacio entre grupos */
}

.field-group:last-child {
    margin-bottom: 0;               /* Sin margen en el último grupo */
}

/* ===== ETIQUETAS DE CAMPOS ===== */
.field-label {
    color: #ffffff;                 /* Color blanco */
    font-size: 0.9rem;              /* Tamaño de fuente */
    font-weight: 500;               /* Peso medio */
    margin-bottom: 8px;             /* Espacio debajo de la etiqueta */
    display: block;                 /* Bloque para ocupar línea completa */
}

/* ===== CAMPOS DE ENTRADA ===== */
.field-input,
.field-textarea {
    width: 100%;                    /* Ancho completo */
    background: #2d3748;            /* Fondo gris oscuro */
    border: 2px solid rgba(255, 255, 255, 0.1); /* Borde semi-transparente */
    border-radius: 6px;             /* Bordes redondeados */
    padding: 12px;                  /* Espaciado interno */
    color: #ffffff;                 /* Texto blanco */
    font-size: 0.9rem;              /* Tamaño de fuente */
    font-family: inherit;           /* Hereda la fuente */
    transition: all 0.3s ease;      /* Transición suave */
    box-sizing: border-box;         /* Incluye padding y border en el ancho */
}

.field-input:focus,
.field-textarea:focus {
    outline: none;                  /* Sin outline por defecto */
    border-color: #6b46c1;          /* Borde morado al enfocar */
    box-shadow: 0 0 0 3px rgba(107, 70, 193, 0.2); /* Sombra morada */
}

.field-input::placeholder,
.field-textarea::placeholder {
    color: rgba(255, 255, 255, 0.5); /* Color gris claro para placeholder */
    font-style: italic;             /* Cursiva para distinguir */
}

/* ===== TEXTAREA ESPECÍFICO ===== */
.field-textarea {
    min-height: 80px;               /* Altura mínima para textarea */
    resize: vertical;               /* Solo redimensionar verticalmente */
}

/* ===== INFORMACIÓN ADICIONAL ===== */
.field-info {
    color: rgba(255, 255, 255, 0.6); /* Color gris claro */
    font-size: 0.75rem;             /* Tamaño pequeño */
    margin-top: 4px;                /* Espacio pequeño arriba */
    font-style: italic;             /* Cursiva */
}

/* ===== CONTADOR DE CARACTERES ===== */
.char-counter {
    color: rgba(255, 255, 255, 0.5); /* Color gris claro */
    font-size: 0.7rem;              /* Tamaño muy pequeño */
    text-align: right;              /* Alineado a la derecha */
    margin-top: 2px;                /* Espacio pequeño arriba */
}

.char-counter.warning {
    color: #f59e0b;                 /* Color amarillo para advertencia */
}

.char-counter.error {
    color: #ef4444;                 /* Color rojo para error */
}

/* ===== ESTILOS DE LOS BOTONES ===== */
.action-buttons {
    display: flex;                  /* Flexbox para organizar botones */
    gap: 10px;                      /* Espacio entre botones */
    justify-content: center;        /* Centra los botones */
    margin-top: 25px;               /* Espacio arriba */
    grid-column: 1 / -1;            /* Ocupa ambas columnas */
}

.btn-save,
.btn-cancel {
    padding: 8px 12px;              /* Espaciado interno */
    border: none;                   /* Sin borde */
    border-radius: 6px;             /* Bordes redondeados */
    font-size: 0.8rem;              /* Tamaño de fuente */
    font-weight: 500;               /* Peso medio */
    cursor: pointer;                /* Cursor de mano */
    transition: all 0.3s ease;      /* Transición suave */
    min-width: 60px;                /* Ancho mínimo */
    flex: 0 0 auto;                 /* No crecen ni se encogen */
}

.btn-save {
    background: #10b981;            /* Fondo verde */
    color: #ffffff;                 /* Texto blanco */
    border: 2px solid #10b981;      /* Borde verde */
}

.btn-save:hover {
    background: #059669;            /* Verde más oscuro al hover */
    border-color: #059669;          /* Borde más oscuro al hover */
    transform: translateY(-1px);    /* Eleva al hover */
    box-shadow: 0 2px 4px rgba(16, 185, 129, 0.3); /* Sombra verde */
}

.btn-cancel {
    background: rgba(255, 255, 255, 0.1); /* Fondo semi-transparente */
    color: #ffffff;                 /* Texto blanco */
    border: 2px solid rgba(255, 255, 255, 0.3); /* Borde semi-transparente */
}

.btn-cancel:hover {
    background: rgba(255, 255, 255, 0.2); /* Fondo más opaco al hover */
    border-color: rgba(255, 255, 255, 0.5); /* Borde más opaco al hover */
    transform: translateY(-1px);    /* Eleva al hover */
}

/* ===== RESPONSIVE PARA TABLETS (768px - 1024px) ===== */
@media (max-width: 1024px) and (min-width: 769px) {
    /* ===== CONTENEDOR PRINCIPAL EN TABLET ===== */
    .empresa-container {
        max-width: calc(100% - 20px); /* Usa casi todo el ancho con margen mínimo */
        padding: 15px;               /* Reduce padding interno */
        margin: 20px 10px;           /* Margen pequeño con los laterales */
    }

    /* ===== TÍTULO EN TABLET ===== */
    .empresa-external-title {
        font-size: 1.4rem;           /* Más pequeño para tablet */
        margin: 20px auto 8px auto;  /* Ajusta márgenes para tablet */
        max-width: calc(100% - 20px); /* Mismo ancho que contenedor */
        padding: 0 10px;             /* Padding lateral */
    }

    /* ===== CONTENIDO EN TABLET - COLUMNAS APILADAS ===== */
    .empresa-content {
        grid-template-columns: 1fr;  /* Una sola columna en tablet */
        gap: 20px;                   /* Reduce espacio entre elementos */
    }

    /* ===== COLUMNAS EN TABLET ===== */
    .empresa-column {
        padding: 20px;               /* Reduce padding interno */
        border-radius: 10px;         /* Reduce bordes redondeados */
    }

    .column-title {
        font-size: 1.2rem;           /* Reduce tamaño del título */
        margin-bottom: 15px;         /* Reduce margen inferior */
    }

    /* ===== CAMPOS EN TABLET ===== */
    .field-input,
    .field-textarea {
        padding: 10px;               /* Reduce padding */
        font-size: 0.85rem;          /* Reduce tamaño de fuente */
    }

    .field-textarea {
        min-height: 70px;            /* Reduce altura mínima */
    }
}

/* ===== RESPONSIVE PARA MÓVILES (≤768px) ===== */
@media (max-width: 768px) {
    /* ===== OCULTAR CONTAINERS POR DEFECTO EN RESPONSIVE ===== */
    .empresa-container.responsive-hidden,
    .empresa-external-title.responsive-hidden {
        display: none;               /* Oculto por defecto en responsive */
    }

    /* ===== CONTENEDOR PRINCIPAL EN MÓVIL ===== */
    .empresa-container {
        max-width: calc(100% - 16px); /* Usa casi todo el ancho con margen mínimo */
        padding: 12px;               /* Padding mínimo */
        margin: 15px 8px;            /* Margen pequeño */
    }

    /* ===== TÍTULO EN MÓVIL ===== */
    .empresa-external-title {
        font-size: 1.8rem;           /* Título más pequeño */
        text-align: center;          /* Centra el título */
        margin: 15px auto 15px auto; /* Margen igual que otros */
        max-width: calc(100% - 16px); /* Mismo ancho que contenedor */
        padding: 0 8px;              /* Padding lateral */
    }

    /* ===== CONTENIDO EN MÓVIL ===== */
    .empresa-content {
        grid-template-columns: 1fr;  /* Una sola columna en móvil */
        gap: 15px;                   /* Reduce espacio entre elementos */
    }

    /* ===== COLUMNAS EN MÓVIL ===== */
    .empresa-column {
        padding: 15px;               /* Reduce padding interno */
        border-radius: 8px;          /* Reduce bordes redondeados */
    }

    .column-title {
        font-size: 1.1rem;           /* Reduce más el tamaño del título */
        margin-bottom: 12px;         /* Reduce margen inferior */
    }

    /* ===== CAMPOS EN MÓVIL ===== */
    .field-input,
    .field-textarea {
        padding: 8px;                /* Reduce padding */
        font-size: 0.8rem;           /* Reduce tamaño de fuente */
    }

    .field-textarea {
        min-height: 60px;            /* Reduce altura mínima */
    }

    .field-label {
        font-size: 0.85rem;          /* Reduce tamaño de etiqueta */
    }

    .field-info {
        font-size: 0.7rem;           /* Reduce tamaño de información */
    }

    /* ===== BOTONES EN MÓVIL ===== */
    .action-buttons {
        gap: 6px;                    /* Espacio muy reducido entre botones */
        justify-content: center !important; /* Centra los botones - forzado */
        align-items: center !important; /* Alinea verticalmente - forzado */
        flex-wrap: nowrap !important; /* No permite salto de línea - forzado */
    }

    .btn-save,
    .btn-cancel {
        padding: 6px 8px !important; /* Padding más razonable - forzado */
        min-width: 45px !important;  /* Ancho mínimo razonable - forzado */
        max-width: 80px !important;  /* Ancho máximo razonable - forzado */
        font-size: 0.75rem !important; /* Fuente legible - forzado */
        width: auto !important;      /* Ancho automático - forzado */
        flex: 0 0 auto !important;   /* No se expanden - forzado */
    }

    /* Espaciado uniforme entre filas de inputs en responsive */
    .empresa-column .field-group,
    .empresa-column .field-row {
        margin-bottom: 18px;
    }
    .empresa-column .field-group:last-child,
    .empresa-column .field-row:last-child {
        margin-bottom: 0;
    }
}

/* ===== RESPONSIVE PARA MÓVILES PEQUEÑOS (≤480px) ===== */
@media (max-width: 480px) {
    /* ===== CONTENEDOR PRINCIPAL EN MÓVIL PEQUEÑO ===== */
    .empresa-container {
        max-width: calc(100% - 12px); /* Usa casi todo el ancho */
        padding: 10px;               /* Padding mínimo */
        margin: 12px 6px;            /* Margen mínimo */
    }

    /* ===== TÍTULO EN MÓVIL PEQUEÑO ===== */
    .empresa-external-title {
        font-size: 1.5rem;           /* Título más pequeño */
        margin: 12px auto 12px auto; /* Margen reducido */
        max-width: calc(100% - 12px); /* Mismo ancho que contenedor */
        padding: 0 6px;              /* Padding lateral */
    }

    /* ===== COLUMNAS EN MÓVIL PEQUEÑO ===== */
    .empresa-column {
        padding: 12px;               /* Padding mínimo */
    }

    .column-title {
        font-size: 1rem;             /* Título mínimo */
        margin-bottom: 10px;         /* Margen mínimo */
    }

    /* ===== CAMPOS EN MÓVIL PEQUEÑO ===== */
    .field-input,
    .field-textarea {
        padding: 6px;                /* Padding mínimo */
        font-size: 0.75rem;          /* Tamaño de fuente mínimo */
    }

    .field-textarea {
        min-height: 50px;            /* Altura mínima reducida */
    }

    /* ===== BOTONES EN MÓVIL PEQUEÑO ===== */
    .action-buttons {
        gap: 4px;                    /* Espacio mínimo entre botones */
    }

    .btn-save,
    .btn-cancel {
        padding: 5px 6px !important; /* Padding razonable - forzado */
        font-size: 0.7rem !important; /* Fuente legible - forzado */
        min-width: 40px !important;  /* Ancho mínimo razonable - forzado */
        max-width: 70px !important;  /* Ancho máximo razonable - forzado */
        width: auto !important;      /* Ancho automático - forzado */
        flex: 0 0 auto !important;   /* No se expanden - forzado */
    }
}

/* ===== RESPONSIVE PARA MÓVILES MUY PEQUEÑOS (≤360px) ===== */
@media (max-width: 360px) {
    /* ===== CONTENEDOR PRINCIPAL EN MÓVIL MUY PEQUEÑO ===== */
    .empresa-container {
        max-width: calc(100% - 8px);  /* Usa casi todo el ancho */
        padding: 8px;                /* Padding mínimo absoluto */
        margin: 10px 4px;            /* Margen mínimo absoluto */
    }

    /* ===== TÍTULO EN MÓVIL MUY PEQUEÑO ===== */
    .empresa-external-title {
        font-size: 1.3rem;           /* Título mínimo */
        margin: 10px auto 10px auto; /* Margen mínimo */
        max-width: calc(100% - 8px);  /* Mismo ancho que contenedor */
        padding: 0 4px;              /* Padding lateral */
    }

    .column-title {
        font-size: 0.9rem;           /* Título mínimo */
        margin-bottom: 8px;          /* Margen mínimo */
    }

    /* ===== BOTONES EN MÓVIL MUY PEQUEÑO ===== */
    .action-buttons {
        gap: 3px;                    /* Espacio mínimo entre botones */
    }

    .btn-save,
    .btn-cancel {
        padding: 4px 5px !important; /* Padding mínimo pero usable - forzado */
        font-size: 0.65rem !important; /* Fuente mínima pero legible - forzado */
        min-width: 35px !important;  /* Ancho mínimo usable - forzado */
        max-width: 60px !important;  /* Ancho máximo razonable - forzado */
        width: auto !important;      /* Ancho automático - forzado */
        flex: 0 0 auto !important;   /* No se expanden - forzado */
    }
}

/* ===== ESTILOS DE MENSAJES ===== */
.empresa-message {
    display: none;                  /* Oculto por defecto */
    padding: 10px 15px;             /* Espaciado interno */
    border-radius: 6px;             /* Bordes redondeados */
    margin-bottom: 15px;            /* Espacio debajo */
    font-size: 0.9rem;              /* Tamaño de fuente */
    font-weight: 500;               /* Peso medio */
    text-align: center;             /* Centrado */
}

.empresa-message.success {
    background: rgba(16, 185, 129, 0.1); /* Fondo verde claro */
    border: 1px solid #10b981;      /* Borde verde */
    color: #10b981;                 /* Texto verde */
}

.empresa-message.error {
    background: rgba(239, 68, 68, 0.1); /* Fondo rojo claro */
    border: 1px solid #ef4444;      /* Borde rojo */
    color: #ef4444;                 /* Texto rojo */
}

.empresa-message.info {
    background: rgba(107, 70, 193, 0.1); /* Fondo morado claro */
    border: 1px solid #6b46c1;      /* Borde morado */
    color: #6b46c1;                 /* Texto morado */
}

/* ===== FILA DE CAMPOS (TELÉFONO Y WHATSAPP) ===== */
.field-row {
    display: flex;
    gap: 24px;
    width: 100%;
}

@media (max-width: 768px) {
    .field-row {
        flex-direction: column;
        gap: 0;
    }
}
