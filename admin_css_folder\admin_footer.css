/* ===== FOOTER DESKTOP ===== */
.footer-desktop {
    background: linear-gradient(45deg, #6a1b9a 40%, #ffd54f 90%);
    color: white;
    padding: 28px 10px 18px 10px;
    text-align: center;
    box-shadow: 0 -2px 10px rgba(0,0,0,0.08);
    margin-top: 40px;
    width: 100%;
}
.footer-desktop-links {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 38px;
    flex-wrap: wrap;
    margin-bottom: 10px;
}
.footer-desktop-link {
    color: white;
    font-size: 1rem;
    font-weight: 500;
    letter-spacing: 0.01em;
    padding: 4px 10px;
    border-radius: 6px;
    background: none;
    transition: background 0.3s, color 0.3s;
    cursor: pointer;
}
.footer-desktop-link:hover {
    background: rgba(255,255,255,0.13);
    color: #fff;
}
.footer-desktop-divider {
    border: none;
    border-top: 1.5px solid #fff;
    margin: 10px auto 10px auto;
    width: 90%;
    max-width: 600px;
}
.footer-desktop-legal {
    margin-top: 6px;
}
.footer-desktop-copyright {
    font-family: 'Roboto', 'Arial', 'Helvetica Neue', sans-serif;
    font-weight: 400;
    font-size: 0.93rem;
    margin-bottom: 2px;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.12);
}
.footer-desktop-desc {
    font-family: 'Roboto', 'Arial', 'Helvetica Neue', sans-serif;
    font-weight: 400;
    font-size: 0.82rem;
    color: #fffde7;
    opacity: 0.92;
    margin-top: 2px;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.10);
}
@media (max-width: 1024px) {
    .footer-desktop { display: none; }
}

/* ===== FOOTER RESPONSIVE SOLO TEXTO, DOS FILAS ===== */
.footer-responsive-texts {
    background: linear-gradient(45deg, #6a1b9a 40%, #e6b84a 90%);
    color: white;
    padding: 18px 10px 10px 10px;
    text-align: center;
    box-shadow: 0 -2px 10px rgba(0,0,0,0.08);
    margin-top: 10px;
    width: 100%;
    border-radius: 0 0 12px 12px;
}
.footer-responsive-row {
    display: flex;
    justify-content: center;
    gap: 12px;
    margin-bottom: 4px;
    flex-wrap: wrap;
}
.footer-responsive-row-center {
    justify-content: center;
}
.footer-responsive-item {
    color: white;
    font-size: 0.85rem;
    font-weight: 500;
    padding: 1px 6px;
    border-radius: 4px;
    background: none;
    margin: 0 1px;
    letter-spacing: 0.01em;
    border: none;
    box-shadow: none;
    display: inline-block;
}
.footer-responsive-item:active,
.footer-responsive-item:focus,
.footer-responsive-item:hover {
    background: rgba(255,255,255,0.10);
    color: #fff;
}
.footer-responsive-divider {
    border: none;
    border-top: 1.5px solid #fff;
    margin: 10px auto 10px auto;
    width: 90%;
    max-width: 600px;
}
.footer-responsive-legal {
    margin-top: 6px;
}
.footer-responsive-copyright, .footer-responsive-desc {
    font-family: 'Roboto', 'Arial', 'Helvetica Neue', sans-serif;
    font-weight: 400;
    letter-spacing: 0.01em;
}
.footer-responsive-copyright {
    font-size: 0.70rem;
    margin-bottom: 2px;
    font-weight: 400;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.12);
}
.footer-responsive-desc {
    font-size: 0.68rem;
    color: #fffde7;
    opacity: 0.92;
    margin-top: 2px;
    font-weight: 400;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.10);
}
@media (min-width: 1025px) {
    .footer-responsive-texts { display: none; }
} 