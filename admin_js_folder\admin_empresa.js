/**
 * ===== ADMINISTRACIÓN DE DATOS DE LA EMPRESA =====
 * Maneja la funcionalidad del contenedor de datos de la empresa
 */

// Variables globales para datos de la empresa
let empresaData = {
    titulo: '',
    descripcion: '',
    sobreNosotros: '',
    direccion: '',
    telefono: '',
    whatsapp: '',
    ubicacion: ''
};

/**
 * Inicializa el módulo de datos de la empresa
 */
function initEmpresaModule() {
    console.log('🏢 Inicializando módulo de Datos de la Empresa...');
    
    // Inicializar contadores de caracteres
    initCharacterCounters();
    
    // Inicializar eventos de los campos
    initFieldEvents();
    
    // Inicializar botones
    initEmpresaButtons();
    
    // Cargar datos guardados
    loadEmpresaData();
    
    console.log('✅ Módulo de Datos de la Empresa inicializado correctamente');
}

/**
 * Inicializa los contadores de caracteres
 */
function initCharacterCounters() {
    const fieldsWithCounters = [
        { id: 'titulo-pagina', max: 60 },
        { id: 'descripcion-tienda', max: 25 }, // 25 palabras
        { id: 'sobre-nosotros', max: 50 }     // 50 palabras
    ];

    fieldsWithCounters.forEach(field => {
        const element = document.getElementById(field.id);
        if (element) {
            // Crear contador si no existe
            let counter = element.parentNode.querySelector('.char-counter');
            if (!counter) {
                counter = document.createElement('div');
                counter.className = 'char-counter';
                element.parentNode.appendChild(counter);
            }

            // Función para actualizar contador
            const updateCounter = () => {
                let count, max;
                
                if (field.id === 'titulo-pagina') {
                    // Contar caracteres
                    count = element.value.length;
                    max = field.max;
                    counter.textContent = `${count}/${max} caracteres`;
                } else {
                    // Contar palabras
                    const words = element.value.trim().split(/\s+/).filter(word => word.length > 0);
                    count = words.length;
                    max = field.max;
                    counter.textContent = `${count}/${max} palabras`;
                }

                // Cambiar color según el límite
                counter.className = 'char-counter';
                if (count > max * 0.8) {
                    counter.classList.add('warning');
                }
                if (count > max) {
                    counter.classList.add('error');
                }
            };

            // Eventos para actualizar contador
            element.addEventListener('input', updateCounter);
            element.addEventListener('paste', () => setTimeout(updateCounter, 10));
            
            // Actualizar contador inicial
            updateCounter();
        }
    });
}

/**
 * Inicializa los eventos de los campos
 */
function initFieldEvents() {
    const fields = [
        'titulo-pagina',
        'descripcion-tienda', 
        'sobre-nosotros',
        'direccion',
        'telefono',
        'whatsapp',
        'ubicacion'
    ];

    fields.forEach(fieldId => {
        const element = document.getElementById(fieldId);
        if (element) {
            element.addEventListener('input', function() {
                empresaData[fieldId.replace('-', '')] = this.value;
                showEmpresaMessage('Datos actualizados', 'info');
            });

            element.addEventListener('blur', function() {
                validateField(fieldId, this.value);
            });
        }
    });
}

/**
 * Valida un campo específico
 */
function validateField(fieldId, value) {
    const element = document.getElementById(fieldId);
    if (!element) return;

    let isValid = true;
    let message = '';

    switch (fieldId) {
        case 'titulo-pagina':
            if (value.length > 60) {
                isValid = false;
                message = 'El título no puede exceder 60 caracteres';
            }
            break;
        case 'descripcion-tienda':
            const words = value.trim().split(/\s+/).filter(word => word.length > 0);
            if (words.length > 25) {
                isValid = false;
                message = 'La descripción no puede exceder 25 palabras';
            }
            break;
        case 'sobre-nosotros':
            const aboutWords = value.trim().split(/\s+/).filter(word => word.length > 0);
            if (aboutWords.length > 50) {
                isValid = false;
                message = 'Sobre nosotros no puede exceder 50 palabras';
            }
            break;
        case 'telefono':
        case 'whatsapp':
            if (value && !/^[\d\s\-\+\(\)]+$/.test(value)) {
                isValid = false;
                message = 'Formato de teléfono inválido';
            }
            break;
        case 'ubicacion':
            if (value && !value.includes('maps.google.com') && !value.includes('goo.gl')) {
                isValid = false;
                message = 'Debe ser un enlace válido de Google Maps';
            }
            break;
    }

    // Mostrar mensaje si hay error
    if (!isValid) {
        showEmpresaMessage(message, 'error');
        element.style.borderColor = '#ef4444';
    } else {
        element.style.borderColor = '';
    }

    return isValid;
}

/**
 * Inicializa los botones del módulo
 */
function initEmpresaButtons() {
    const saveBtn = document.getElementById('empresa-save-btn');
    const cancelBtn = document.getElementById('empresa-cancel-btn');

    if (saveBtn) {
        saveBtn.addEventListener('click', saveEmpresaData);
    }

    if (cancelBtn) {
        cancelBtn.addEventListener('click', cancelEmpresaChanges);
    }
}

/**
 * Guarda los datos de la empresa
 */
function saveEmpresaData() {
    console.log('💾 Guardando datos de la empresa...');

    // Validar todos los campos
    const fields = ['titulo-pagina', 'descripcion-tienda', 'sobre-nosotros', 'direccion', 'telefono', 'whatsapp', 'ubicacion'];
    let allValid = true;

    fields.forEach(fieldId => {
        const element = document.getElementById(fieldId);
        if (element && !validateField(fieldId, element.value)) {
            allValid = false;
        }
    });

    if (!allValid) {
        showEmpresaMessage('Por favor corrige los errores antes de guardar', 'error');
        return;
    }

    // Simular guardado
    try {
        localStorage.setItem('empresaData', JSON.stringify(empresaData));
        showEmpresaMessage('Datos de la empresa guardados correctamente', 'success');
        console.log('✅ Datos guardados:', empresaData);
    } catch (error) {
        console.error('❌ Error al guardar:', error);
        showEmpresaMessage('Error al guardar los datos', 'error');
    }
}

/**
 * Cancela los cambios y restaura datos originales
 */
function cancelEmpresaChanges() {
    console.log('🔄 Cancelando cambios...');
    
    if (confirm('¿Estás seguro de que quieres cancelar los cambios?')) {
        loadEmpresaData();
        showEmpresaMessage('Cambios cancelados', 'info');
    }
}

/**
 * Carga los datos guardados de la empresa
 */
function loadEmpresaData() {
    try {
        const savedData = localStorage.getItem('empresaData');
        if (savedData) {
            empresaData = JSON.parse(savedData);
            
            // Llenar los campos con los datos guardados
            Object.keys(empresaData).forEach(key => {
                const fieldId = key.replace(/([A-Z])/g, '-$1').toLowerCase();
                const element = document.getElementById(fieldId);
                if (element) {
                    element.value = empresaData[key] || '';
                }
            });
            
            console.log('✅ Datos cargados:', empresaData);
        }
    } catch (error) {
        console.error('❌ Error al cargar datos:', error);
    }
}

/**
 * Muestra mensajes de estado
 */
function showEmpresaMessage(message, type = 'info') {
    // Crear o encontrar el contenedor de mensajes
    let messageContainer = document.querySelector('.empresa-message');
    if (!messageContainer) {
        messageContainer = document.createElement('div');
        messageContainer.className = 'empresa-message';
        const container = document.querySelector('.empresa-container');
        if (container) {
            container.insertBefore(messageContainer, container.firstChild);
        }
    }

    // Configurar el mensaje
    messageContainer.textContent = message;
    messageContainer.className = `empresa-message ${type}`;
    
    // Mostrar y ocultar después de 3 segundos
    messageContainer.style.display = 'block';
    setTimeout(() => {
        messageContainer.style.display = 'none';
    }, 3000);
}

// Inicializar cuando el DOM esté listo
document.addEventListener('DOMContentLoaded', function() {
    // Esperar un poco para asegurar que otros módulos se hayan cargado
    setTimeout(initEmpresaModule, 100);
});
