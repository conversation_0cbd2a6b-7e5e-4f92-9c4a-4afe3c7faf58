/* ===== ESTILOS DEL CONTENEDOR DE GESTIÓN DE PRODUCTOS ===== */
.gestion-productos-container {
    max-width: 1200px;           /* Ancho máximo igual al contenedor de estadísticas */
    margin: 30px auto;           /* Centra el contenedor con margen superior */
    padding: 20px;               /* Espaciado interno del contenedor */
    background-color: #2d3748;   /* Fondo azul oscuro igual que estadísticas */
}

/* ===== ESTILOS DEL HEADER DE GESTIÓN DE PRODUCTOS ===== */
.gestion-productos-header {
    margin-bottom: 30px;         /* Espacio debajo del header */
}

.gestion-productos-title {
    font-size: 2.5rem;           /* Tamaño grande para el título principal */
    font-weight: bold;           /* Texto en negrita */
    color: #5b21b6;              /* Color morado oscuro igual que estadísticas */
    margin-bottom: 8px;          /* Espacio pequeño debajo del título */
    text-align: left;            /* Alineación a la izquierda */
}

/* ===== ESTILOS DE LAS PESTAÑAS ===== */
.tabs-container {
    margin-bottom: 30px;         /* Espacio debajo de las pestañas */
}

.tabs-nav {
    display: flex;               /* Flexbox para las pestañas horizontalmente */
    gap: 5px;                    /* Espacio pequeño entre pestañas */
    border-bottom: 2px solid #1a202c; /* Línea debajo de las pestañas */
    margin-bottom: 20px;         /* Espacio debajo de la navegación */
}

.tab-button {
    background: #1a202c;         /* Fondo azul oscuro igual que KPIs */
    color: rgba(255,255,255,0.7); /* Color blanco semi-transparente */
    border: none;                /* Sin borde */
    padding: 12px 20px;          /* Espaciado interno de las pestañas */
    cursor: pointer;             /* Cursor de mano al pasar sobre la pestaña */
    border-radius: 8px 8px 0 0;  /* Bordes redondeados solo arriba */
    font-size: 0.9rem;           /* Tamaño de fuente de las pestañas */
    font-weight: 500;            /* Peso medio de la fuente */
    transition: all 0.3s ease;   /* Transición suave para efectos */
    border-left: 4px solid transparent; /* Borde izquierdo transparente por defecto */
}

.tab-button:hover {
    background: #2d3748;         /* Fondo más claro al pasar el mouse */
    color: #ffffff;              /* Color blanco completo al hover */
    transform: translateY(-2px); /* Eleva ligeramente la pestaña */
}

.tab-button.active {
    background: #2d3748;         /* Fondo más claro para pestaña activa */
    color: #ffffff;              /* Color blanco completo para pestaña activa */
    border-left-color: #6b46c1;  /* Borde morado para pestaña activa */
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3); /* Sombra para pestaña activa */
}

/* ===== ESTILOS DEL CONTENIDO DE LAS PESTAÑAS ===== */
.tab-content {
    display: none;               /* Oculta el contenido por defecto */
}

.tab-content.active {
    display: block;              /* Muestra el contenido de la pestaña activa */
}

/* ===== ESTILOS DEL CONTENEDOR DE PRODUCTOS ===== */
.productos-slider-container {
    position: relative;          /* Posición relativa para las flechas */
    overflow: hidden;            /* Oculta el contenido que se desborda */
}

.productos-slider {
    display: flex;               /* Flexbox para las tarjetas horizontalmente */
    gap: 15px;                   /* Espacio reducido entre tarjetas */
    transition: transform 0.3s ease; /* Transición suave para el deslizamiento */
    padding: 10px 0;             /* Padding vertical para sombras */
}

/* ===== ESTILOS DE LAS FLECHAS DE NAVEGACIÓN ===== */
.slider-arrow {
    position: absolute;          /* Posición absoluta para ubicar las flechas */
    top: 50%;                    /* Centra verticalmente */
    transform: translateY(-50%); /* Ajuste fino del centrado vertical */
    background: rgba(26, 32, 44, 0.9); /* Fondo azul oscuro semi-transparente */
    color: #ffffff;              /* Color blanco para las flechas */
    border: 3px solid rgba(255, 255, 255, 0.8); /* Borde blanco más visible */
    width: 50px;                 /* Ancho aumentado de las flechas */
    height: 50px;                /* Alto aumentado de las flechas */
    border-radius: 50%;          /* Flechas circulares */
    cursor: pointer;             /* Cursor de mano */
    font-size: 1.4rem;           /* Tamaño aumentado del icono de flecha */
    display: flex;               /* Flexbox para centrar el icono */
    align-items: center;         /* Centra verticalmente el icono */
    justify-content: center;     /* Centra horizontalmente el icono */
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.4); /* Sombra más pronunciada */
    transition: all 0.3s ease;   /* Transición suave para efectos */
    z-index: 100;                /* Z-index muy alto para estar por encima de todo */
    backdrop-filter: blur(5px);  /* Efecto de desenfoque del fondo */
}

.slider-arrow:hover {
    background: rgba(107, 70, 193, 0.95); /* Fondo morado semi-transparente al hover */
    color: #ffffff;              /* Color blanco de la flecha al hover */
    border-color: rgba(255, 255, 255, 1); /* Borde blanco completamente opaco al hover */
    transform: translateY(-50%) scale(1.15); /* Agranda más al hover */
    box-shadow: 0 8px 16px rgba(107, 70, 193, 0.4); /* Sombra morada al hover */
}

.slider-arrow.prev {
    left: 5px;                   /* Posición más hacia adentro de la flecha izquierda */
}

.slider-arrow.next {
    right: 5px;                  /* Posición más hacia adentro de la flecha derecha */
}

/* ===== ESTILOS DE LAS TARJETAS DE PRODUCTOS ===== */
.producto-card {
    background: #1a202c;         /* Fondo azul oscuro igual que KPIs */
    border-radius: 8px;          /* Bordes redondeados */
    padding: 18px;               /* Espaciado interno reducido de las tarjetas */
    min-width: 240px;            /* Ancho mínimo reducido de las tarjetas */
    max-width: 240px;            /* Ancho máximo para mantener consistencia */
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3); /* Sombra de las tarjetas */
    border-left: 4px solid #6b46c1; /* Borde morado igual que KPIs */
    transition: all 0.3s ease;   /* Transición suave para efectos */
    display: flex;               /* Flexbox para el contenido interno */
    flex-direction: column;      /* Organiza el contenido verticalmente */
}

.producto-card:hover {
    transform: translateY(-5px); /* Eleva la tarjeta al pasar el mouse */
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.4); /* Sombra más pronunciada */
}

/* ===== ESTILOS DE LA TARJETA PARA AGREGAR PRODUCTOS ===== */
.add-producto-card {
    border-left-color: #10b981;  /* Borde verde para diferenciarse */
    display: flex;               /* Flexbox para centrar el contenido */
    flex-direction: column;      /* Organiza contenido verticalmente */
    align-items: center;         /* Centra verticalmente */
    justify-content: center;     /* Centra horizontalmente */
    min-height: 300px;           /* Altura mínima igual a las tarjetas de productos */
    cursor: pointer;             /* Cursor de mano */
    text-align: center;          /* Centra el texto */
}

.add-producto-content {
    text-align: center;          /* Centra el contenido */
    color: rgba(255,255,255,0.7); /* Color blanco semi-transparente */
}

.add-producto-icon {
    font-size: 3rem;             /* Tamaño grande del icono de agregar */
    color: #10b981;              /* Color verde del icono */
    margin-bottom: 10px;         /* Espacio debajo del icono */
    line-height: 1;              /* Altura de línea compacta */
}

.add-producto-text {
    font-size: 1rem;             /* Tamaño del texto */
    font-weight: 500;            /* Peso medio de la fuente */
    color: rgba(255,255,255,0.8); /* Color blanco semi-transparente */
    line-height: 1.2;            /* Altura de línea */
}

/* ===== EFECTO HOVER PARA TARJETA DE AGREGAR ===== */
.add-producto-card:hover {
    transform: translateY(-3px);  /* Eleva ligeramente la tarjeta */
    box-shadow: 0 8px 16px rgba(16, 185, 129, 0.3); /* Sombra verde */
}

.add-producto-card:hover .add-producto-icon {
    color: #059669;              /* Color verde más oscuro al hover */
    transform: scale(1.1);       /* Agranda el icono al hover */
}

.add-producto-card:hover .add-producto-text {
    color: #ffffff;              /* Color blanco completo al hover */
}

/* ===== ESTILOS DEL CONTENIDO DE PRODUCTOS ===== */
.producto-image {
    width: 100%;                 /* Ancho completo de la tarjeta */
    height: 160px;               /* Altura reducida para tarjetas más angostas */
    background-color: #2d3748;   /* Fondo gris para imágenes placeholder */
    border-radius: 6px;          /* Bordes ligeramente redondeados */
    margin-bottom: 12px;         /* Espacio reducido debajo de la imagen */
    display: flex;               /* Flexbox para centrar el texto placeholder */
    align-items: center;         /* Centra verticalmente */
    justify-content: center;     /* Centra horizontalmente */
    color: rgba(255,255,255,0.5); /* Color del texto placeholder */
    font-size: 0.85rem;          /* Tamaño reducido del texto placeholder */
    background-size: cover;      /* Cubre todo el contenedor manteniendo proporción */
    background-position: center; /* Centra la imagen */
    background-repeat: no-repeat; /* No repite la imagen */
    overflow: hidden;            /* Oculta desbordamiento */
}

/* ===== ESTILOS ESPECÍFICOS PARA IMÁGENES CON CONTENIDO ===== */
.producto-image[data-has-image="true"] {
    background-size: cover !important;      /* Fuerza cover para imágenes reales */
    background-position: center !important; /* Fuerza centrado para imágenes reales */
    background-repeat: no-repeat !important; /* Evita repetición */
}

.producto-image[data-has-image="false"] {
    background-image: none !important;      /* Asegura que no hay imagen de fondo */
    display: flex;                          /* Flexbox para centrar texto */
    align-items: center;                    /* Centra verticalmente */
    justify-content: center;                /* Centra horizontalmente */
}

/* ===== ESTILOS ESPECÍFICOS PARA RESPONSIVE ===== */
@media (max-width: 768px) {
    .producto-image[data-has-image="true"] {
        background-attachment: scroll;       /* Mejora rendimiento en móviles */
        will-change: background-position;    /* Optimiza animaciones */
    }

    /* Optimización para imágenes horizontales en responsive */
    .producto-image[data-aspect-ratio="horizontal"] {
        background-position: center 30% !important;
    }

    /* Optimización para imágenes verticales en responsive */
    .producto-image[data-aspect-ratio="vertical"] {
        background-position: center 20% !important;
    }

    /* Optimización para imágenes cuadradas en responsive */
    .producto-image[data-aspect-ratio="square"] {
        background-position: center center !important;
    }
}

.producto-categoria {
    font-size: 0.7rem;           /* Tamaño más pequeño para categoría */
    color: rgba(255,255,255,0.6); /* Color blanco semi-transparente */
    margin-bottom: 6px;          /* Espacio reducido debajo de la categoría */
}

.producto-nombre {
    font-size: 0.9rem;           /* Tamaño reducido del nombre del producto */
    color: #ffffff;              /* Color blanco */
    font-weight: 600;            /* Peso semi-bold */
    margin-bottom: 8px;          /* Espacio reducido debajo del nombre */
    line-height: 1.3;            /* Altura de línea para mejor legibilidad */
}

.producto-precios {
    display: flex;               /* Flexbox para los precios horizontalmente */
    align-items: center;         /* Centra verticalmente los precios */
    gap: 8px;                    /* Espacio reducido entre precios */
    margin-bottom: 8px;          /* Espacio debajo de los precios */
}

.precio-actual {
    font-size: 1rem;             /* Tamaño reducido del precio actual */
    color: #10b981;              /* Color verde para el precio actual */
    font-weight: 700;            /* Peso bold */
}

.precio-anterior {
    font-size: 0.85rem;          /* Tamaño reducido para precio anterior */
    color: rgba(255,255,255,0.5); /* Color gris claro */
    text-decoration: line-through; /* Línea roja tachando el precio */
    text-decoration-color: #ef4444; /* Color rojo para la línea */
}

.producto-seccion {
    font-size: 0.75rem;          /* Tamaño pequeño para la sección */
    color: #6b46c1;              /* Color morado para la sección */
    font-weight: 500;            /* Peso medio */
    text-transform: capitalize;   /* Primera letra en mayúscula */
    margin-top: auto;            /* Empuja el elemento hacia abajo */
    padding-top: 8px;            /* Espacio superior para separar del contenido */
    border-top: 1px solid rgba(255,255,255,0.1); /* Línea sutil arriba del texto */
    display: flex;               /* Flexbox para organizar texto e iconos */
    justify-content: space-between; /* Separa texto a la izquierda e iconos a la derecha */
    align-items: center;         /* Centra verticalmente el contenido */
}

/* ===== TEXTO DE LA SECCIÓN ===== */
.seccion-texto {
    flex: 1;                     /* Ocupa el espacio disponible */
    text-align: left;            /* Alinea el texto a la izquierda */
}

/* ===== CONTENEDOR DE ICONOS DE ACCIÓN ===== */
.seccion-actions {
    display: flex;               /* Flexbox para los iconos */
    gap: 8px;                    /* Espacio entre iconos */
    align-items: center;         /* Centra verticalmente los iconos */
}

/* ===== ICONOS DE ACCIÓN ===== */
.action-icon {
    width: 16px;                 /* Ancho del icono */
    height: 16px;                /* Alto del icono */
    cursor: pointer;             /* Cursor de mano */
    transition: all 0.3s ease;   /* Transición suave */
    font-size: 0.75rem;          /* Tamaño de fuente para iconos */
    display: flex;               /* Flexbox para centrar el icono */
    align-items: center;         /* Centra verticalmente */
    justify-content: center;     /* Centra horizontalmente */
    border-radius: 3px;          /* Bordes ligeramente redondeados */
    padding: 2px;                /* Padding pequeño para área de click */
}

/* ===== ICONO DE EDITAR ===== */
.edit-icon {
    color: #3b82f6;              /* Color azul para editar */
    background: rgba(59, 130, 246, 0.1); /* Fondo azul semi-transparente */
}

.edit-icon:hover {
    color: #ffffff;              /* Color blanco al hover */
    background: #3b82f6;         /* Fondo azul sólido al hover */
    transform: scale(1.1);       /* Agranda ligeramente al hover */
}

/* ===== ICONO DE ELIMINAR ===== */
.delete-icon {
    color: #ef4444;              /* Color rojo para eliminar */
    background: rgba(239, 68, 68, 0.1); /* Fondo rojo semi-transparente */
}

.delete-icon:hover {
    color: #ffffff;              /* Color blanco al hover */
    background: #ef4444;         /* Fondo rojo sólido al hover */
    transform: scale(1.1);       /* Agranda ligeramente al hover */
}

/* ===== ASEGURAR VISIBILIDAD EN MODO NORMAL (DESKTOP) ===== */
@media (min-width: 1025px) {
    .producto-card .seccion-actions {
        display: flex !important;    /* Fuerza mostrar los iconos en desktop */
        gap: 8px !important;         /* Espacio entre iconos */
        align-items: center !important; /* Centra verticalmente */
        justify-content: flex-end !important; /* Alinea a la derecha */
    }

    .producto-card .edit-icon,
    .producto-card .delete-icon {
        display: flex !important;    /* Fuerza mostrar cada icono */
        opacity: 1 !important;       /* Asegura opacidad completa */
        visibility: visible !important; /* Asegura visibilidad */
        width: 20px !important;      /* Ancho fijo */
        height: 20px !important;     /* Alto fijo */
        font-size: 0.85rem !important; /* Tamaño de fuente */
    }

    .producto-card .producto-seccion {
        display: flex !important;    /* Asegura que la sección se muestre */
        justify-content: space-between !important; /* Espacio entre texto y acciones */
        align-items: center !important; /* Centra verticalmente */
    }
}

/* ===== RESPONSIVE PARA TABLETS (768px - 1024px) ===== */
@media (max-width: 1024px) and (min-width: 769px) {
    /* ===== CONTENEDOR PRINCIPAL EN TABLET ===== */
    .gestion-productos-container {
        max-width: calc(100% - 20px);  /* Usa casi todo el ancho con margen mínimo */
        padding: 0;                    /* Sin padding interno para maximizar espacio */
        margin: 20px 10px;             /* Margen pequeño con los laterales del HTML */
    }

    /* ===== PESTAÑAS EN TABLET - DOS FILAS DE 3 ===== */
    .tabs-nav {
        display: grid;                 /* Cambia a grid layout */
        grid-template-columns: repeat(3, 1fr); /* 3 columnas iguales */
        grid-template-rows: repeat(2, 1fr);    /* 2 filas iguales */
        gap: 8px;                      /* Espacio entre pestañas */
        justify-content: center;       /* Centra las pestañas */
        padding: 0 15px;               /* Padding lateral */
        margin-bottom: 20px;           /* Margen inferior */
        max-width: 600px;              /* Ancho máximo para mejor proporción */
        margin-left: auto;             /* Centra horizontalmente */
        margin-right: auto;            /* Centra horizontalmente */
    }

    .tab-button {
        font-size: 1.05rem;            /* Tamaño de fuente levemente más pequeño para tablets */
        padding: 10px 15px;            /* Padding uniforme */
        width: 100%;                   /* Ancho completo de la celda del grid */
        height: 50px;                  /* Altura un poco más alta para tablets */
        display: flex;                 /* Flexbox para centrar contenido */
        align-items: center;           /* Centra verticalmente */
        justify-content: center;       /* Centra horizontalmente */
        text-align: center;            /* Centra el texto */
        white-space: nowrap;           /* Evita salto de línea */
        overflow: hidden;              /* Oculta texto que se desborde */
        text-overflow: ellipsis;       /* Agrega puntos suspensivos si es necesario */
        border-radius: 4px;            /* Bordes más rectangulares para tablets */
    }

    /* ===== OCULTAR FLECHAS EN TABLET ===== */
    .slider-arrow {
        display: none;                /* Oculta las flechas de navegación */
    }

    /* ===== CONTENEDOR DE PRODUCTOS EN TABLET ===== */
    .productos-slider-container {
        overflow: visible;            /* Permite que el contenido sea visible */
        padding: 15px;                /* Padding interno del contenedor de productos */
    }

    .productos-slider {
        display: grid;                /* Cambia a grid layout */
        grid-template-columns: 1fr 1fr; /* 2 columnas iguales */
        gap: 15px;                    /* Espacio entre tarjetas */
        transform: none !important;   /* Elimina cualquier transformación del slider */
        padding: 0;                   /* Sin padding adicional */
    }

    /* ===== TARJETAS EN TABLET ===== */
    .producto-card {
        min-width: auto;              /* Permite que se ajuste al grid */
        max-width: none;              /* Elimina restricción de ancho máximo */
        width: 100%;                  /* Ocupa todo el ancho de la columna */
        padding: 0;                   /* Sin padding para que la imagen ocupe todo */
        min-height: 280px;            /* Altura mínima más alta para las tarjetas */
        overflow: hidden;             /* Oculta cualquier desbordamiento */
    }

    /* ===== TARJETA AGREGAR PRODUCTO EN TABLET ===== */
    .add-producto-card {
        min-height: 280px;            /* Misma altura que las tarjetas normales */
        display: flex;                /* Mantiene flexbox */
        align-items: center;          /* Centra contenido verticalmente */
        justify-content: center;      /* Centra contenido horizontalmente */
        padding: 15px;                /* Mantiene padding para el contenido de agregar */
    }

    /* ===== IMAGEN OCUPA TODA LA TARJETA EN TABLET ===== */
    .producto-card:not(.add-producto-card) .producto-image {
        width: 100%;                  /* Ancho completo de la tarjeta */
        height: 100%;                 /* Altura completa de la tarjeta */
        margin: 0;                    /* Sin márgenes */
        border-radius: 8px;           /* Bordes redondeados igual que la tarjeta */
        background-size: cover;       /* Cubre todo el contenedor manteniendo proporción */
        background-position: center;  /* Centra la imagen */
        background-repeat: no-repeat; /* No repite la imagen */
        object-fit: cover;            /* Fallback para elementos img */
    }

    /* ===== OCULTAR TODA LA INFORMACIÓN DEL PRODUCTO EN TABLET ===== */
    .producto-card:not(.add-producto-card) .producto-categoria,
    .producto-card:not(.add-producto-card) .producto-nombre,
    .producto-card:not(.add-producto-card) .producto-precios,
    .producto-card:not(.add-producto-card) .producto-seccion,
    .producto-card:not(.add-producto-card) .seccion-actions {
        display: none;                /* Oculta toda la información del producto */
    }
}

/* ===== RESPONSIVE PARA MÓVILES (≤768px) ===== */
@media (max-width: 768px) {
    /* ===== OCULTAR CONTAINERS POR DEFECTO EN RESPONSIVE ===== */
    .gestion-productos-container.responsive-hidden {
        display: none;               /* Oculto por defecto en responsive */
    }

    /* ===== CONTENEDOR PRINCIPAL EN MÓVIL ===== */
    .gestion-productos-container {
        max-width: calc(100% - 16px);  /* Usa casi todo el ancho con margen mínimo */
        padding: 0;                    /* Sin padding interno para maximizar espacio */
        margin: 15px 8px;              /* Margen pequeño con los laterales del HTML */
    }

    /* ===== HEADER EN MÓVIL ===== */
    .gestion-productos-title {
        font-size: 1.4rem;            /* Tamaño del título */
        text-align: center;           /* Centra el título */
        margin-bottom: 12px;          /* Margen inferior */
        padding: 0 12px;              /* Padding lateral para el título */
    }

    /* ===== PESTAÑAS EN MÓVIL - DOS FILAS DE 3 ===== */
    .tabs-nav {
        display: grid;                 /* Cambia a grid layout */
        grid-template-columns: repeat(3, 1fr); /* 3 columnas iguales */
        grid-template-rows: repeat(2, 1fr);    /* 2 filas iguales */
        gap: 5px;                      /* Espacio entre pestañas */
        justify-content: center;       /* Centra las pestañas */
        padding: 0 12px;               /* Padding lateral para las pestañas */
        margin-bottom: 15px;           /* Margen inferior */
        max-width: 100%;               /* Usa todo el ancho disponible */
    }

    .tab-button {
        font-size: 0.9rem;             /* Tamaño de fuente levemente más pequeño para móviles */
        padding: 8px 10px;             /* Padding uniforme */
        width: 100%;                   /* Ancho completo de la celda del grid */
        height: 42px;                  /* Altura un poco más alta para móviles */
        display: flex;                 /* Flexbox para centrar contenido */
        align-items: center;           /* Centra verticalmente */
        justify-content: center;       /* Centra horizontalmente */
        text-align: center;            /* Centra el texto */
        white-space: nowrap;           /* Evita salto de línea */
        overflow: hidden;              /* Oculta texto que se desborde */
        text-overflow: ellipsis;       /* Agrega puntos suspensivos si es necesario */
        line-height: 1.1;              /* Altura de línea compacta */
        border-radius: 4px;            /* Bordes más rectangulares para móviles */
    }

    /* ===== OCULTAR FLECHAS EN MÓVIL ===== */
    .slider-arrow {
        display: none;                /* Oculta las flechas de navegación */
    }

    /* ===== CONTENEDOR DE PRODUCTOS EN MÓVIL ===== */
    .productos-slider-container {
        overflow: visible;            /* Permite que el contenido sea visible */
        padding: 12px;                /* Padding interno del contenedor de productos */
    }

    .productos-slider {
        display: grid;                /* Cambia a grid layout */
        grid-template-columns: 1fr 1fr; /* 2 columnas iguales */
        gap: 10px;                    /* Espacio entre tarjetas */
        transform: none !important;   /* Elimina cualquier transformación del slider */
        padding: 0;                   /* Sin padding adicional */
    }

    /* ===== TARJETAS EN MÓVIL ===== */
    .producto-card {
        min-width: auto;              /* Permite que se ajuste al grid */
        max-width: none;              /* Elimina restricción de ancho máximo */
        width: 100%;                  /* Ocupa todo el ancho de la columna */
        padding: 0;                   /* Sin padding para que la imagen ocupe todo */
        min-height: 240px;            /* Altura mínima más alta para móvil */
        overflow: hidden;             /* Oculta cualquier desbordamiento */
    }

    /* ===== TARJETA AGREGAR PRODUCTO EN MÓVIL ===== */
    .add-producto-card {
        min-height: 240px;            /* Misma altura que las tarjetas normales */
        display: flex;                /* Mantiene flexbox */
        align-items: center;          /* Centra contenido verticalmente */
        justify-content: center;      /* Centra contenido horizontalmente */
        padding: 12px;                /* Mantiene padding para el contenido de agregar */
    }

    .add-producto-icon {
        font-size: 1.8rem;            /* Tamaño del icono */
        margin-bottom: 8px;           /* Margen inferior */
    }

    .add-producto-text {
        font-size: 0.75rem;           /* Tamaño del texto */
    }

    /* ===== IMAGEN OCUPA TODA LA TARJETA EN MÓVIL ===== */
    .producto-card:not(.add-producto-card) .producto-image {
        width: 100%;                  /* Ancho completo de la tarjeta */
        height: 240px;                /* Altura fija igual a la tarjeta */
        margin: 0;                    /* Sin márgenes */
        border-radius: 8px;           /* Bordes redondeados igual que la tarjeta */
        background-size: cover;       /* Cubre todo el contenedor manteniendo proporción */
        background-position: center center; /* Centra la imagen perfectamente */
        background-repeat: no-repeat; /* No repite la imagen */
        object-fit: cover;            /* Fallback para elementos img */
        display: flex;                /* Flexbox para centrar placeholder */
        align-items: center;          /* Centra placeholder verticalmente */
        justify-content: center;      /* Centra placeholder horizontalmente */
        font-size: 0.8rem;            /* Tamaño de texto placeholder */
        color: rgba(255,255,255,0.4); /* Color placeholder más sutil */
    }

    /* ===== OCULTAR TODA LA INFORMACIÓN DEL PRODUCTO EN MÓVIL ===== */
    .producto-card:not(.add-producto-card) .producto-categoria,
    .producto-card:not(.add-producto-card) .producto-nombre,
    .producto-card:not(.add-producto-card) .producto-precios,
    .producto-card:not(.add-producto-card) .producto-seccion,
    .producto-card:not(.add-producto-card) .seccion-actions {
        display: none;                /* Oculta toda la información del producto */
    }
}

/* ===== RESPONSIVE PARA MÓVILES PEQUEÑOS (≤480px) ===== */
@media (max-width: 480px) {
    /* ===== CONTENEDOR PRINCIPAL EN MÓVIL PEQUEÑO ===== */
    .gestion-productos-container {
        max-width: calc(100% - 12px);  /* Usa casi todo el ancho con margen mínimo */
        padding: 0;                    /* Sin padding interno para maximizar espacio */
        margin: 12px 6px;              /* Margen pequeño con los laterales del HTML */
    }

    /* ===== HEADER EN MÓVIL PEQUEÑO ===== */
    .gestion-productos-title {
        font-size: 1.2rem;            /* Título más pequeño */
        margin-bottom: 10px;          /* Margen */
        padding: 0 10px;              /* Padding lateral */
    }

    /* ===== PESTAÑAS EN MÓVIL PEQUEÑO - DOS FILAS DE 3 ===== */
    .tabs-nav {
        display: grid;                 /* Cambia a grid layout */
        grid-template-columns: repeat(3, 1fr); /* 3 columnas iguales */
        grid-template-rows: repeat(2, 1fr);    /* 2 filas iguales */
        gap: 4px;                      /* Espacio entre pestañas */
        justify-content: center;       /* Centra las pestañas */
        padding: 0 10px;               /* Padding lateral para pestañas */
        margin-bottom: 12px;           /* Margen inferior */
        max-width: 100%;               /* Usa todo el ancho disponible */
    }

    .tab-button {
        font-size: 0.8rem;             /* Tamaño de fuente levemente más pequeño para móviles pequeños */
        padding: 6px 8px;              /* Padding reducido uniforme */
        width: 100%;                   /* Ancho completo de la celda del grid */
        height: 36px;                  /* Altura un poco más alta para móviles pequeños */
        display: flex;                 /* Flexbox para centrar contenido */
        align-items: center;           /* Centra verticalmente */
        justify-content: center;       /* Centra horizontalmente */
        text-align: center;            /* Centra el texto */
        white-space: nowrap;           /* Evita salto de línea */
        overflow: hidden;              /* Oculta texto que se desborde */
        text-overflow: ellipsis;       /* Agrega puntos suspensivos si es necesario */
        line-height: 1;                /* Altura de línea compacta */
        border-radius: 3px;            /* Bordes más rectangulares para móviles pequeños */
    }

    /* ===== CONTENEDOR DE PRODUCTOS EN MÓVIL PEQUEÑO ===== */
    .productos-slider-container {
        padding: 10px;                /* Padding interno */
    }

    .productos-slider {
        gap: 8px;                     /* Espacio entre tarjetas */
    }

    /* ===== TARJETAS EN MÓVIL PEQUEÑO ===== */
    .producto-card {
        padding: 0;                   /* Sin padding para que la imagen ocupe todo */
        border-radius: 6px;           /* Bordes redondeados */
        min-height: 200px;            /* Altura mínima más alta */
        overflow: hidden;             /* Oculta cualquier desbordamiento */
    }

    /* ===== TARJETA AGREGAR EN MÓVIL PEQUEÑO ===== */
    .add-producto-card {
        min-height: 200px;            /* Misma altura que las tarjetas normales */
        padding: 10px;                /* Mantiene padding para el contenido de agregar */
    }

    .add-producto-icon {
        font-size: 1.5rem;            /* Icono */
        margin-bottom: 6px;           /* Margen */
    }

    .add-producto-text {
        font-size: 0.65rem;           /* Texto */
    }

    /* ===== IMAGEN OCUPA TODA LA TARJETA EN MÓVIL PEQUEÑO ===== */
    .producto-card:not(.add-producto-card) .producto-image {
        width: 100%;                  /* Ancho completo de la tarjeta */
        height: 200px;                /* Altura fija igual a la tarjeta */
        margin: 0;                    /* Sin márgenes */
        border-radius: 6px;           /* Bordes redondeados igual que la tarjeta */
        background-size: cover;       /* Cubre todo el contenedor manteniendo proporción */
        background-position: center center; /* Centra la imagen perfectamente */
        background-repeat: no-repeat; /* No repite la imagen */
        object-fit: cover;            /* Fallback para elementos img */
        display: flex;                /* Flexbox para centrar placeholder */
        align-items: center;          /* Centra placeholder verticalmente */
        justify-content: center;      /* Centra placeholder horizontalmente */
        font-size: 0.75rem;           /* Tamaño de texto placeholder */
        color: rgba(255,255,255,0.4); /* Color placeholder más sutil */
    }

    /* ===== OCULTAR TODA LA INFORMACIÓN DEL PRODUCTO EN MÓVIL PEQUEÑO ===== */
    .producto-card:not(.add-producto-card) .producto-categoria,
    .producto-card:not(.add-producto-card) .producto-nombre,
    .producto-card:not(.add-producto-card) .producto-precios,
    .producto-card:not(.add-producto-card) .producto-seccion,
    .producto-card:not(.add-producto-card) .seccion-actions {
        display: none;                /* Oculta toda la información del producto */
    }
}

/* ===== RESPONSIVE PARA MÓVILES MUY PEQUEÑOS (≤360px) ===== */
@media (max-width: 360px) {
    /* ===== CONTENEDOR PRINCIPAL EN MÓVIL MUY PEQUEÑO ===== */
    .gestion-productos-container {
        max-width: calc(100% - 8px);   /* Usa casi todo el ancho con margen mínimo */
        padding: 0;                    /* Sin padding interno para maximizar espacio */
        margin: 10px 4px;              /* Margen muy pequeño con los laterales del HTML */
    }

    /* ===== HEADER EN MÓVIL MUY PEQUEÑO ===== */
    .gestion-productos-title {
        font-size: 1rem;              /* Título mínimo */
        margin-bottom: 8px;           /* Margen mínimo */
        padding: 0 8px;               /* Padding lateral */
    }

    /* ===== PESTAÑAS EN MÓVIL MUY PEQUEÑO - DOS FILAS DE 3 ===== */
    .tabs-nav {
        display: grid;                 /* Cambia a grid layout */
        grid-template-columns: repeat(3, 1fr); /* 3 columnas iguales */
        grid-template-rows: repeat(2, 1fr);    /* 2 filas iguales */
        gap: 3px;                      /* Espacio mínimo entre pestañas */
        justify-content: center;       /* Centra las pestañas */
        padding: 0 8px;                /* Padding lateral para pestañas */
        margin-bottom: 10px;           /* Margen inferior */
        max-width: 100%;               /* Usa todo el ancho disponible */
    }

    .tab-button {
        font-size: 0.7rem;             /* Tamaño de fuente levemente más pequeño para móviles muy pequeños */
        padding: 4px 6px;              /* Padding mínimo uniforme */
        width: 100%;                   /* Ancho completo de la celda del grid */
        height: 32px;                  /* Altura un poco más alta para móviles muy pequeños */
        display: flex;                 /* Flexbox para centrar contenido */
        align-items: center;           /* Centra verticalmente */
        justify-content: center;       /* Centra horizontalmente */
        text-align: center;            /* Centra el texto */
        white-space: nowrap;           /* Evita salto de línea */
        overflow: hidden;              /* Oculta texto que se desborde */
        text-overflow: ellipsis;       /* Agrega puntos suspensivos si es necesario */
        line-height: 1;                /* Altura de línea compacta */
        border-radius: 3px;            /* Bordes más rectangulares para móviles muy pequeños */
    }

    /* ===== CONTENEDOR DE PRODUCTOS EN MÓVIL MUY PEQUEÑO ===== */
    .productos-slider-container {
        padding: 8px;                 /* Padding interno mínimo */
    }

    .productos-slider {
        gap: 6px;                     /* Espacio mínimo entre tarjetas */
    }

    /* ===== TARJETAS EN MÓVIL MUY PEQUEÑO ===== */
    .producto-card {
        padding: 0;                   /* Sin padding para que la imagen ocupe todo */
        min-height: 180px;            /* Altura mínima más alta */
        overflow: hidden;             /* Oculta cualquier desbordamiento */
    }

    /* ===== TARJETA AGREGAR EN MÓVIL MUY PEQUEÑO ===== */
    .add-producto-card {
        min-height: 180px;            /* Misma altura que las tarjetas normales */
        padding: 8px;                 /* Mantiene padding para el contenido de agregar */
    }

    .add-producto-icon {
        font-size: 1.2rem;            /* Icono mínimo */
        margin-bottom: 5px;           /* Margen mínimo */
    }

    .add-producto-text {
        font-size: 0.55rem;           /* Texto mínimo */
    }

    /* ===== IMAGEN OCUPA TODA LA TARJETA EN MÓVIL MUY PEQUEÑO ===== */
    .producto-card:not(.add-producto-card) .producto-image {
        width: 100%;                  /* Ancho completo de la tarjeta */
        height: 180px;                /* Altura fija igual a la tarjeta */
        margin: 0;                    /* Sin márgenes */
        border-radius: 8px;           /* Bordes redondeados igual que la tarjeta */
        background-size: cover;       /* Cubre todo el contenedor manteniendo proporción */
        background-position: center center; /* Centra la imagen perfectamente */
        background-repeat: no-repeat; /* No repite la imagen */
        object-fit: cover;            /* Fallback para elementos img */
        display: flex;                /* Flexbox para centrar placeholder */
        align-items: center;          /* Centra placeholder verticalmente */
        justify-content: center;      /* Centra placeholder horizontalmente */
        font-size: 0.7rem;            /* Tamaño de texto placeholder */
        color: rgba(255,255,255,0.4); /* Color placeholder más sutil */
    }

    /* ===== OCULTAR TODA LA INFORMACIÓN DEL PRODUCTO EN MÓVIL MUY PEQUEÑO ===== */
    .producto-card:not(.add-producto-card) .producto-categoria,
    .producto-card:not(.add-producto-card) .producto-nombre,
    .producto-card:not(.add-producto-card) .producto-precios,
    .producto-card:not(.add-producto-card) .producto-seccion,
    .producto-card:not(.add-producto-card) .seccion-actions {
        display: none;                /* Oculta toda la información del producto */
    }
}

/* ===== ESTILOS DEL POP-UP DE PRODUCTO (SOLO RESPONSIVE) ===== */
.producto-popup-overlay {
    position: fixed;                  /* Posición fija para cubrir toda la pantalla */
    top: 0;                          /* Desde arriba */
    left: 0;                         /* Desde la izquierda */
    width: 100%;                     /* Ancho completo */
    height: 100%;                    /* Alto completo */
    background: rgba(0, 0, 0, 0.8);  /* Fondo negro semi-transparente */
    display: none;                   /* Oculto por defecto */
    justify-content: center;         /* Centra horizontalmente */
    align-items: center;             /* Centra verticalmente */
    z-index: 1000;                   /* Z-index alto para estar encima de todo */
    backdrop-filter: blur(5px);      /* Efecto de desenfoque del fondo */
    animation: fadeIn 0.3s ease;     /* Animación de entrada */
}

.producto-popup-overlay.active {
    display: flex;                   /* Muestra el pop-up cuando está activo */
}

/* ===== OCULTAR POP-UP EN DESKTOP (>1024px) ===== */
@media (min-width: 1025px) {
    .producto-popup-overlay {
        display: none !important;    /* Nunca se muestra en desktop */
    }

    .producto-popup-overlay.active {
        display: none !important;    /* Fuerza que no se muestre en desktop */
    }
}

/* ===== CONTENEDOR DEL POP-UP ===== */
.producto-popup-container {
    position: relative;              /* Posición relativa para el botón de cerrar */
    background: #1a202c;            /* Fondo azul oscuro igual que las tarjetas */
    border-radius: 8px;              /* Bordes redondeados igual que tarjetas normales */
    padding: 0;                      /* Sin padding inicial */
    max-width: 240px;                /* Ancho máximo igual a tarjetas normales */
    width: 240px;                    /* Ancho fijo igual a tarjetas normales */
    max-height: 95vh;                /* Altura máxima aumentada */
    overflow: hidden;                /* Oculta desbordamiento */
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3); /* Sombra igual que tarjetas normales */
    border-left: 4px solid #6b46c1; /* Borde morado igual que las tarjetas */
    animation: slideIn 0.3s ease;    /* Animación de entrada */
}

/* ===== BOTÓN DE CERRAR ===== */
.popup-close-btn {
    position: absolute;              /* Posición absoluta */
    top: 15px;                       /* Desde arriba */
    right: 15px;                     /* Desde la derecha */
    background: rgba(255, 255, 255, 0.1); /* Fondo semi-transparente */
    color: #ffffff;                  /* Color blanco */
    border: 2px solid rgba(255, 255, 255, 0.3); /* Borde semi-transparente */
    width: 35px;                     /* Ancho del botón */
    height: 35px;                    /* Alto del botón */
    border-radius: 50%;              /* Botón circular */
    font-size: 1.5rem;               /* Tamaño del icono */
    font-weight: bold;               /* Texto en negrita */
    cursor: pointer;                 /* Cursor de mano */
    display: flex;                   /* Flexbox para centrar */
    align-items: center;             /* Centra verticalmente */
    justify-content: center;         /* Centra horizontalmente */
    transition: all 0.3s ease;       /* Transición suave */
    z-index: 1001;                   /* Z-index alto */
}

.popup-close-btn:hover {
    background: rgba(239, 68, 68, 0.8); /* Fondo rojo al hover */
    border-color: rgba(239, 68, 68, 1); /* Borde rojo al hover */
    transform: scale(1.1);           /* Agranda al hover */
}

/* ===== TARJETA DEL PRODUCTO EN POP-UP ===== */
.popup-producto-card {
    display: flex;                   /* Flexbox para organizar contenido */
    flex-direction: column;          /* Organiza verticalmente */
    padding: 18px;                   /* Padding interno igual que tarjetas normales */
    height: 100%;                    /* Altura completa */
    min-height: auto;                /* Altura mínima automática */
}

/* ===== IMAGEN EN POP-UP ===== */
.popup-producto-image {
    width: 100%;                     /* Ancho completo */
    height: 160px;                   /* Altura igual que tarjetas normales */
    background-color: #2d3748;       /* Fondo gris para placeholder */
    border-radius: 6px;              /* Bordes redondeados igual que tarjetas normales */
    margin-bottom: 12px;             /* Espacio debajo igual que tarjetas normales */
    display: flex;                   /* Flexbox para centrar texto */
    align-items: center;             /* Centra verticalmente */
    justify-content: center;         /* Centra horizontalmente */
    color: rgba(255,255,255,0.5);    /* Color del texto placeholder */
    font-size: 0.85rem;              /* Tamaño del texto igual que tarjetas normales */
    object-fit: cover;               /* Ajuste de imagen */
}

/* ===== CATEGORÍA EN POP-UP ===== */
.popup-producto-categoria {
    font-size: 0.7rem;               /* Tamaño igual que tarjetas normales */
    color: rgba(255,255,255,0.6);    /* Color semi-transparente */
    margin-bottom: 6px;              /* Espacio debajo igual que tarjetas normales */
}

/* ===== NOMBRE EN POP-UP ===== */
.popup-producto-nombre {
    font-size: 0.9rem;               /* Tamaño igual que tarjetas normales */
    color: #ffffff;                  /* Color blanco */
    font-weight: 600;                /* Peso semi-bold */
    margin-bottom: 8px;              /* Espacio debajo igual que tarjetas normales */
    line-height: 1.3;                /* Altura de línea igual que tarjetas normales */
}

/* ===== PRECIOS EN POP-UP ===== */
.popup-producto-precios {
    display: flex;                   /* Flexbox para precios */
    align-items: center;             /* Centra verticalmente */
    gap: 8px;                        /* Espacio entre precios igual que tarjetas normales */
    margin-bottom: 8px;              /* Espacio debajo igual que tarjetas normales */
}

.popup-precio-actual {
    font-size: 1rem;                 /* Tamaño igual que tarjetas normales */
    color: #10b981;                  /* Color verde */
    font-weight: 700;                /* Peso bold */
}

.popup-precio-anterior {
    font-size: 0.85rem;              /* Tamaño igual que tarjetas normales */
    color: rgba(255,255,255,0.5);    /* Color gris */
    text-decoration: line-through;    /* Línea tachada */
    text-decoration-color: #ef4444;  /* Color rojo para la línea */
}

/* ===== SECCIÓN EN POP-UP ===== */
.popup-producto-seccion {
    font-size: 0.75rem;              /* Tamaño igual que tarjetas normales */
    color: #6b46c1;                  /* Color morado */
    font-weight: 500;                /* Peso medio */
    text-transform: capitalize;       /* Primera letra mayúscula */
    text-align: center;              /* Centra el texto */
    margin-top: auto;                /* Empuja el elemento hacia abajo igual que tarjetas normales */
    padding-top: 8px;                /* Espacio superior igual que tarjetas normales */
    border-top: 1px solid rgba(255,255,255,0.1); /* Línea superior igual que tarjetas normales */
}

/* ===== BOTONES DE ACCIÓN ===== */
.popup-actions {
    display: flex;                   /* Flexbox para botones */
    gap: 8px;                        /* Espacio entre botones */
    margin-top: 8px;                 /* Margen superior pequeño */
}

.popup-btn {
    flex: 1;                         /* Ocupan espacio igual */
    padding: 8px 12px;               /* Padding interno más pequeño */
    border: none;                    /* Sin borde */
    border-radius: 4px;              /* Bordes redondeados más pequeños */
    font-size: 0.75rem;              /* Tamaño de fuente más pequeño */
    font-weight: 500;                /* Peso medio */
    cursor: pointer;                 /* Cursor de mano */
    transition: all 0.3s ease;       /* Transición suave */
}

.popup-btn-edit {
    background: #3b82f6;             /* Fondo azul */
    color: white;                    /* Texto blanco */
}

.popup-btn-edit:hover {
    background: #2563eb;             /* Azul más oscuro al hover */
    transform: translateY(-2px);     /* Eleva al hover */
}

.popup-btn-delete {
    background: #ef4444;             /* Fondo rojo */
    color: white;                    /* Texto blanco */
}

.popup-btn-delete:hover {
    background: #dc2626;             /* Rojo más oscuro al hover */
    transform: translateY(-2px);     /* Eleva al hover */
}

/* ===== ANIMACIONES ===== */
@keyframes fadeIn {
    from {
        opacity: 0;                  /* Inicia transparente */
    }
    to {
        opacity: 1;                  /* Termina opaco */
    }
}

@keyframes slideIn {
    from {
        transform: scale(0.8) translateY(-50px); /* Inicia pequeño y arriba */
        opacity: 0;                  /* Inicia transparente */
    }
    to {
        transform: scale(1) translateY(0); /* Termina normal */
        opacity: 1;                  /* Termina opaco */
    }
}

/* ===== RESPONSIVE PARA TABLETS (768px - 1024px) ===== */
@media (max-width: 1024px) and (min-width: 769px) {
    .producto-popup-container {
        max-width: 240px;            /* Mantiene ancho igual a tarjetas normales */
        width: 240px;                /* Ancho fijo igual a tarjetas normales */
        max-height: 90vh;            /* Altura máxima en tablet */
    }

    .popup-producto-card {
        min-height: auto;            /* Altura automática */
    }

    .popup-producto-image {
        height: 160px;               /* Imagen igual que tarjetas normales */
    }

    .popup-producto-nombre {
        font-size: 0.9rem;           /* Nombre igual que tarjetas normales */
    }

    .popup-precio-actual {
        font-size: 1rem;             /* Precio igual que tarjetas normales */
    }
}

/* ===== RESPONSIVE PARA MÓVILES (≤768px) ===== */
@media (max-width: 768px) {
    .producto-popup-container {
        max-width: 240px;            /* Mantiene ancho igual a tarjetas normales */
        width: 240px;                /* Ancho fijo igual a tarjetas normales */
        max-height: 90vh;            /* Altura máxima aumentada */
    }

    .popup-producto-card {
        padding: 18px;               /* Padding igual que tarjetas normales */
        min-height: auto;            /* Altura automática */
    }

    .popup-close-btn {
        width: 30px;                 /* Botón más pequeño */
        height: 30px;                /* Botón más pequeño */
        font-size: 1.2rem;           /* Icono más pequeño */
        top: 12px;                   /* Posición ajustada */
        right: 12px;                 /* Posición ajustada */
    }

    .popup-producto-image {
        height: 160px;               /* Imagen igual que tarjetas normales */
    }

    .popup-producto-nombre {
        font-size: 0.9rem;           /* Nombre igual que tarjetas normales */
    }

    .popup-precio-actual {
        font-size: 1rem;             /* Precio igual que tarjetas normales */
    }

    .popup-btn {
        padding: 6px 10px;           /* Padding más pequeño en móvil */
        font-size: 0.7rem;           /* Fuente más pequeña en móvil */
    }
}

/* ===== RESPONSIVE PARA MÓVILES PEQUEÑOS (≤480px) ===== */
@media (max-width: 480px) {
    .producto-popup-container {
        max-width: 220px;            /* Ancho ligeramente más pequeño */
        width: 220px;                /* Ancho fijo más pequeño */
        max-height: 85vh;            /* Altura máxima reducida */
    }

    .popup-close-btn {
        width: 28px;                 /* Botón más pequeño */
        height: 28px;                /* Botón más pequeño */
        font-size: 1rem;             /* Icono más pequeño */
        top: 10px;                   /* Posición ajustada */
        right: 10px;                 /* Posición ajustada */
    }

    .popup-producto-card {
        padding: 15px;               /* Padding reducido */
    }

    .popup-producto-image {
        height: 140px;               /* Imagen más pequeña */
    }

    .popup-producto-nombre {
        font-size: 0.85rem;          /* Nombre más pequeño */
    }

    .popup-precio-actual {
        font-size: 0.9rem;           /* Precio más pequeño */
    }

    .popup-btn {
        padding: 5px 8px;            /* Padding más pequeño */
        font-size: 0.65rem;          /* Fuente más pequeña */
    }
}

/* ===== RESPONSIVE PARA MÓVILES MUY PEQUEÑOS (≤360px) ===== */
@media (max-width: 360px) {
    .producto-popup-container {
        max-width: 200px;            /* Ancho más pequeño */
        width: 200px;                /* Ancho fijo más pequeño */
        max-height: 80vh;            /* Altura máxima reducida */
    }

    .popup-close-btn {
        width: 25px;                 /* Botón más pequeño */
        height: 25px;                /* Botón más pequeño */
        font-size: 0.9rem;           /* Icono más pequeño */
        top: 8px;                    /* Posición ajustada */
        right: 8px;                  /* Posición ajustada */
    }

    .popup-producto-card {
        padding: 12px;               /* Padding mínimo */
    }

    .popup-producto-image {
        height: 120px;               /* Imagen más pequeña */
    }

    .popup-producto-nombre {
        font-size: 0.8rem;           /* Nombre más pequeño */
    }

    .popup-precio-actual {
        font-size: 0.85rem;          /* Precio más pequeño */
    }

    .popup-btn {
        padding: 4px 6px;            /* Padding mínimo */
        font-size: 0.6rem;           /* Fuente mínima */
    }
}
