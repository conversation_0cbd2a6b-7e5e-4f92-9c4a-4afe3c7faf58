// ===== GRÁFICOS INTERACTIVOS PARA ESTADÍSTICAS =====

// Datos de ejemplo para los gráficos (7 días de la semana)
const chartData = {
    'visitas-totales': {
        labels: ['Lun', 'Mar', 'Mi<PERSON>', '<PERSON><PERSON>', 'Vie', '<PERSON>áb', 'Dom'],
        data: [1850, 2100, 1950, 2300, 2150, 1800, 1694],
        color: '#00ff9f', // Verde fosforescente
        total: 12847
    },
    'visitantes-unicos': {
        labels: ['Lun', 'Mar', 'Mié', 'Jue', 'Vie', 'Sáb', 'Dom'],
        data: [1320, 1450, 1380, 1650, 1520, 1280, 1234],
        color: '#ff6b9d', // Rosa fosforescente
        total: 9234
    },
    'productos-vistos': {
        labels: ['Lun', 'Mar', 'Mié', 'Jue', 'Vie', 'Sáb', 'Dom'],
        data: [650, 720, 680, 780, 720, 600, 567],
        color: '#4ecdc4', // Turquesa fosforescente
        total: 4567
    },
    'productos-compartidos': {
        labels: ['Lun', 'Mar', '<PERSON><PERSON>', 'Jue', 'Vie', 'Sáb', 'Dom'],
        data: [125, 140, 130, 150, 135, 120, 92],
        color: '#ffe66d', // Amarillo fosforescente
        total: 892
    }
};

// Variable para almacenar las instancias de los gráficos
let chartInstances = {};

// ===== INICIALIZACIÓN DE GRÁFICOS =====
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 Inicializando gráficos de estadísticas...');
    
    // Solo inicializar en modo desktop
    if (window.innerWidth > 768) {
        initializeCharts();
    }
    
    // Reinicializar en cambio de tamaño de ventana
    window.addEventListener('resize', function() {
        if (window.innerWidth > 768) {
            initializeCharts();
        } else {
            destroyCharts();
        }
    });
});

// ===== FUNCIÓN PARA INICIALIZAR TODOS LOS GRÁFICOS =====
function initializeCharts() {
    console.log('📊 Creando gráficos interactivos...');
    
    // Destruir gráficos existentes si los hay
    destroyCharts();
    
    // Crear cada gráfico
    Object.keys(chartData).forEach(chartId => {
        createChart(chartId, chartData[chartId]);
    });
    
    console.log('✅ Gráficos inicializados correctamente');
}

// ===== FUNCIÓN PARA CREAR UN GRÁFICO INDIVIDUAL =====
function createChart(chartId, data) {
    const canvas = document.getElementById(`chart-${chartId}`);
    if (!canvas) {
        console.warn(`⚠️ Canvas no encontrado para: chart-${chartId}`);
        return;
    }
    
    const ctx = canvas.getContext('2d');
    
    // Configurar el canvas
    canvas.width = 200;
    canvas.height = 80;
    
    // Crear el gráfico con Chart.js (si está disponible) o canvas nativo
    if (typeof Chart !== 'undefined') {
        createChartJS(canvas, ctx, data, chartId);
    } else {
        createNativeChart(canvas, ctx, data, chartId);
    }
}

// ===== CREAR GRÁFICO CON CHART.JS =====
function createChartJS(canvas, ctx, data, chartId) {
    const chart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: data.labels,
            datasets: [{
                data: data.data,
                borderColor: data.color,
                backgroundColor: data.color + '20', // Color con transparencia
                borderWidth: 2,
                fill: true,
                tension: 0.4,
                pointBackgroundColor: data.color,
                pointBorderColor: data.color,
                pointRadius: 4,
                pointHoverRadius: 6,
                pointBorderWidth: 2
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: '#ffffff',
                    bodyColor: '#ffffff',
                    borderColor: data.color,
                    borderWidth: 1,
                    cornerRadius: 6,
                    displayColors: false,
                    callbacks: {
                        title: function(context) {
                            return data.labels[context[0].dataIndex];
                        },
                        label: function(context) {
                            return `${context.parsed.y.toLocaleString()} visitas`;
                        }
                    }
                }
            },
            scales: {
                x: {
                    display: false
                },
                y: {
                    display: false
                }
            },
            elements: {
                point: {
                    hoverRadius: 8
                }
            },
            interaction: {
                intersect: false,
                mode: 'index'
            }
        }
    });
    
    // Almacenar la instancia del gráfico
    chartInstances[chartId] = chart;
}

// ===== CREAR GRÁFICO NATIVO CON CANVAS =====
function createNativeChart(canvas, ctx, data, chartId) {
    console.log(`📈 Creando gráfico nativo para: ${chartId}`);
    
    const width = canvas.width;
    const height = canvas.height;
    const padding = 10;
    const chartWidth = width - (padding * 2);
    const chartHeight = height - (padding * 2);
    
    // Encontrar valores máximo y mínimo
    const maxValue = Math.max(...data.data);
    const minValue = Math.min(...data.data);
    const valueRange = maxValue - minValue;
    
    // Limpiar canvas
    ctx.clearRect(0, 0, width, height);
    
    // Configurar estilos
    ctx.strokeStyle = data.color;
    ctx.fillStyle = data.color + '40'; // Color con transparencia
    ctx.lineWidth = 2;
    ctx.lineCap = 'round';
    ctx.lineJoin = 'round';
    
    // Calcular puntos
    const points = data.data.map((value, index) => {
        const x = padding + (index * (chartWidth / (data.data.length - 1)));
        const y = padding + chartHeight - ((value - minValue) / valueRange * chartHeight);
        return { x, y, value, label: data.labels[index] };
    });
    
    // Dibujar área bajo la línea
    ctx.beginPath();
    ctx.moveTo(points[0].x, height - padding);
    points.forEach(point => {
        ctx.lineTo(point.x, point.y);
    });
    ctx.lineTo(points[points.length - 1].x, height - padding);
    ctx.closePath();
    ctx.fill();
    
    // Dibujar línea
    ctx.beginPath();
    ctx.moveTo(points[0].x, points[0].y);
    points.forEach(point => {
        ctx.lineTo(point.x, point.y);
    });
    ctx.stroke();
    
    // Dibujar puntos
    points.forEach(point => {
        ctx.beginPath();
        ctx.arc(point.x, point.y, 3, 0, 2 * Math.PI);
        ctx.fillStyle = data.color;
        ctx.fill();
        ctx.strokeStyle = '#ffffff';
        ctx.lineWidth = 1;
        ctx.stroke();
    });
    
    // Agregar interactividad
    addCanvasInteractivity(canvas, points, data, chartId);
}

// ===== AGREGAR INTERACTIVIDAD AL CANVAS NATIVO =====
function addCanvasInteractivity(canvas, points, data, chartId) {
    let tooltip = null;
    
    canvas.addEventListener('mousemove', function(e) {
        const rect = canvas.getBoundingClientRect();
        const mouseX = e.clientX - rect.left;
        const mouseY = e.clientY - rect.top;
        
        // Encontrar el punto más cercano
        let closestPoint = null;
        let minDistance = Infinity;
        
        points.forEach(point => {
            const distance = Math.sqrt(
                Math.pow(mouseX - point.x, 2) + Math.pow(mouseY - point.y, 2)
            );
            if (distance < minDistance && distance < 20) {
                minDistance = distance;
                closestPoint = point;
            }
        });
        
        if (closestPoint) {
            showTooltip(e, closestPoint, data.color);
            canvas.style.cursor = 'pointer';
        } else {
            hideTooltip();
            canvas.style.cursor = 'default';
        }
    });
    
    canvas.addEventListener('mouseleave', function() {
        hideTooltip();
        canvas.style.cursor = 'default';
    });
}

// ===== MOSTRAR TOOLTIP =====
function showTooltip(event, point, color) {
    hideTooltip(); // Ocultar tooltip anterior
    
    const tooltip = document.createElement('div');
    tooltip.className = 'chart-tooltip';
    tooltip.innerHTML = `
        <div class="tooltip-day">${point.label}</div>
        <div class="tooltip-value">${point.value.toLocaleString()}</div>
    `;
    
    // Estilos del tooltip
    tooltip.style.cssText = `
        position: fixed;
        background: rgba(0, 0, 0, 0.9);
        color: white;
        padding: 8px 12px;
        border-radius: 6px;
        font-size: 0.8rem;
        pointer-events: none;
        z-index: 10000;
        border: 1px solid ${color};
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        transform: translate(-50%, -100%);
        margin-top: -10px;
    `;
    
    tooltip.style.left = event.clientX + 'px';
    tooltip.style.top = event.clientY + 'px';
    
    document.body.appendChild(tooltip);
    
    // Almacenar referencia para poder eliminarlo
    window.currentChartTooltip = tooltip;
}

// ===== OCULTAR TOOLTIP =====
function hideTooltip() {
    if (window.currentChartTooltip) {
        window.currentChartTooltip.remove();
        window.currentChartTooltip = null;
    }
}

// ===== DESTRUIR TODOS LOS GRÁFICOS =====
function destroyCharts() {
    // Destruir gráficos de Chart.js
    Object.values(chartInstances).forEach(chart => {
        if (chart && typeof chart.destroy === 'function') {
            chart.destroy();
        }
    });
    chartInstances = {};
    
    // Limpiar canvas nativos
    Object.keys(chartData).forEach(chartId => {
        const canvas = document.getElementById(`chart-${chartId}`);
        if (canvas) {
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
        }
    });
    
    // Ocultar tooltip si existe
    hideTooltip();
}

// ===== FUNCIÓN PARA ACTUALIZAR DATOS DE GRÁFICOS =====
function updateChartData(chartId, newData) {
    if (chartInstances[chartId]) {
        // Actualizar Chart.js
        chartInstances[chartId].data.datasets[0].data = newData.data;
        chartInstances[chartId].update();
    } else {
        // Recrear gráfico nativo
        chartData[chartId].data = newData.data;
        createChart(chartId, chartData[chartId]);
    }
}

// ===== POPUP DE KPI EN RESPONSIVE (MEJORADO) =====
if (window.innerWidth <= 768) {
    document.addEventListener('DOMContentLoaded', function() {
        console.log('🟢 Inicializando eventos de KPIs responsive');
        // Seleccionar todos los KPIs principales en la vista responsive
        const kpiResponsive = document.querySelectorAll('.responsive-content .kpi');
        console.log('🟢 KPIs encontrados:', kpiResponsive.length);
        // Mapear títulos a los datos de gráfico
        const kpiGraficos = [
            { titulo: 'Visitas Totales', chart: 'visitas-totales', nombre: 'Visitas Totales' },
            { titulo: 'Visitantes Únicos', chart: 'visitantes-unicos', nombre: 'Visitantes Únicos' },
            { titulo: 'Productos Vistos', chart: 'productos-vistos', nombre: 'Productos Vistos' },
            { titulo: 'Productos Compartidos', chart: 'productos-compartidos', nombre: 'Productos Compartidos' }
        ];
        kpiResponsive.forEach(el => {
            const titulo = el.querySelector('.kpi-title')?.textContent.trim();
            const kpi = kpiGraficos.find(k => k.titulo === titulo);
            if (kpi) {
                el.style.cursor = 'pointer';
                el.addEventListener('click', function(e) {
                    console.log('🟢 Click en KPI responsive:', titulo);
                    e.stopPropagation();
                    mostrarPopupKPIResponsive(kpi);
                });
            } else {
                console.log('🔴 KPI no mapeado:', titulo);
            }
        });
    });
}

// Función para mostrar el popup de KPI en responsive
function mostrarPopupKPIResponsive(kpi) {
    // Si ya existe el popup, eliminarlo primero
    let popup = document.getElementById('kpi-popup-responsive');
    if (popup) popup.remove();

    // Crear overlay
    popup = document.createElement('div');
    popup.id = 'kpi-popup-responsive';
    popup.style.position = 'fixed';
    popup.style.top = '0';
    popup.style.left = '0';
    popup.style.width = '100vw';
    popup.style.height = '100vh';
    popup.style.background = 'rgba(0,0,0,0.7)';
    popup.style.display = 'flex';
    popup.style.alignItems = 'center';
    popup.style.justifyContent = 'center';
    popup.style.zIndex = '9999';

    // Cerrar popup haciendo clic fuera del contenedor
    popup.addEventListener('click', function(e) {
        if (e.target === popup) {
            popup.remove();
        }
    });

    // Crear contenedor del popup
    const container = document.createElement('div');
    container.style.background = 'rgba(35,41,58,0.7)'; // Fondo semitransparente
    container.style.borderRadius = '18px';
    container.style.padding = '28px 18px 18px 18px';
    container.style.boxShadow = '0 8px 32px rgba(0,0,0,0.4)';
    container.style.width = '90vw';
    container.style.maxWidth = '400px';
    container.style.position = 'relative';
    container.style.textAlign = 'center';
    container.style.overflow = 'hidden'; // Evita desbordes del gráfico

    // Botón de cerrar
    const btnCerrar = document.createElement('button');
    btnCerrar.textContent = '×';
    btnCerrar.style.position = 'absolute';
    btnCerrar.style.top = '10px';
    btnCerrar.style.right = '16px';
    btnCerrar.style.background = 'none';
    btnCerrar.style.border = 'none';
    btnCerrar.style.fontSize = '2rem';
    btnCerrar.style.color = '#fff';
    btnCerrar.style.cursor = 'pointer';
    btnCerrar.addEventListener('click', function(e) {
        e.stopPropagation();
        popup.remove();
    });
    container.appendChild(btnCerrar);

    // Título del KPI
    const titulo = document.createElement('h2');
    titulo.textContent = kpi.nombre;
    titulo.style.color = '#fff';
    titulo.style.marginBottom = '10px';
    titulo.style.fontWeight = 'bold';
    titulo.style.fontSize = '2rem';
    container.appendChild(titulo);

    // Valor del KPI
    const valor = document.createElement('div');
    valor.style.fontSize = '2.5rem';
    valor.style.fontWeight = 'bold';
    valor.style.color = chartData[kpi.chart].color;
    valor.style.marginBottom = '10px';
    valor.textContent = chartData[kpi.chart].total.toLocaleString();
    container.appendChild(valor);

    // Canvas para el gráfico
    const canvas = document.createElement('canvas');
    canvas.width = 320;
    canvas.height = 140;
    canvas.style.margin = '0 auto 10px auto';
    canvas.style.display = 'block';
    canvas.style.maxWidth = '100%';
    canvas.style.height = 'auto';
    container.appendChild(canvas);

    // Crear el gráfico grande
    if (typeof Chart !== 'undefined') {
        new Chart(canvas.getContext('2d'), {
            type: 'line',
            data: {
                labels: chartData[kpi.chart].labels,
                datasets: [{
                    data: chartData[kpi.chart].data,
                    borderColor: chartData[kpi.chart].color,
                    backgroundColor: chartData[kpi.chart].color + '20',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4,
                    pointBackgroundColor: chartData[kpi.chart].color,
                    pointBorderColor: chartData[kpi.chart].color,
                    pointRadius: 6,
                    pointHoverRadius: 8,
                    pointBorderWidth: 2
                }]
            },
            options: {
                responsive: false,
                maintainAspectRatio: false,
                plugins: {
                    legend: { display: false },
                    tooltip: {
                        enabled: true,
                        mode: 'nearest',
                        intersect: false,
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: '#ffffff',
                        bodyColor: '#ffffff',
                        borderColor: chartData[kpi.chart].color,
                        borderWidth: 1,
                        cornerRadius: 6,
                        displayColors: false
                    }
                },
                layout: {
                    padding: 0
                },
                scales: {
                    x: { display: true, ticks: { color: '#aaa' } },
                    y: { display: true, ticks: { color: '#aaa' } }
                },
                elements: { point: { hoverRadius: 10 } },
                interaction: { mode: 'nearest', intersect: false }
            }
        });
    }

    // Agregar el contenedor al overlay
    popup.appendChild(container);
    document.body.appendChild(popup);

    // Cerrar popup con Escape
    document.addEventListener('keydown', function escListener(e) {
        if (e.key === 'Escape') {
            popup.remove();
            document.removeEventListener('keydown', escListener);
        }
    });
}

console.log('📊 Módulo de gráficos de estadísticas cargado');
