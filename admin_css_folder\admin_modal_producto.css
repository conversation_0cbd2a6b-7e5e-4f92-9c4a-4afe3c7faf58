/* ===== MODAL DE AGREGAR PRODUCTO ===== */
.modal-producto-overlay {
    position: fixed;                  /* Posición fija para cubrir toda la pantalla */
    top: 0;                          /* Desde arriba */
    left: 0;                         /* Desde la izquierda */
    width: 100%;                     /* Ancho completo */
    height: 100%;                    /* Alto completo */
    background: rgba(0, 0, 0, 0.8);  /* Fondo negro semi-transparente */
    display: none;                   /* Oculto por defecto */
    justify-content: center;         /* Centra horizontalmente */
    align-items: center;             /* Centra verticalmente */
    z-index: 10000;                  /* Z-index alto para estar encima de todo */
    backdrop-filter: blur(5px);      /* Efecto de desenfoque */
}

.modal-producto-overlay.active {
    display: flex;                   /* Muestra el modal cuando está activo */
}

.modal-producto-container {
    background: #1a202c;            /* Fondo oscuro del modal */
    border-radius: 12px;             /* <PERSON>rdes redondeados */
    padding: 30px;                   /* Padding interno */
    max-width: 600px;                /* Ancho máximo */
    width: 90%;                      /* Ancho responsive */
    max-height: 90vh;                /* Altura máxima */
    overflow-y: auto;                /* Scroll vertical si es necesario */
    position: relative;              /* Para posicionar el botón de cerrar */
    border: 1px solid #6b46c1;      /* Borde morado */
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5); /* Sombra */
}

/* ===== BOTÓN DE CERRAR ===== */
.modal-producto-close {
    position: absolute;              /* Posición absoluta */
    top: 15px;                       /* Desde arriba */
    right: 20px;                     /* Desde la derecha */
    background: none;                /* Sin fondo */
    border: none;                    /* Sin borde */
    color: rgba(255, 255, 255, 0.7); /* Color gris claro */
    font-size: 1.5rem;               /* Tamaño grande */
    cursor: pointer;                 /* Cursor de mano */
    width: 30px;                     /* Ancho */
    height: 30px;                    /* Alto */
    display: flex;                   /* Flexbox para centrar */
    align-items: center;             /* Centra verticalmente */
    justify-content: center;         /* Centra horizontalmente */
    border-radius: 50%;              /* Circular */
    transition: all 0.3s ease;      /* Transición suave */
}

.modal-producto-close:hover {
    background: rgba(255, 255, 255, 0.1); /* Fondo al hover */
    color: #ffffff;                  /* Color blanco al hover */
    transform: scale(1.1);           /* Agranda al hover */
}

/* ===== TÍTULO DEL MODAL ===== */
.modal-producto-title {
    font-size: 1.5rem;               /* Tamaño del título */
    color: #ffffff;                  /* Color blanco */
    font-weight: 600;                /* Peso semi-bold */
    margin-bottom: 25px;             /* Margen inferior */
    text-align: center;              /* Centrado */
    padding-right: 40px;             /* Espacio para el botón de cerrar */
}

/* ===== FORMULARIO ===== */
.modal-producto-form {
    display: flex;                   /* Flexbox */
    flex-direction: column;          /* Organiza verticalmente */
    gap: 20px;                       /* Espacio entre elementos */
}

/* ===== GRUPOS DE CAMPOS ===== */
.modal-form-group {
    display: flex;                   /* Flexbox */
    flex-direction: column;          /* Organiza verticalmente */
    gap: 8px;                        /* Espacio entre label e input */
}

.modal-form-label {
    font-size: 0.9rem;               /* Tamaño del label */
    color: rgba(255, 255, 255, 0.8); /* Color blanco semi-transparente */
    font-weight: 500;                /* Peso medio */
}

.modal-form-input {
    background: #2d3748;             /* Fondo oscuro */
    border: 1px solid #4a5568;      /* Borde gris */
    border-radius: 6px;              /* Bordes redondeados */
    padding: 12px 15px;              /* Padding interno */
    color: #ffffff;                  /* Color del texto */
    font-size: 0.9rem;               /* Tamaño de fuente */
    transition: all 0.3s ease;      /* Transición suave */
}

.modal-form-input:focus {
    outline: none;                   /* Sin outline */
    border-color: #6b46c1;          /* Borde morado al focus */
    box-shadow: 0 0 0 3px rgba(107, 70, 193, 0.2); /* Sombra morada */
}

.modal-form-input::placeholder {
    color: rgba(255, 255, 255, 0.4); /* Color del placeholder */
}

/* ===== GRUPO DE CATEGORÍAS ===== */
.modal-categorias-group {
    display: grid;                   /* Grid layout */
    grid-template-columns: 1fr 1fr;  /* 2 columnas iguales */
    gap: 15px;                       /* Espacio entre columnas */
}

/* ===== GRUPO DE PRECIOS ===== */
.modal-precios-group {
    display: grid;                   /* Grid layout */
    grid-template-columns: 1fr 1fr;  /* 2 columnas iguales */
    gap: 15px;                       /* Espacio entre columnas */
}

/* ===== SELECCIÓN DE PESTAÑAS ===== */
.modal-pestanas-group {
    display: flex;                   /* Flexbox */
    flex-direction: column;          /* Organiza verticalmente */
    gap: 12px;                       /* Espacio entre elementos */
}

.modal-pestanas-grid {
    display: grid;                   /* Grid layout */
    grid-template-columns: repeat(3, 1fr); /* 3 columnas */
    grid-template-rows: repeat(2, 1fr);    /* 2 filas */
    gap: 10px;                       /* Espacio entre elementos */
}

.modal-pestana-option {
    position: relative;              /* Para posicionar el radio */
    cursor: pointer;                 /* Cursor de mano */
}

.modal-pestana-radio {
    position: absolute;              /* Posición absoluta */
    opacity: 0;                      /* Invisible */
    pointer-events: none;            /* No recibe eventos */
}

.modal-pestana-label {
    display: block;                  /* Display block */
    background: #2d3748;             /* Fondo oscuro */
    border: 2px solid #4a5568;      /* Borde gris */
    border-radius: 8px;              /* Bordes redondeados */
    padding: 12px 15px;              /* Padding interno */
    text-align: center;              /* Texto centrado */
    color: rgba(255, 255, 255, 0.7); /* Color gris claro */
    font-size: 0.85rem;              /* Tamaño de fuente */
    font-weight: 500;                /* Peso medio */
    transition: all 0.3s ease;      /* Transición suave */
    text-transform: capitalize;      /* Primera letra mayúscula */
}

.modal-pestana-radio:checked + .modal-pestana-label {
    background: #6b46c1;             /* Fondo morado cuando está seleccionado */
    border-color: #6b46c1;          /* Borde morado */
    color: #ffffff;                  /* Color blanco */
    box-shadow: 0 4px 8px rgba(107, 70, 193, 0.3); /* Sombra morada */
}

.modal-pestana-label:hover {
    border-color: #6b46c1;          /* Borde morado al hover */
    color: #ffffff;                  /* Color blanco al hover */
}

/* ===== CARGA DE IMAGEN ===== */
.modal-imagen-group {
    display: flex;                   /* Flexbox */
    flex-direction: column;          /* Organiza verticalmente */
    gap: 12px;                       /* Espacio entre elementos */
}

.modal-imagen-upload {
    border: 2px dashed #4a5568;     /* Borde punteado */
    border-radius: 8px;              /* Bordes redondeados */
    padding: 30px;                   /* Padding interno */
    text-align: center;              /* Texto centrado */
    background: #2d3748;             /* Fondo oscuro */
    cursor: pointer;                 /* Cursor de mano */
    transition: all 0.3s ease;      /* Transición suave */
    position: relative;              /* Para posicionar elementos internos */
}

.modal-imagen-upload:hover {
    border-color: #6b46c1;          /* Borde morado al hover */
    background: rgba(107, 70, 193, 0.1); /* Fondo morado semi-transparente */
}

.modal-imagen-upload.has-image {
    border-color: #10b981;          /* Borde verde cuando tiene imagen */
    background-size: cover;          /* Cubre todo el contenedor */
    background-position: center;     /* Centra la imagen */
    background-repeat: no-repeat;    /* No repite la imagen */
}

.modal-imagen-placeholder {
    color: rgba(255, 255, 255, 0.6); /* Color gris claro */
    font-size: 0.9rem;               /* Tamaño de fuente */
}

.modal-imagen-icon {
    font-size: 2rem;                 /* Tamaño del icono */
    color: #6b46c1;                  /* Color morado */
    margin-bottom: 10px;             /* Margen inferior */
}

.modal-imagen-input {
    position: absolute;              /* Posición absoluta */
    opacity: 0;                      /* Invisible */
    pointer-events: none;            /* No recibe eventos */
}

/* ===== BOTONES DE ACCIÓN ===== */
.modal-producto-actions {
    display: flex;                   /* Flexbox */
    gap: 15px;                       /* Espacio entre botones */
    justify-content: flex-end;       /* Alinea a la derecha */
    margin-top: 30px;                /* Margen superior */
    padding-top: 20px;               /* Padding superior */
    border-top: 1px solid rgba(255, 255, 255, 0.1); /* Línea separadora */
}

.modal-btn {
    padding: 12px 24px;              /* Padding interno */
    border: none;                    /* Sin borde */
    border-radius: 6px;              /* Bordes redondeados */
    font-size: 0.9rem;               /* Tamaño de fuente */
    font-weight: 500;                /* Peso medio */
    cursor: pointer;                 /* Cursor de mano */
    transition: all 0.3s ease;      /* Transición suave */
    min-width: 100px;                /* Ancho mínimo */
}

.modal-btn-cancelar {
    background: #4a5568;             /* Fondo gris */
    color: #ffffff;                  /* Color blanco */
}

.modal-btn-cancelar:hover {
    background: #2d3748;             /* Fondo más oscuro al hover */
    transform: translateY(-1px);     /* Eleva ligeramente */
}

.modal-btn-guardar {
    background: #10b981;             /* Fondo verde */
    color: #ffffff;                  /* Color blanco */
}

.modal-btn-guardar:hover {
    background: #059669;             /* Fondo verde más oscuro al hover */
    transform: translateY(-1px);     /* Eleva ligeramente */
    box-shadow: 0 4px 8px rgba(16, 185, 129, 0.3); /* Sombra verde */
}

/* ===== RESPONSIVE ===== */
@media (max-width: 768px) {
    .modal-producto-container {
        width: 95%;                  /* Ancho mayor en móvil */
        padding: 20px;               /* Padding menor */
        max-height: 95vh;            /* Altura mayor */
    }

    .modal-producto-title {
        font-size: 1.3rem;           /* Título más pequeño */
        margin-bottom: 20px;         /* Margen menor */
    }

    .modal-categorias-group {
        grid-template-columns: 1fr;  /* Una columna en móvil */
        gap: 15px;                   /* Espacio entre elementos */
    }

    .modal-precios-group {
        grid-template-columns: 1fr;  /* Una columna en móvil */
        gap: 15px;                   /* Espacio entre elementos */
    }

    .modal-pestanas-grid {
        grid-template-columns: 1fr 1fr; /* 2 columnas en móvil */
        grid-template-rows: repeat(3, 1fr); /* 3 filas */
        gap: 8px;                    /* Espacio menor */
    }

    .modal-pestana-label {
        padding: 10px 12px;          /* Padding menor */
        font-size: 0.8rem;           /* Fuente más pequeña */
    }

    .modal-imagen-upload {
        padding: 20px;               /* Padding menor */
    }

    .modal-imagen-icon {
        font-size: 1.5rem;           /* Icono más pequeño */
    }

    .modal-producto-actions {
        flex-direction: column;      /* Botones en columna */
        gap: 10px;                   /* Espacio menor */
    }

    .modal-btn {
        width: 100%;                 /* Ancho completo */
        padding: 14px 20px;          /* Padding mayor para móvil */
    }
}
