/* ===== ESTILOS DEL CONTENEDOR DE BANNER PUBLICITARIO ===== */

/* ===== TÍTULO EXTERNO DEL BANNER ===== */
.banner-external-title {
    font-size: 2.5rem;              /* Tamaño grande igual que marca */
    font-weight: bold;              /* Texto en negrita igual que marca */
    color: #5b21b6;                 /* Color morado oscuro igual que marca */
    margin-bottom: 8px;             /* Espacio pequeño debajo del título igual que marca */
    text-align: left;               /* Alineación a la izquierda igual que marca */
    max-width: 1200px;              /* Ancho máximo igual al contenedor */
    margin: 30px auto 8px auto;     /* Margen igual que marca */
}

/* ===== CONTENEDOR PRINCIPAL DEL BANNER ===== */
.banner-container {
    background: #2d3748;            /* Fondo gris azulado como marca */
    padding: 20px;                  /* Espaciado interno igual a marca */
    margin: 30px auto;              /* Margen igual a marca */
    max-width: 1200px;              /* Ancho máximo */
    /* Sin border, border-radius ni box-shadow para coincidir con marca */
}

/* ===== CONTENIDO DEL BANNER ===== */
.banner-content {
    display: grid;                  /* Grid layout */
    grid-template-columns: 1fr 1fr; /* Dos columnas iguales */
    gap: 30px;                      /* Espacio entre columnas */
    align-items: start;             /* Alineación superior */
}

/* ===== COLUMNAS DEL BANNER ===== */
.banner-column {
    background: #1a202c;            /* Fondo azul oscuro igual que marca */
    border-radius: 12px;            /* Bordes redondeados igual que marca */
    border-left: 4px solid #6b46c1; /* Borde izquierdo morado igual que marca */
    padding: 25px;                  /* Espaciado interno */
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3); /* Sombra igual que marca */
}

/* ===== TÍTULOS DE COLUMNA ===== */
.column-title {
    color: #ffffff;                 /* Color blanco */
    font-size: 1.3rem;              /* Tamaño mediano */
    font-weight: 600;               /* Peso semi-bold */
    margin-bottom: 20px;            /* Espacio debajo */
    text-align: center;             /* Centrado */
    border-bottom: 2px solid #6b46c1; /* Línea morada debajo */
    padding-bottom: 10px;           /* Espacio interno debajo */
}

/* ===== TARJETAS DE CARGA DE IMAGEN ===== */
.image-upload-card {
    background: #1a202c;            /* Fondo azul oscuro */
    border: 3px dashed #6b46c1;     /* Borde punteado morado */
    border-radius: 10px;            /* Bordes redondeados */
    height: 200px;                  /* Altura fija */
    display: flex;                  /* Flexbox para centrar contenido */
    flex-direction: column;         /* Organiza verticalmente */
    align-items: center;            /* Centra horizontalmente */
    justify-content: center;        /* Centra verticalmente */
    cursor: pointer;                /* Cursor de mano */
    transition: all 0.3s ease;      /* Transición suave */
    position: relative;             /* Posición relativa para elementos absolutos */
    overflow: hidden;               /* Oculta desbordamiento */
    margin-bottom: 20px;            /* Espacio debajo */
}

.image-upload-card:hover {
    border-color: #8b5cf6;          /* Borde más claro al hover */
    background: #2d3748;            /* Fondo más claro al hover */
    transform: translateY(-2px);    /* Eleva ligeramente al hover */
    box-shadow: 0 6px 12px rgba(107, 70, 193, 0.2); /* Sombra morada */
}

/* ===== PLACEHOLDER DE IMAGEN ===== */
.image-placeholder {
    text-align: center;             /* Centra el contenido */
    color: rgba(255, 255, 255, 0.7); /* Color gris claro */
}

.image-placeholder-icon {
    font-size: 3rem;                /* Icono grande */
    margin-bottom: 10px;            /* Espacio debajo */
    color: #6b46c1;                 /* Color morado */
}

.image-placeholder-text {
    font-size: 1rem;                /* Tamaño normal */
    font-weight: 500;               /* Peso medio */
    line-height: 1.4;               /* Altura de línea */
}

/* ===== IMAGEN CARGADA ===== */
.image-upload-card img {
    width: 100%;                    /* Ancho completo */
    height: 100%;                   /* Alto completo */
    object-fit: cover;              /* Ajusta la imagen manteniendo proporción */
    border-radius: 8px;             /* Bordes redondeados */
}

/* ===== INDICADOR DE IMAGEN CARGADA ===== */
.image-upload-card.has-image {
    border-style: solid;            /* Borde sólido cuando hay imagen */
    border-color: #10b981;          /* Borde verde para indicar éxito */
}

.image-upload-card.has-image:hover {
    border-color: #059669;          /* Verde más oscuro al hover */
}

/* ===== OVERLAY DE EDICIÓN DESHABILITADO ===== */
.edit-overlay {
    display: none !important;       /* Oculta completamente el overlay */
}

.edit-icon {
    display: none !important;       /* Oculta completamente el icono */
}

/* ===== ESTILOS DEL BOTÓN CAMBIAR IMAGEN (LÁPIZ) ===== */
.btn-change-image {
    background: #6b46c1;            /* Fondo morado */
    color: #ffffff;                 /* Texto blanco */
    border: none;                   /* Sin borde */
    border-radius: 6px;             /* Bordes redondeados */
    padding: 8px 12px;              /* Espaciado interno reducido */
    font-size: 1rem;                /* Tamaño de fuente para el emoji */
    font-weight: 500;               /* Peso medio */
    cursor: pointer;                /* Cursor de mano */
    transition: all 0.3s ease;      /* Transición suave */
    min-width: 40px;                /* Ancho mínimo reducido */
    flex: 0 0 auto;                 /* No crece ni se encoge */
    display: flex;                  /* Flexbox para centrar */
    align-items: center;            /* Centra verticalmente */
    justify-content: center;        /* Centra horizontalmente */
}

.btn-change-image:hover {
    background: #553c9a;            /* Morado más oscuro al hover */
    transform: translateY(-1px);    /* Eleva ligeramente al hover */
    box-shadow: 0 2px 4px rgba(107, 70, 193, 0.3); /* Sombra morada */
}

/* ===== INFORMACIÓN DEL FORMATO DEL BANNER ===== */
.banner-format-info {
    display: flex;                  /* Flexbox para centrar */
    justify-content: center;        /* Centra horizontalmente */
    align-items: center;            /* Centra verticalmente */
    margin-bottom: 20px;            /* Espacio debajo de la información */
    padding: 10px;                  /* Espaciado interno */
    background: rgba(107, 70, 193, 0.1); /* Fondo morado semi-transparente */
    border: 1px solid rgba(107, 70, 193, 0.3); /* Borde morado semi-transparente */
    border-radius: 6px;             /* Bordes redondeados */
}

.format-info-text {
    color: #ffffff;                 /* Color blanco */
    font-size: 0.9rem;              /* Tamaño de fuente */
    font-weight: 500;               /* Peso medio */
    text-align: center;             /* Centra el texto */
}

/* ===== ESTILOS DE LAS OPCIONES DE BANNER ===== */
.banner-options {
    display: flex !important;       /* Flexbox para organizar elementos - forzado */
    flex-direction: row !important; /* Organiza horizontalmente - forzado */
    align-items: center;            /* Centra verticalmente */
    justify-content: center;        /* Centra horizontalmente */
    gap: 20px;                      /* Espacio entre elementos */
    margin-bottom: 20px;            /* Espacio debajo de las opciones */
}

/* ===== OPCIONES DE RADIO ===== */
.radio-option {
    display: flex;                  /* Flexbox para organizar elementos */
    align-items: center;            /* Centra verticalmente */
    gap: 8px;                       /* Espacio entre elementos */
    cursor: pointer;                /* Cursor de mano */
    transition: all 0.3s ease;      /* Transición suave */
}

.radio-option:hover {
    transform: scale(1.05);         /* Agranda ligeramente al hover */
}

.radio-option input[type="radio"] {
    display: none;                  /* Oculta el radio button nativo */
}

.radio-circle {
    width: 20px;                    /* Ancho del círculo */
    height: 20px;                   /* Alto del círculo */
    border: 2px solid rgba(255, 255, 255, 0.3); /* Borde semi-transparente */
    border-radius: 50%;             /* Círculo perfecto */
    position: relative;             /* Posición relativa para el punto interno */
    transition: all 0.3s ease;      /* Transición suave */
}

.radio-option input[type="radio"]:checked + .radio-circle {
    border-color: #6b46c1;          /* Borde morado cuando está seleccionado */
    background: rgba(107, 70, 193, 0.1); /* Fondo morado semi-transparente */
}

.radio-option input[type="radio"]:checked + .radio-circle::after {
    content: '';                    /* Contenido vacío para el pseudo-elemento */
    position: absolute;             /* Posición absoluta */
    top: 50%;                       /* Centrado verticalmente */
    left: 50%;                      /* Centrado horizontalmente */
    transform: translate(-50%, -50%); /* Centra el punto */
    width: 8px;                     /* Ancho del punto */
    height: 8px;                    /* Alto del punto */
    background: #6b46c1;            /* Color morado */
    border-radius: 50%;             /* Círculo perfecto */
}

.radio-label {
    color: #ffffff;                 /* Color blanco */
    font-size: 0.9rem;              /* Tamaño de fuente */
    font-weight: 500;               /* Peso medio */
    user-select: none;              /* No seleccionable */
}

/* ===== ESTILOS DE LOS BOTONES ===== */
.action-buttons {
    display: flex;                  /* Flexbox para organizar botones */
    gap: 10px;                      /* Espacio reducido entre botones */
    justify-content: center;        /* Centra los botones */
}

.btn-save,
.btn-cancel {
    padding: 8px 12px;              /* Espaciado interno más reducido */
    border: none;                   /* Sin borde */
    border-radius: 6px;             /* Bordes redondeados */
    font-size: 0.8rem;              /* Tamaño de fuente más reducido */
    font-weight: 500;               /* Peso medio */
    cursor: pointer;                /* Cursor de mano */
    transition: all 0.3s ease;      /* Transición suave */
    min-width: 60px;                /* Ancho mínimo más reducido */
    flex: 0 0 auto;                 /* No crecen ni se encogen */
}

.btn-save {
    background: #10b981;            /* Fondo verde */
    color: #ffffff;                 /* Texto blanco */
}

.btn-save:hover {
    background: #059669;            /* Verde más oscuro al hover */
    transform: translateY(-1px);    /* Eleva al hover */
    box-shadow: 0 2px 4px rgba(16, 185, 129, 0.3); /* Sombra verde */
}

.btn-cancel {
    background: rgba(255, 255, 255, 0.1); /* Fondo semi-transparente */
    color: #ffffff;                 /* Texto blanco */
    border: 2px solid rgba(255, 255, 255, 0.3); /* Borde semi-transparente */
}

.btn-cancel:hover {
    background: rgba(255, 255, 255, 0.2); /* Fondo más opaco al hover */
    border-color: rgba(255, 255, 255, 0.5); /* Borde más opaco al hover */
    transform: translateY(-1px);    /* Eleva al hover */
}

/* ===== HERRAMIENTAS DE EDICIÓN DE IMAGEN ===== */
.tools-section {
    margin-bottom: 25px;            /* Espacio entre secciones */
}

.tools-section:last-child {
    margin-bottom: 0;               /* Sin margen en la última sección */
}

.tools-section-title {
    color: #ffffff;                 /* Color blanco para el título */
    font-size: 1rem;                /* Tamaño de fuente */
    font-weight: 600;               /* Peso semi-bold */
    margin-bottom: 15px;            /* Espacio debajo del título */
    margin-top: 0;                  /* Sin margen superior */
    text-align: center;             /* Centra el título */
    border-bottom: 1px solid rgba(107, 70, 193, 0.3); /* Línea morada debajo */
    padding-bottom: 8px;            /* Espacio interno debajo */
}

/* ===== PRIMERA FILA - 4 OPCIONES ===== */
.tools-grid-row1 {
    display: grid;                  /* Grid layout */
    grid-template-columns: repeat(4, 1fr); /* 4 columnas iguales */
    gap: 8px;                       /* Espacio entre botones */
    margin-bottom: 10px;            /* Espacio debajo de la primera fila */
}

/* ===== SEGUNDA FILA - 3 OPCIONES CENTRADAS ===== */
.tools-grid-row2 {
    display: flex;                  /* Flexbox para centrar */
    justify-content: center;        /* Centra horizontalmente */
    gap: 8px;                       /* Espacio entre botones */
}

/* Mantener compatibilidad con tools-grid existente */
.tools-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 8px;
    margin-bottom: 10px;
}

/* ===== BOTONES DE HERRAMIENTAS ===== */
.tool-btn {
    background: #1a202c;            /* Fondo azul oscuro */
    color: #ffffff;                 /* Texto blanco */
    border: 2px solid rgba(255, 255, 255, 0.1); /* Borde semi-transparente */
    border-radius: 6px;             /* Bordes redondeados */
    padding: 8px 4px;               /* Espaciado interno */
    font-size: 0.75rem;             /* Tamaño de fuente pequeño */
    font-weight: 500;               /* Peso medio */
    cursor: pointer;                /* Cursor de mano */
    transition: all 0.3s ease;      /* Transición suave */
    display: flex;                  /* Flexbox para organizar contenido */
    flex-direction: column;         /* Organiza verticalmente */
    align-items: center;            /* Centra horizontalmente */
    gap: 4px;                       /* Espacio entre icono y texto */
    min-height: 50px;               /* Altura mínima */
    text-align: center;             /* Centra el texto */
}

.tool-btn:hover {
    background: #6b46c1;            /* Fondo morado al hover */
    border-color: #6b46c1;          /* Borde morado al hover */
    transform: translateY(-2px);    /* Eleva al hover */
    box-shadow: 0 4px 8px rgba(107, 70, 193, 0.3); /* Sombra morada */
}

.tool-btn.active {
    background: #6b46c1;            /* Fondo morado cuando está activo */
    border-color: #6b46c1;          /* Borde morado cuando está activo */
    box-shadow: 0 0 0 3px rgba(107, 70, 193, 0.2); /* Sombra morada */
}

.tool-icon {
    font-size: 1rem;                /* Tamaño del icono */
    line-height: 1;                 /* Altura de línea */
}

.tool-label {
    font-size: 0.65rem;             /* Tamaño pequeño para la etiqueta */
    line-height: 1.2;               /* Altura de línea */
    text-align: center;             /* Centra el texto */
}

/* ===== CONTROLES DE AJUSTE DESKTOP ===== */
.desktop-adjustments {
    display: grid !important;       /* Visible por defecto en desktop */
}

.adjustment-control {
    display: flex;                  /* Flexbox para organizar elementos */
    flex-direction: column;         /* Organiza verticalmente */
    gap: 8px;                       /* Espacio entre elementos */
    margin-bottom: 15px;            /* Espacio debajo de cada control */
}

.adjustment-label {
    color: #ffffff;                 /* Color blanco */
    font-size: 0.9rem;              /* Tamaño de fuente */
    font-weight: 500;               /* Peso medio */
    margin-bottom: 5px;             /* Espacio debajo de la etiqueta */
}

.adjustment-slider-container {
    display: flex;                  /* Flexbox para slider y valor */
    align-items: center;            /* Centra verticalmente */
    gap: 10px;                      /* Espacio entre slider y valor */
}

.adjustment-slider {
    flex: 1;                        /* Ocupa el espacio disponible */
    height: 6px;                    /* Altura del slider */
    background: #1a202c;            /* Fondo del slider */
    border-radius: 3px;             /* Bordes redondeados */
    outline: none;                  /* Sin outline */
    cursor: pointer;                /* Cursor de mano */
    -webkit-appearance: none;       /* Elimina estilo por defecto en WebKit */
    appearance: none;               /* Elimina estilo por defecto */
}

.adjustment-slider::-webkit-slider-thumb {
    -webkit-appearance: none;       /* Elimina estilo por defecto */
    appearance: none;               /* Elimina estilo por defecto */
    width: 18px;                    /* Ancho del thumb */
    height: 18px;                   /* Alto del thumb */
    background: #6b46c1;            /* Color morado */
    border-radius: 50%;             /* Circular */
    cursor: pointer;                /* Cursor de mano */
    transition: all 0.3s ease;      /* Transición suave */
}

.adjustment-slider::-webkit-slider-thumb:hover {
    background: #553c9a;            /* Morado más oscuro al hover */
    transform: scale(1.2);          /* Agranda al hover */
}

.adjustment-slider::-moz-range-thumb {
    width: 18px;                    /* Ancho del thumb */
    height: 18px;                   /* Alto del thumb */
    background: #6b46c1;            /* Color morado */
    border-radius: 50%;             /* Circular */
    cursor: pointer;                /* Cursor de mano */
    border: none;                   /* Sin borde */
    transition: all 0.3s ease;      /* Transición suave */
}

.adjustment-value {
    color: rgba(255, 255, 255, 0.8); /* Color gris claro */
    font-size: 0.8rem;              /* Tamaño pequeño */
    min-width: 45px;                /* Ancho mínimo */
    text-align: center;             /* Centra el texto */
    font-weight: 500;               /* Peso medio */
}

/* ===== ESTILOS DE LOS BOTONES DE AJUSTE +/- ===== */

/* DEFAULT STATE: Desktop mode - only show sliders */
.desktop-adjustments {
    display: grid !important;       /* Show sliders by default */
}

.responsive-adjustments {
    display: none !important;       /* Hide responsive elements by default */
}

/* Force hide ALL responsive adjustment elements in desktop */
.responsive-adjustments,
.responsive-adjustments *,
.adjustments-buttons,
.adjustments-buttons *,
.adjustment-button-control,
.adjustment-display {
    display: none !important;       /* Force hide all responsive elements */
}

/* Ensure desktop mode (large screens) only shows sliders */
@media (min-width: 1025px) {
    .desktop-adjustments {
        display: grid !important;    /* Force show sliders in desktop */
    }

    .responsive-adjustments,
    .responsive-adjustments *,
    .adjustments-buttons,
    .adjustments-buttons *,
    .adjustment-button-control,
    .adjustment-display {
        display: none !important;    /* Force hide ALL responsive elements */
    }
}

/* ===== ESTILOS DE LOS CONTROLES DE BOTONES +/- ===== */
.adjustment-button-control {
    display: flex;                  /* Flexbox para organizar elementos */
    flex-direction: column;         /* Organiza verticalmente */
    align-items: center;            /* Centra horizontalmente */
    gap: 6px;                       /* Espacio entre elementos - igual que marca */
    padding: 8px 4px;               /* Padding interno - igual que marca */
    text-align: center;             /* Centra el texto */
    min-height: 80px;               /* Altura mínima para 3 elementos - igual que marca */
    justify-content: space-between; /* Distribuye elementos uniformemente */
}

.adjustment-buttons {
    display: flex;                  /* Flexbox para organizar botones */
    flex-direction: row;            /* Organiza horizontalmente - igual que marca */
    align-items: center;            /* Centra verticalmente */
    gap: 6px;                       /* Espacio entre botones */
    justify-content: center;        /* Centra los botones */
}

.adjustment-btn {
    background: #4a5568;            /* Fondo gris oscuro - igual que marca */
    border: 1px solid rgba(255, 255, 255, 0.2); /* Borde sutil - igual que marca */
    color: #ffffff;                 /* Texto blanco */
    border-radius: 4px;             /* Bordes redondeados */
    width: 30px;                    /* Ancho fijo */
    height: 30px;                   /* Alto fijo */
    font-size: 1rem;                /* Tamaño de fuente */
    font-weight: bold;              /* Texto en negrita */
    cursor: pointer;                /* Cursor de mano */
    transition: all 0.3s ease;      /* Transición suave */
    display: flex;                  /* Flexbox para centrar */
    align-items: center;            /* Centra verticalmente */
    justify-content: center;        /* Centra horizontalmente */
}

.adjustment-btn:hover {
    background: #6b46c1;            /* Fondo morado al hover */
    border-color: #6b46c1;          /* Borde morado al hover */
    transform: scale(1.1);          /* Agranda al hover */
}

.adjustment-btn:active {
    transform: scale(0.95);         /* Reduce al hacer clic */
}

.adjustment-display {
    color: #ffffff;                 /* Color blanco */
    font-size: 0.8rem;              /* Tamaño de fuente aumentado */
    font-weight: 500;               /* Peso medio */
    min-width: 45px;                /* Ancho mínimo aumentado */
    text-align: center;             /* Centra el texto */
}

/* ===== ORDEN FORZADO PARA CONTROLES RESPONSIVE ===== */
/* Force uniform order: Label → Buttons → Percentage */
.responsive-adjustments .adjustment-button-control .adjustment-label {
    order: 1 !important;            /* Label at top - forced */
    font-weight: 600 !important;    /* Bold label */
}

.responsive-adjustments .adjustment-button-control .adjustment-buttons {
    order: 2 !important;            /* Buttons in middle - forced */
}

.responsive-adjustments .adjustment-button-control .adjustment-display {
    order: 3 !important;            /* Percentage at bottom - forced */
    display: block !important;      /* Show percentage values in responsive mode */
    background: none !important;    /* Remove background styling */
    border: none !important;        /* Remove border styling */
    padding: 0 !important;          /* Remove padding */
    border-radius: 0 !important;    /* Remove border radius */
}

/* Responsive adjustments grid layout - only active in responsive mode */
.responsive-adjustments.adjustments-buttons {
    display: grid !important;       /* Grid layout for responsive buttons */
    grid-template-columns: repeat(3, 1fr) !important; /* 3 equal columns - forced */
    gap: 10px;                      /* Space between controls */
    width: 100%;                    /* Full width */
}

.responsive-adjustments .adjustments-buttons {
    display: grid !important;       /* Grid layout for responsive buttons */
    grid-template-columns: repeat(3, 1fr) !important; /* 3 equal columns - forced */
    gap: 10px;                      /* Space between controls */
    width: 100%;                    /* Full width */
}

/* ===== ESTILOS DE LA VISTA PREVIA ===== */
.tools-preview {
    margin-top: 20px;               /* Espacio superior */
    display: none;                  /* Oculto por defecto */
}

.preview-canvas {
    width: 100%;                    /* Ancho completo */
    max-width: 200px;               /* Ancho máximo */
    height: auto;                   /* Alto automático */
    border: 1px solid rgba(255, 255, 255, 0.2); /* Borde sutil */
    border-radius: 8px;             /* Bordes redondeados */
    background: #2d3748;            /* Fondo azul oscuro */
    margin: 0 auto;                 /* Centra el canvas */
    display: block;                 /* Display block para centrado */
}

/* ===== RESPONSIVE PARA TABLETS (768px - 1024px) ===== */
@media (max-width: 1024px) and (min-width: 769px) {
    /* ===== CONTENEDOR PRINCIPAL EN TABLET ===== */
    .banner-container {
        max-width: calc(100% - 20px); /* Usa casi todo el ancho con margen mínimo */
        padding: 15px;               /* Reduce padding interno */
        margin: 20px 10px;           /* Margen pequeño con los laterales */
    }

    /* ===== TÍTULO EN TABLET ===== */
    .banner-external-title {
        font-size: 1.4rem;           /* Más pequeño para tablet - igual que estadísticas */
        margin: 20px auto 8px auto;  /* Ajusta márgenes para tablet */
        max-width: calc(100% - 20px); /* Mismo ancho que contenedor */
        padding: 0 10px;             /* Padding lateral */
    }

    /* ===== OPCIONES DE RADIO EN TABLET ===== */
    .radio-label {
        font-size: 0.75rem;          /* Reduce tamaño de fuente - igual que marca */
    }

    .radio-circle {
        width: 16px;                 /* Círculo más pequeño - igual que marca */
        height: 16px;                /* Círculo más pequeño - igual que marca */
    }

    .radio-option input[type="radio"]:checked + .radio-circle::after {
        width: 5px;                  /* Punto más pequeño - igual que marca */
        height: 5px;                 /* Punto más pequeño - igual que marca */
    }

    /* ===== CONTENIDO EN TABLET - COLUMNAS APILADAS ===== */
    .banner-content {
        grid-template-columns: 1fr;  /* Una sola columna en tablet */
        gap: 20px;                   /* Reduce espacio entre elementos */
    }

    /* ===== HERRAMIENTAS EN TABLET ===== */
    .tools-grid,
    .tools-grid-row1 {
        grid-template-columns: repeat(4, 1fr); /* 4 columnas en primera fila */
        gap: 6px;                    /* Reduce espacio entre botones */
        margin-bottom: 8px;          /* Reduce espacio debajo */
    }

    .tools-grid-row2 {
        gap: 6px;                    /* Reduce espacio entre botones de segunda fila */
    }

    .tool-btn {
        padding: 8px 4px;            /* Reduce padding */
        min-height: 40px;            /* Reduce altura mínima */
    }

    .tool-icon {
        font-size: 0.9rem;           /* Reduce tamaño del icono */
    }

    .tool-label {
        font-size: 0.6rem;           /* Reduce tamaño del texto */
    }

    /* ===== AJUSTES EN TABLET ===== */
    .desktop-adjustments {
        display: none !important;    /* Hide sliders in tablet */
    }

    .responsive-adjustments {
        display: block !important;   /* Show responsive elements in tablet */
    }

    .responsive-adjustments *,
    .adjustments-buttons,
    .adjustments-buttons *,
    .adjustment-button-control,
    .adjustment-display {
        display: revert !important;  /* Allow responsive elements to show */
    }

    .responsive-adjustments.adjustments-buttons,
    .adjustments-buttons {
        display: grid !important;    /* Force grid layout */
        grid-template-columns: repeat(3, 1fr) !important; /* 3 columns side by side */
        gap: 10px;                   /* Space between controls */
        width: 100%;                 /* Full width */
    }

    .adjustment-label {
        font-size: 0.75rem;          /* Reduce tamaño de fuente */
    }

    .adjustment-btn {
        width: 28px;                 /* Tamaño de botones para tablet */
        height: 28px;                /* Tamaño de botones para tablet */
        font-size: 0.9rem;           /* Tamaño de fuente para tablet */
    }

    .adjustment-display {
        font-size: 0.75rem;          /* Tamaño de fuente para tablet */
        min-width: 40px;             /* Ancho mínimo para tablet */
        padding: 2px 4px;            /* Padding reducido */
    }

    /* Force uniform order in tablet mode - Label → Buttons → Percentage */
    .responsive-adjustments .adjustment-button-control .adjustment-label {
        order: 1 !important;         /* Label at top */
    }

    .responsive-adjustments .adjustment-button-control .adjustment-buttons {
        order: 2 !important;         /* Buttons in middle */
    }

    .responsive-adjustments .adjustment-button-control .adjustment-display {
        order: 3 !important;         /* Percentage at bottom */
        display: block !important;   /* Show percentage values */
        background: none !important; /* Remove background styling */
        border: none !important;     /* Remove border styling */
        padding: 0 !important;       /* Remove padding */
        border-radius: 0 !important; /* Remove border radius */
    }

    /* ===== VISTA PREVIA OCULTA EN TABLET ===== */
    .tools-preview {
        display: none;               /* Oculta la vista previa en tablet */
    }

    /* ===== COLUMNAS EN TABLET ===== */
    .banner-column {
        padding: 20px;               /* Reduce padding interno */
    }

    .column-title {
        font-size: 1.2rem;           /* Reduce tamaño del título */
        margin-bottom: 15px;         /* Reduce margen inferior */
    }

    /* ===== TARJETAS DE IMAGEN EN TABLET ===== */
    .image-upload-card {
        height: 180px;               /* Reduce altura */
        margin-bottom: 15px;         /* Reduce margen inferior */
    }

    .image-placeholder-icon {
        font-size: 2.5rem;           /* Reduce tamaño del icono */
    }

    .image-placeholder-text {
        font-size: 0.85rem;          /* Reduce tamaño del texto */
    }
}

/* ===== RESPONSIVE PARA MÓVILES (≤768px) ===== */
@media (max-width: 768px) {
    /* ===== OCULTAR CONTAINERS POR DEFECTO EN RESPONSIVE ===== */
    .banner-container.responsive-hidden,
    .banner-external-title.responsive-hidden {
        display: none;               /* Oculto por defecto en responsive */
    }

    /* ===== CONTENEDOR PRINCIPAL EN MÓVIL ===== */
    .banner-container {
        max-width: calc(100% - 16px); /* Usa casi todo el ancho con margen mínimo */
        padding: 12px;               /* Padding mínimo */
        margin: 15px 8px;            /* Margen pequeño */
    }

    /* ===== TÍTULO EN MÓVIL ===== */
    .banner-external-title {
        font-size: 1.8rem;           /* Título más pequeño - igual que marca */
        text-align: center;          /* Centra el título - igual que marca */
        margin: 15px auto 15px auto; /* Margen igual que marca */
        max-width: calc(100% - 16px); /* Mismo ancho que contenedor */
        padding: 0 8px;              /* Padding lateral */
    }

    /* ===== OPCIONES DE RADIO EN MÓVIL ===== */
    .radio-label {
        font-size: 0.75rem;          /* Texto más pequeño - igual que marca */
    }

    .radio-circle {
        width: 14px;                 /* Círculo más pequeño - igual que marca */
        height: 14px;                /* Círculo más pequeño - igual que marca */
    }

    .radio-option input[type="radio"]:checked + .radio-circle::after {
        width: 4px;                  /* Punto más pequeño - igual que marca */
        height: 4px;                 /* Punto más pequeño - igual que marca */
    }

    /* ===== HEADER EN MÓVIL ===== */
    .banner-title {
        font-size: 1.8rem;           /* Título más pequeño */
        text-align: center;          /* Centra el título */
        margin-bottom: 15px;         /* Margen inferior */
    }

    /* ===== CONTENIDO EN MÓVIL ===== */
    .banner-content {
        grid-template-columns: 1fr;  /* Una sola columna en móvil */
        gap: 15px;                   /* Reduce espacio entre elementos */
    }

    /* ===== COLUMNAS EN MÓVIL ===== */
    .banner-column {
        padding: 15px;               /* Reduce padding interno */
        border-radius: 8px;          /* Reduce bordes redondeados */
    }

    .column-title {
        font-size: 1.1rem;           /* Reduce más el tamaño del título */
        margin-bottom: 12px;         /* Reduce margen inferior */
    }

    /* ===== TARJETAS DE IMAGEN EN MÓVIL ===== */
    .image-upload-card {
        height: 150px;               /* Reduce altura */
        margin-bottom: 12px;         /* Reduce margen inferior */
        border-radius: 6px;          /* Reduce bordes redondeados */
    }

    .image-placeholder-icon {
        font-size: 2rem;             /* Reduce tamaño del icono */
    }

    .image-placeholder-text {
        font-size: 0.8rem;           /* Reduce tamaño del texto */
    }

    /* ===== INFORMACIÓN DEL FORMATO EN MÓVIL ===== */
    .banner-format-info {
        margin-bottom: 15px;         /* Reduce margen inferior */
        padding: 8px;                /* Reduce padding */
    }

    .format-info-text {
        font-size: 0.8rem;           /* Reduce tamaño de fuente */
    }

    /* ===== BOTONES EN MÓVIL ===== */
    .action-buttons {
        flex-direction: row !important; /* Mantiene fila en móvil - forzado */
        gap: 6px;                    /* Espacio muy reducido entre botones */
        justify-content: center !important; /* Centra los botones - forzado */
        align-items: center !important; /* Alinea verticalmente - forzado */
        flex-wrap: nowrap !important; /* No permite salto de línea - forzado */
    }

    .btn-save,
    .btn-cancel,
    .btn-change-image {
        flex: 0 0 auto !important;   /* No se expanden - forzado */
        padding: 6px 8px !important; /* Padding más razonable - forzado */
        min-width: 45px !important;  /* Ancho mínimo razonable - forzado */
        max-width: 80px !important;  /* Ancho máximo razonable - forzado */
        font-size: 0.75rem !important; /* Fuente legible - forzado */
        width: auto !important;      /* Ancho automático - forzado */
    }

    .btn-change-image {
        font-size: 0.9rem;           /* Emoji un poco más grande */
    }

    /* ===== HERRAMIENTAS EN MÓVIL ===== */
    .tools-grid,
    .tools-grid-row1 {
        grid-template-columns: repeat(4, 1fr); /* 4 columnas en primera fila */
        gap: 4px;                    /* Reduce espacio entre botones */
        margin-bottom: 6px;          /* Reduce espacio debajo */
    }

    .tools-grid-row2 {
        gap: 4px;                    /* Reduce espacio entre botones de segunda fila */
    }

    .tool-btn {
        padding: 6px 3px;            /* Reduce padding */
        min-height: 35px;            /* Reduce altura mínima */
    }

    .tool-icon {
        font-size: 0.8rem;           /* Reduce tamaño del icono */
    }

    .tool-label {
        font-size: 0.55rem;          /* Reduce tamaño del texto */
    }

    /* ===== AJUSTES EN MÓVIL ===== */
    .desktop-adjustments {
        display: none !important;    /* Hide sliders in mobile */
    }

    .responsive-adjustments {
        display: block !important;   /* Show responsive elements in mobile */
    }

    .responsive-adjustments *,
    .adjustments-buttons,
    .adjustments-buttons *,
    .adjustment-button-control,
    .adjustment-display {
        display: revert !important;  /* Allow responsive elements to show */
    }

    .responsive-adjustments.adjustments-buttons,
    .adjustments-buttons {
        display: grid !important;    /* Force grid layout */
        grid-template-columns: repeat(3, 1fr) !important; /* 3 columns side by side */
        gap: 8px;                    /* Space between controls */
        width: 100%;                 /* Full width */
    }

    .adjustment-button-control {
        gap: 4px;                    /* Reduce espacio entre elementos - igual que marca */
    }

    .adjustment-btn {
        width: 26px;                 /* Tamaño de botones para móvil */
        height: 26px;                /* Tamaño de botones para móvil */
        font-size: 0.8rem;           /* Tamaño de fuente para móvil */
    }

    .adjustment-display {
        font-size: 0.7rem;           /* Tamaño de fuente para móvil */
        min-width: 35px;             /* Ancho mínimo para móvil */
        padding: 1px 3px;            /* Padding mínimo */
    }

    /* Force uniform order in mobile mode - Label → Buttons → Percentage */
    .responsive-adjustments .adjustment-button-control .adjustment-label {
        order: 1 !important;         /* Label at top */
    }

    .responsive-adjustments .adjustment-button-control .adjustment-buttons {
        order: 2 !important;         /* Buttons in middle */
    }

    .responsive-adjustments .adjustment-button-control .adjustment-display {
        order: 3 !important;         /* Percentage at bottom */
        display: block !important;   /* Show percentage values */
        background: none !important; /* Remove background styling */
        border: none !important;     /* Remove border styling */
        padding: 0 !important;       /* Remove padding */
        border-radius: 0 !important; /* Remove border radius */
    }

    .tools-section-title {
        font-size: 0.9rem;           /* Reduce tamaño del título */
        margin-bottom: 12px;         /* Reduce margen */
    }

    .adjustment-control {
        gap: 6px;                    /* Reduce espacio entre elementos */
    }

    .adjustment-label {
        font-size: 0.8rem;           /* Reduce tamaño de fuente */
    }

    .adjustment-slider {
        height: 5px;                 /* Reduce altura del slider */
    }

    .adjustment-slider::-webkit-slider-thumb {
        width: 16px;                 /* Reduce tamaño del thumb */
        height: 16px;                /* Reduce tamaño del thumb */
    }

    .adjustment-slider::-moz-range-thumb {
        width: 16px;                 /* Reduce tamaño del thumb */
        height: 16px;                /* Reduce tamaño del thumb */
    }

    /* ===== VISTA PREVIA OCULTA EN MÓVIL ===== */
    .tools-preview {
        display: none;               /* Oculta la vista previa en móvil */
    }

    /* ===== BOTÓN CAMBIAR IMAGEN EN MÓVIL ===== */
    .btn-change-image {
        padding: 6px 12px;           /* Reduce padding */
        font-size: 0.8rem;           /* Reduce tamaño de fuente */
        min-width: 100px;            /* Reduce ancho mínimo */
    }
}

/* ===== RESPONSIVE PARA MÓVILES PEQUEÑOS (≤480px) ===== */
@media (max-width: 480px) {
    /* ===== CONTENEDOR PRINCIPAL EN MÓVIL PEQUEÑO ===== */
    .banner-container {
        max-width: calc(100% - 12px); /* Usa casi todo el ancho */
        padding: 10px;               /* Padding mínimo */
        margin: 12px 6px;            /* Margen mínimo */
    }

    /* ===== TÍTULO EN MÓVIL PEQUEÑO ===== */
    .banner-external-title {
        font-size: 1.5rem;           /* Título más pequeño - igual que marca */
        margin: 12px auto 12px auto; /* Margen reducido - igual que marca */
        max-width: calc(100% - 12px); /* Mismo ancho que contenedor */
        padding: 0 6px;              /* Padding lateral */
    }

    /* ===== OPCIONES DE RADIO EN MÓVIL PEQUEÑO ===== */
    .radio-label {
        font-size: 0.75rem;          /* Texto más pequeño - igual que marca */
    }

    .radio-circle {
        width: 14px;                 /* Círculo más pequeño - igual que marca */
        height: 14px;                /* Círculo más pequeño - igual que marca */
    }

    .radio-option input[type="radio"]:checked + .radio-circle::after {
        width: 4px;                  /* Punto más pequeño - igual que marca */
        height: 4px;                 /* Punto más pequeño - igual que marca */
    }

    /* ===== BOTONES EN MÓVIL PEQUEÑO ===== */
    .action-buttons {
        gap: 4px;                    /* Espacio mínimo entre botones */
    }

    .btn-save,
    .btn-cancel,
    .btn-change-image {
        padding: 5px 6px !important; /* Padding razonable - forzado */
        font-size: 0.7rem !important; /* Fuente legible - forzado */
        min-width: 40px !important;  /* Ancho mínimo razonable - forzado */
        max-width: 70px !important;  /* Ancho máximo razonable - forzado */
        width: auto !important;      /* Ancho automático - forzado */
    }
}

/* ===== RESPONSIVE PARA MÓVILES MUY PEQUEÑOS (≤360px) ===== */
@media (max-width: 360px) {
    /* ===== CONTENEDOR PRINCIPAL EN MÓVIL MUY PEQUEÑO ===== */
    .banner-container {
        max-width: calc(100% - 8px);  /* Usa casi todo el ancho */
        padding: 8px;                /* Padding mínimo absoluto */
        margin: 10px 4px;            /* Margen mínimo absoluto */
    }

    /* ===== TÍTULO EN MÓVIL MUY PEQUEÑO ===== */
    .banner-external-title {
        font-size: 1.3rem;           /* Título mínimo - igual que marca */
        margin: 10px auto 10px auto; /* Margen mínimo - igual que marca */
        max-width: calc(100% - 8px);  /* Mismo ancho que contenedor */
        padding: 0 4px;              /* Padding lateral */
    }

    /* ===== OPCIONES DE RADIO EN MÓVIL MUY PEQUEÑO ===== */
    .radio-label {
        font-size: 0.75rem;          /* Texto mínimo - igual que marca */
    }

    .radio-circle {
        width: 14px;                 /* Círculo mínimo - igual que marca */
        height: 14px;                /* Círculo mínimo - igual que marca */
    }

    .radio-option input[type="radio"]:checked + .radio-circle::after {
        width: 4px;                  /* Punto mínimo - igual que marca */
        height: 4px;                 /* Punto mínimo - igual que marca */
    }

    /* ===== BOTONES EN MÓVIL MUY PEQUEÑO ===== */
    .action-buttons {
        gap: 3px;                    /* Espacio mínimo entre botones */
    }

    .btn-save,
    .btn-cancel,
    .btn-change-image {
        padding: 4px 5px !important; /* Padding mínimo pero usable - forzado */
        font-size: 0.65rem !important; /* Fuente mínima pero legible - forzado */
        min-width: 35px !important;  /* Ancho mínimo usable - forzado */
        max-width: 60px !important;  /* Ancho máximo razonable - forzado */
        width: auto !important;      /* Ancho automático - forzado */
    }
}
