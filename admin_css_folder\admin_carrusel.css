/* ===== ESTILOS DEL CONTENEDOR DE CARRUSELES DE IMÁGENES ===== */
.carruseles-container {
    max-width: 1200px;           /* Ancho máximo igual al contenedor de productos */
    margin: 30px auto;           /* Centra el contenedor con margen superior */
    padding: 20px;               /* Espaciado interno del contenedor */
    background-color: #2d3748;   /* Fondo azul oscuro igual que otros contenedores */
}

/* ===== ESTILOS DEL HEADER DE CARRUSELES ===== */
.carruseles-header {
    margin-bottom: 30px;         /* Espacio debajo del header */
}

.carruseles-title {
    font-size: 2.5rem;           /* Tamaño grande para el título principal */
    font-weight: bold;           /* Texto en negrita */
    color: #5b21b6;              /* Color morado oscuro igual que otros títulos */
    margin-bottom: 8px;          /* Espacio pequeño debajo del título */
    text-align: left;            /* Alineación a la izquierda */
}

/* ===== ESTILOS DE LAS PESTAÑAS DE CARRUSELES ===== */
.carrusel-tabs-container {
    margin-bottom: 30px;         /* Espacio debajo de las pestañas */
}

.carrusel-tabs-nav {
    display: flex;               /* Flexbox para las pestañas horizontalmente */
    gap: 5px;                    /* Espacio pequeño entre pestañas */
    border-bottom: 2px solid #1a202c; /* Línea debajo de las pestañas */
    margin-bottom: 20px;         /* Espacio debajo de la navegación */
    justify-content: flex-start; /* Alinea las pestañas a la izquierda */
}

.carrusel-tab-button {
    background: #1a202c;         /* Fondo azul oscuro igual que otros botones */
    color: rgba(255,255,255,0.7); /* Color blanco semi-transparente */
    border: none;                /* Sin borde */
    padding: 12px 20px;          /* Espaciado interno de las pestañas */
    cursor: pointer;             /* Cursor de mano al pasar sobre la pestaña */
    border-radius: 8px 8px 0 0;  /* Bordes redondeados solo arriba */
    font-size: 0.9rem;           /* Tamaño de fuente de las pestañas */
    font-weight: 500;            /* Peso medio de la fuente */
    transition: all 0.3s ease;   /* Transición suave para efectos */
    border-left: 4px solid transparent; /* Borde izquierdo transparente por defecto */
    min-width: 120px;            /* Ancho mínimo para las pestañas */
}

.carrusel-tab-button:hover {
    background: #2d3748;         /* Fondo más claro al pasar el mouse */
    color: #ffffff;              /* Color blanco completo al hover */
    transform: translateY(-2px); /* Eleva ligeramente la pestaña */
}

.carrusel-tab-button.active {
    background: #2d3748;         /* Fondo más claro para pestaña activa */
    color: #ffffff;              /* Color blanco completo para pestaña activa */
    border-left-color: #6b46c1;  /* Borde morado para pestaña activa */
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3); /* Sombra para pestaña activa */
}

/* ===== ESTILOS DEL CONTENIDO DE LAS PESTAÑAS ===== */
.carrusel-tab-content {
    display: none;               /* Oculta el contenido por defecto */
}

.carrusel-tab-content.active {
    display: block;              /* Muestra el contenido de la pestaña activa */
}

/* ===== ESTILOS DEL GRID DE TARJETAS ===== */
.carrusel-grid {
    display: grid;               /* Grid layout para las tarjetas */
    grid-template-columns: repeat(3, 1fr); /* 3 columnas iguales en desktop */
    grid-template-rows: repeat(2, 1fr);    /* 2 filas iguales en desktop */
    gap: 15px;                   /* Espacio entre tarjetas */
    width: 100%;                 /* Ancho completo del contenedor */
    height: 400px;               /* Altura fija para mantener proporción */
}

/* ===== ESTILOS DE LAS TARJETAS DE CARRUSEL ===== */
.carrusel-card {
    background: #1a202c;         /* Fondo azul oscuro igual que otras tarjetas */
    border-radius: 8px;          /* Bordes redondeados */
    border-left: 4px solid #6b46c1; /* Borde morado igual que otras tarjetas */
    cursor: pointer;             /* Cursor de mano */
    transition: all 0.3s ease;   /* Transición suave para efectos */
    position: relative;          /* Posición relativa para elementos absolutos */
    overflow: hidden;            /* Oculta desbordamiento */
    display: flex;               /* Flexbox para centrar contenido */
    align-items: center;         /* Centra verticalmente */
    justify-content: center;     /* Centra horizontalmente */
    background-size: cover;      /* Cubre todo el contenedor manteniendo proporción */
    background-position: center; /* Centra la imagen */
    background-repeat: no-repeat; /* No repite la imagen */
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3); /* Sombra de las tarjetas */
}

.carrusel-card:hover {
    transform: translateY(-5px); /* Eleva la tarjeta al pasar el mouse */
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.4); /* Sombra más pronunciada */
}

/* ===== ESTILOS DEL NÚMERO DE TARJETA ===== */
.carrusel-card-number {
    position: absolute;          /* Posición absoluta */
    top: 10px;                   /* Desde arriba */
    right: 10px;                 /* Desde la derecha */
    background: #6b46c1;         /* Fondo morado igual que el header */
    color: #ffffff;              /* Texto blanco */
    width: 30px;                 /* Ancho del círculo */
    height: 30px;                /* Alto del círculo */
    border-radius: 50%;          /* Círculo perfecto */
    display: flex;               /* Flexbox para centrar */
    align-items: center;         /* Centra verticalmente */
    justify-content: center;     /* Centra horizontalmente */
    font-size: 0.8rem;           /* Tamaño de fuente */
    font-weight: 600;            /* Peso semi-bold */
    z-index: 10;                 /* Z-index alto para estar encima */
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3); /* Sombra del número */
}

/* ===== ESTILOS DEL PLACEHOLDER DE IMAGEN ===== */
.carrusel-placeholder {
    color: rgba(255, 255, 255, 0.6); /* Color gris claro */
    font-size: 0.9rem;           /* Tamaño de fuente */
    text-align: center;          /* Centra el texto */
    display: flex;               /* Flexbox para organizar contenido */
    flex-direction: column;      /* Organiza verticalmente */
    align-items: center;         /* Centra horizontalmente */
    gap: 10px;                   /* Espacio entre elementos */
}

.carrusel-placeholder-icon {
    font-size: 3rem;             /* Tamaño más grande para el signo + */
    color: #6b46c1;              /* Color morado del icono */
    font-weight: 300;            /* Peso ligero para que se vea más elegante */
    line-height: 1;              /* Altura de línea para mejor centrado */
}

.carrusel-placeholder-text {
    font-weight: 500;            /* Peso medio */
}

/* ===== ESTILOS PARA TARJETAS CON IMAGEN ===== */
.carrusel-card.has-image {
    background-size: cover !important;      /* Fuerza cover para imágenes */
    background-position: center !important; /* Fuerza centrado para imágenes */
    background-repeat: no-repeat !important; /* Evita repetición */
}

.carrusel-card.has-image .carrusel-placeholder {
    display: none;               /* Oculta el placeholder cuando hay imagen */
}

/* ===== RESPONSIVE PARA TABLETS (768px - 1024px) ===== */
@media (max-width: 1024px) and (min-width: 769px) {
    /* ===== CONTENEDOR PRINCIPAL EN TABLET ===== */
    .carruseles-container {
        max-width: calc(100% - 20px);  /* Usa casi todo el ancho con margen mínimo */
        padding: 15px;                 /* Reduce padding interno */
        margin: 20px 10px;             /* Margen pequeño con los laterales */
    }

    /* ===== PESTAÑAS EN TABLET ===== */
    .carrusel-tabs-nav {
        justify-content: flex-start;   /* Alinea las pestañas a la izquierda */
        gap: 8px;                      /* Espacio entre pestañas */
    }

    .carrusel-tab-button {
        font-size: 0.85rem;            /* Tamaño de fuente más pequeño */
        padding: 10px 15px;            /* Padding reducido */
        min-width: 100px;              /* Ancho mínimo reducido */
    }

    /* ===== GRID EN TABLET - 3 FILAS DE 2 ===== */
    .carrusel-grid {
        grid-template-columns: repeat(2, 1fr); /* 2 columnas en tablet */
        grid-template-rows: repeat(3, 1fr);    /* 3 filas en tablet */
        height: 450px;                 /* Altura ajustada para tablet */
        gap: 12px;                     /* Espacio reducido entre tarjetas */
    }

    /* ===== TARJETAS EN TABLET ===== */
    .carrusel-card-number {
        width: 28px;                   /* Círculo más pequeño */
        height: 28px;                  /* Círculo más pequeño */
        font-size: 0.75rem;            /* Fuente más pequeña */
        top: 8px;                      /* Posición ajustada */
        right: 8px;                    /* Posición ajustada */
    }

    .carrusel-placeholder-icon {
        font-size: 2.5rem;             /* Signo + más pequeño para tablet */
    }

    .carrusel-placeholder-text {
        font-size: 0.85rem;            /* Texto más pequeño */
    }
}

/* ===== RESPONSIVE PARA MÓVILES (≤768px) ===== */
@media (max-width: 768px) {
    /* ===== OCULTAR CONTAINERS POR DEFECTO EN RESPONSIVE ===== */
    .carruseles-container.responsive-hidden {
        display: none;               /* Oculto por defecto en responsive */
    }

    /* ===== CONTENEDOR PRINCIPAL EN MÓVIL ===== */
    .carruseles-container {
        max-width: calc(100% - 16px);  /* Usa casi todo el ancho con margen mínimo */
        padding: 12px;                 /* Padding mínimo */
        margin: 15px 8px;              /* Margen pequeño */
    }

    /* ===== HEADER EN MÓVIL ===== */
    .carruseles-title {
        font-size: 1.8rem;             /* Título más pequeño */
        text-align: center;            /* Centra el título */
        margin-bottom: 15px;           /* Margen inferior */
    }

    /* ===== PESTAÑAS EN MÓVIL ===== */
    .carrusel-tabs-nav {
        justify-content: flex-start;   /* Alinea las pestañas a la izquierda */
        gap: 5px;                      /* Espacio mínimo entre pestañas */
        margin-bottom: 15px;           /* Margen inferior reducido */
    }

    .carrusel-tab-button {
        font-size: 0.8rem;             /* Fuente más pequeña */
        padding: 8px 12px;             /* Padding reducido */
        min-width: 80px;               /* Ancho mínimo reducido */
        border-radius: 6px 6px 0 0;    /* Bordes menos redondeados */
    }

    /* ===== GRID EN MÓVIL - 3 FILAS DE 2 ===== */
    .carrusel-grid {
        grid-template-columns: repeat(2, 1fr); /* 2 columnas en móvil */
        grid-template-rows: repeat(3, 1fr);    /* 3 filas en móvil */
        height: 360px;                 /* Altura reducida para móvil */
        gap: 10px;                     /* Espacio mínimo entre tarjetas */
    }

    /* ===== TARJETAS EN MÓVIL ===== */
    .carrusel-card {
        border-radius: 6px;            /* Bordes menos redondeados */
    }

    .carrusel-card-number {
        width: 24px;                   /* Círculo más pequeño */
        height: 24px;                  /* Círculo más pequeño */
        font-size: 0.7rem;             /* Fuente más pequeña */
        top: 6px;                      /* Posición ajustada */
        right: 6px;                    /* Posición ajustada */
    }

    .carrusel-placeholder-icon {
        font-size: 2rem;               /* Signo + más pequeño para móvil */
    }

    .carrusel-placeholder-text {
        font-size: 0.75rem;            /* Texto más pequeño */
    }
}

/* ===== RESPONSIVE PARA MÓVILES PEQUEÑOS (≤480px) ===== */
@media (max-width: 480px) {
    /* ===== CONTENEDOR PRINCIPAL EN MÓVIL PEQUEÑO ===== */
    .carruseles-container {
        max-width: calc(100% - 12px);  /* Usa casi todo el ancho */
        padding: 10px;                 /* Padding mínimo */
        margin: 12px 6px;              /* Margen mínimo */
    }

    /* ===== HEADER EN MÓVIL PEQUEÑO ===== */
    .carruseles-title {
        font-size: 1.5rem;             /* Título más pequeño */
        margin-bottom: 12px;           /* Margen reducido */
    }

    /* ===== PESTAÑAS EN MÓVIL PEQUEÑO ===== */
    .carrusel-tab-button {
        font-size: 0.75rem;            /* Fuente mínima */
        padding: 6px 10px;             /* Padding mínimo */
        min-width: 70px;               /* Ancho mínimo */
    }

    /* ===== GRID EN MÓVIL PEQUEÑO ===== */
    .carrusel-grid {
        height: 300px;                 /* Altura mínima */
        gap: 8px;                      /* Espacio mínimo */
    }

    /* ===== TARJETAS EN MÓVIL PEQUEÑO ===== */
    .carrusel-card-number {
        width: 22px;                   /* Círculo mínimo */
        height: 22px;                  /* Círculo mínimo */
        font-size: 0.65rem;            /* Fuente mínima */
        top: 5px;                      /* Posición ajustada */
        right: 5px;                    /* Posición ajustada */
    }

    .carrusel-placeholder-icon {
        font-size: 1.8rem;             /* Signo + mínimo para móvil pequeño */
    }

    .carrusel-placeholder-text {
        font-size: 0.7rem;             /* Texto mínimo */
    }
}

/* ===== RESPONSIVE PARA MÓVILES MUY PEQUEÑOS (≤360px) ===== */
@media (max-width: 360px) {
    /* ===== CONTENEDOR PRINCIPAL EN MÓVIL MUY PEQUEÑO ===== */
    .carruseles-container {
        max-width: calc(100% - 8px);   /* Usa casi todo el ancho */
        padding: 8px;                  /* Padding mínimo absoluto */
        margin: 10px 4px;              /* Margen mínimo absoluto */
    }

    /* ===== HEADER EN MÓVIL MUY PEQUEÑO ===== */
    .carruseles-title {
        font-size: 1.3rem;             /* Título mínimo */
        margin-bottom: 10px;           /* Margen mínimo */
    }

    /* ===== PESTAÑAS EN MÓVIL MUY PEQUEÑO ===== */
    .carrusel-tab-button {
        font-size: 0.7rem;             /* Fuente mínima */
        padding: 5px 8px;              /* Padding mínimo */
        min-width: 60px;               /* Ancho mínimo */
    }

    /* ===== GRID EN MÓVIL MUY PEQUEÑO ===== */
    .carrusel-grid {
        height: 280px;                 /* Altura mínima */
        gap: 6px;                      /* Espacio mínimo */
    }

    /* ===== TARJETAS EN MÓVIL MUY PEQUEÑO ===== */
    .carrusel-card-number {
        width: 20px;                   /* Círculo mínimo */
        height: 20px;                  /* Círculo mínimo */
        font-size: 0.6rem;             /* Fuente mínima */
        top: 4px;                      /* Posición ajustada */
        right: 4px;                    /* Posición ajustada */
    }

    .carrusel-placeholder-icon {
        font-size: 1.5rem;             /* Signo + mínimo para móvil muy pequeño */
    }

    .carrusel-placeholder-text {
        font-size: 0.65rem;            /* Texto mínimo */
    }
}
