/* ===== ESTILOS DEL CONTENEDOR DE CONFIGURACIÓN DE MARCA ===== */
.marca-container {
    max-width: 1200px;           /* Ancho máximo igual a otros contenedores */
    margin: 30px auto;           /* Centra el contenedor con margen superior */
    padding: 20px;               /* Espaciado interno del contenedor */
    background-color: #2d3748;   /* Fondo azul oscuro igual que otros contenedores */
}

/* ===== ESTILOS DEL HEADER DE MARCA ===== */
.marca-header {
    margin-bottom: 30px;         /* Espacio debajo del header */
}

.marca-title {
    font-size: 2.5rem;           /* Tamaño grande para el título principal */
    font-weight: bold;           /* Texto en negrita */
    color: #5b21b6;              /* Color morado oscuro igual que otros títulos */
    margin-bottom: 8px;          /* Espacio pequeño debajo del título */
    text-align: left;            /* Alineación a la izquierda */
}

/* ===== ESTILOS DEL CONTENIDO PRINCIPAL ===== */
.marca-content {
    display: grid;               /* Grid layout para las columnas */
    grid-template-columns: 1fr 1fr; /* 2 columnas iguales en desktop */
    gap: 30px;                   /* Espacio entre columnas */
    width: 100%;                 /* Ancho completo del contenedor */
}

/* ===== ESTILOS DE LAS COLUMNAS ===== */
.marca-column {
    background: #1a202c;         /* Fondo azul oscuro igual que otras tarjetas */
    border-radius: 12px;         /* Bordes redondeados */
    border-left: 4px solid #6b46c1; /* Borde morado igual que otras tarjetas */
    padding: 25px;               /* Espaciado interno */
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3); /* Sombra de las tarjetas */
}

/* ===== ESTILOS DE LOS TÍTULOS DE COLUMNA ===== */
.column-title {
    color: #ffffff;              /* Color blanco para el título */
    font-size: 1.4rem;           /* Tamaño de fuente del título */
    font-weight: 600;            /* Peso semi-bold */
    margin-bottom: 20px;         /* Espacio debajo del título */
    margin-top: 0;               /* Sin margen superior */
    text-align: center;          /* Centra el título */
}

/* ===== ESTILOS DE LAS TARJETAS DE IMAGEN ===== */
.image-upload-card {
    width: 100%;                 /* Ancho completo */
    height: 200px;               /* Altura fija */
    border: 2px dashed rgba(255, 255, 255, 0.3); /* Borde punteado */
    border-radius: 8px;          /* Bordes redondeados */
    display: flex;               /* Flexbox para centrar contenido */
    align-items: center;         /* Centra verticalmente */
    justify-content: center;     /* Centra horizontalmente */
    background: #2d3748;         /* Fondo azul oscuro */
    cursor: pointer;             /* Cursor de mano */
    transition: all 0.3s ease;   /* Transición suave */
    overflow: hidden;            /* Oculta desbordamiento */
    position: relative;          /* Posición relativa para elementos absolutos */
    background-size: cover;      /* Cubre todo el contenedor manteniendo proporción */
    background-position: center; /* Centra la imagen */
    background-repeat: no-repeat; /* No repite la imagen */
    margin-bottom: 20px;         /* Espacio debajo de la tarjeta */
}

.image-upload-card:hover {
    border-color: #6b46c1;       /* Borde morado al hover */
    background-color: rgba(107, 70, 193, 0.1); /* Fondo morado semi-transparente */
}

.image-upload-card.has-image {
    border-style: solid;         /* Borde sólido cuando tiene imagen */
    border-color: #6b46c1;       /* Borde morado cuando tiene imagen */
}

/* ===== ESTILOS DEL PLACEHOLDER DE IMAGEN ===== */
.image-placeholder {
    display: flex;               /* Flexbox para organizar contenido */
    flex-direction: column;      /* Organiza verticalmente */
    align-items: center;         /* Centra horizontalmente */
    gap: 10px;                   /* Espacio entre elementos */
    color: rgba(255, 255, 255, 0.6); /* Color gris claro */
    text-align: center;          /* Centra el texto */
}

.image-placeholder-icon {
    font-size: 3rem;             /* Tamaño grande del signo + */
    color: #6b46c1;              /* Color morado del icono */
    font-weight: 300;            /* Peso ligero para que se vea más elegante */
    line-height: 1;              /* Altura de línea para mejor centrado */
}

.image-placeholder-text {
    font-size: 0.9rem;           /* Tamaño del texto */
    font-weight: 500;            /* Peso medio */
}

/* ===== ESTILOS PARA TARJETAS CON IMAGEN ===== */
.image-upload-card.has-image .image-placeholder {
    display: none;               /* Oculta el placeholder cuando hay imagen */
}

/* ===== ESTILOS DEL ICONO DE EDICIÓN ===== */
.edit-overlay {
    position: absolute;          /* Posición absoluta */
    top: 0;                      /* Desde arriba */
    left: 0;                     /* Desde la izquierda */
    width: 100%;                 /* Ancho completo */
    height: 100%;                /* Alto completo */
    background: rgba(0, 0, 0, 0.7); /* Fondo negro semi-transparente */
    display: none;               /* Oculto por defecto */
    align-items: center;         /* Centra verticalmente */
    justify-content: center;     /* Centra horizontalmente */
    transition: all 0.3s ease;   /* Transición suave */
    z-index: 10;                 /* Z-index para estar encima de la imagen */
    border-radius: 8px;          /* Bordes redondeados igual que la tarjeta */
}

.image-upload-card.has-image:hover .edit-overlay {
    display: none !important;    /* Oculta completamente el overlay */
}

/* Asegurar que el overlay esté oculto cuando la tarjeta tiene imagen */
.image-upload-card.has-image .edit-overlay {
    display: none !important;    /* Oculta completamente el overlay */
}

.image-upload-card.has-image:hover .edit-overlay {
    display: none !important;    /* Oculta completamente el overlay */
}

.edit-icon {
    color: #ffffff;              /* Color blanco */
    font-size: 1rem;             /* Tamaño un poco más grande para mejor visibilidad */
    cursor: pointer;             /* Cursor de mano */
    transition: all 0.3s ease;   /* Transición suave */
    width: 24px;                 /* Ancho un poco más grande para mejor área de click */
    height: 24px;                /* Alto un poco más grande para mejor área de click */
    display: flex;               /* Flexbox para centrar */
    align-items: center;         /* Centra verticalmente */
    justify-content: center;     /* Centra horizontalmente */
    background: rgba(59, 130, 246, 0.8); /* Fondo azul más opaco para mejor visibilidad */
    border-radius: 4px;          /* Bordes ligeramente redondeados */
    padding: 4px;                /* Padding para área de click */
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3); /* Sombra para destacar */
    z-index: 11;                 /* Z-index alto para estar encima del overlay */
}

.edit-icon:hover {
    color: #ffffff;              /* Color blanco al hover */
    background: #3b82f6;         /* Fondo azul sólido al hover */
    transform: scale(1.1);       /* Agranda ligeramente al hover */
    box-shadow: 0 4px 8px rgba(59, 130, 246, 0.4); /* Sombra azul al hover */
}

/* ===== ESTILOS DEL BOTÓN CAMBIAR IMAGEN (LÁPIZ) ===== */
.btn-change-image {
    background: #6b46c1;         /* Fondo morado */
    color: #ffffff;              /* Texto blanco */
    border: none;                /* Sin borde */
    border-radius: 6px;          /* Bordes redondeados */
    padding: 8px 12px;           /* Espaciado interno reducido */
    font-size: 1rem;             /* Tamaño de fuente para el emoji */
    font-weight: 500;            /* Peso medio */
    cursor: pointer;             /* Cursor de mano */
    transition: all 0.3s ease;   /* Transición suave */
    min-width: 40px;             /* Ancho mínimo reducido */
    flex: 0 0 auto;              /* No crece ni se encoge */
    display: flex;               /* Flexbox para centrar */
    align-items: center;         /* Centra verticalmente */
    justify-content: center;     /* Centra horizontalmente */
}

.btn-change-image:hover {
    background: #553c9a;         /* Morado más oscuro al hover */
    transform: translateY(-1px); /* Eleva ligeramente al hover */
    box-shadow: 0 2px 4px rgba(107, 70, 193, 0.3); /* Sombra morada */
}

/* ===== ESTILOS DE LAS OPCIONES DE LOGO ===== */
.logo-options {
    display: flex !important;    /* Flexbox para organizar elementos - forzado */
    flex-direction: row !important; /* Organiza horizontalmente - forzado */
    align-items: center;         /* Centra verticalmente */
    justify-content: center;     /* Centra horizontalmente */
    gap: 20px;                   /* Espacio entre elementos */
    margin-bottom: 20px;         /* Espacio debajo de las opciones */
}

.radio-option {
    display: flex;               /* Flexbox para organizar elementos */
    align-items: center;         /* Centra verticalmente */
    gap: 8px;                    /* Espacio entre radio y texto */
    cursor: pointer;             /* Cursor de mano */
}

.radio-option input[type="radio"] {
    display: none;               /* Oculta el radio button por defecto */
}

.radio-circle {
    width: 18px;                 /* Ancho del círculo */
    height: 18px;                /* Alto del círculo */
    border: 2px solid rgba(255, 255, 255, 0.4); /* Borde del círculo */
    border-radius: 50%;          /* Círculo perfecto */
    position: relative;          /* Posición relativa para el punto interno */
    transition: all 0.3s ease;   /* Transición suave */
    flex-shrink: 0;              /* No se encoge */
}

.radio-option input[type="radio"]:checked + .radio-circle {
    border-color: #6b46c1;       /* Borde morado cuando está seleccionado */
    background: #6b46c1;         /* Fondo morado cuando está seleccionado */
}

.radio-option input[type="radio"]:checked + .radio-circle::after {
    content: '';                 /* Contenido vacío */
    position: absolute;          /* Posición absoluta */
    top: 50%;                    /* Centra verticalmente */
    left: 50%;                   /* Centra horizontalmente */
    transform: translate(-50%, -50%); /* Ajuste fino del centrado */
    width: 6px;                  /* Ancho del punto */
    height: 6px;                 /* Alto del punto */
    background: #ffffff;         /* Color blanco del punto */
    border-radius: 50%;          /* Punto circular */
}

.radio-label {
    color: #ffffff;              /* Color blanco del texto */
    font-size: 0.9rem;           /* Tamaño de fuente */
    font-weight: 500;            /* Peso medio */
}

/* ===== ESTILOS DE LOS BOTONES ===== */
.action-buttons {
    display: flex;               /* Flexbox para organizar botones */
    gap: 10px;                   /* Espacio reducido entre botones */
    justify-content: center;     /* Centra los botones */
}

.btn-save,
.btn-cancel {
    padding: 8px 12px;           /* Espaciado interno más reducido */
    border: none;                /* Sin borde */
    border-radius: 6px;          /* Bordes redondeados */
    font-size: 0.8rem;           /* Tamaño de fuente más reducido */
    font-weight: 500;            /* Peso medio */
    cursor: pointer;             /* Cursor de mano */
    transition: all 0.3s ease;   /* Transición suave */
    min-width: 60px;             /* Ancho mínimo más reducido */
    flex: 0 0 auto;              /* No crecen ni se encogen */
}

.btn-save {
    background: #10b981;         /* Fondo verde */
    color: #ffffff;              /* Texto blanco */
    border: 2px solid #10b981;   /* Borde verde */
}

.btn-save:hover {
    background: #059669;         /* Verde más oscuro al hover */
    border-color: #059669;       /* Borde verde más oscuro */
    transform: translateY(-2px); /* Eleva al hover */
    box-shadow: 0 4px 8px rgba(16, 185, 129, 0.3); /* Sombra verde */
}

.btn-cancel {
    background: rgba(255, 255, 255, 0.1); /* Fondo semi-transparente */
    color: #ffffff;              /* Texto blanco */
    border: 2px solid rgba(255, 255, 255, 0.3); /* Borde semi-transparente */
}

.btn-cancel:hover {
    background: rgba(255, 255, 255, 0.2); /* Fondo más opaco al hover */
    border-color: rgba(255, 255, 255, 0.5); /* Borde más opaco al hover */
    transform: translateY(-2px); /* Eleva al hover */
}

/* ===== ESTILOS PARA LOGO CIRCULAR ===== */
.logo-circular .image-upload-card {
    border-radius: 50%;          /* Hace la tarjeta circular */
    width: 200px;                /* Ancho fijo para mantener proporción */
    height: 200px;               /* Alto igual al ancho para círculo perfecto */
    margin: 0 auto 20px auto;    /* Centra la tarjeta */
}

/* ===== ESTILOS DE LAS HERRAMIENTAS DE EDICIÓN ===== */
.tools-section {
    margin-bottom: 25px;         /* Espacio entre secciones */
}

.tools-section-title {
    color: #ffffff;              /* Color blanco */
    font-size: 1rem;             /* Tamaño de fuente */
    font-weight: 600;            /* Peso semi-bold */
    margin-bottom: 15px;         /* Espacio debajo del título */
    margin-top: 0;               /* Sin margen superior */
    text-align: center;          /* Centra el título */
    border-bottom: 1px solid rgba(255, 255, 255, 0.2); /* Línea separadora */
    padding-bottom: 8px;         /* Espacio debajo del texto */
}

/* ===== PRIMERA FILA - 4 OPCIONES ===== */
.tools-grid-row1 {
    display: grid;               /* Grid layout */
    grid-template-columns: repeat(4, 1fr); /* 4 columnas iguales */
    gap: 8px;                    /* Espacio entre botones */
    margin-bottom: 10px;         /* Espacio debajo de la primera fila */
}

/* ===== SEGUNDA FILA - 3 OPCIONES CENTRADAS ===== */
.tools-grid-row2 {
    display: flex;               /* Flexbox para centrar */
    justify-content: center;     /* Centra horizontalmente */
    gap: 8px;                    /* Espacio entre botones */
}

/* Mantener compatibilidad con tools-grid existente */
.tools-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 8px;
    margin-bottom: 10px;
}

.tool-btn {
    background: #2d3748;         /* Fondo azul oscuro */
    border: 1px solid rgba(255, 255, 255, 0.2); /* Borde sutil */
    border-radius: 6px;          /* Bordes redondeados más pequeños */
    padding: 8px 6px;            /* Espaciado interno reducido */
    cursor: pointer;             /* Cursor de mano */
    transition: all 0.3s ease;   /* Transición suave */
    display: flex;               /* Flexbox para organizar contenido */
    flex-direction: column;      /* Organiza verticalmente */
    align-items: center;         /* Centra horizontalmente */
    gap: 4px;                    /* Espacio reducido entre icono y texto */
    min-height: 45px;            /* Altura mínima reducida */
}

.tool-btn:hover {
    background: #4a5568;         /* Fondo más claro al hover */
    border-color: #6b46c1;       /* Borde morado al hover */
    transform: translateY(-2px); /* Eleva al hover */
    box-shadow: 0 4px 8px rgba(107, 70, 193, 0.2); /* Sombra morada */
}

.tool-btn.active {
    background: #6b46c1;         /* Fondo morado cuando está activo */
    border-color: #6b46c1;       /* Borde morado */
    color: #ffffff;              /* Texto blanco */
}

.tool-icon {
    font-size: 1rem;             /* Tamaño del icono reducido */
    color: #ffffff;              /* Color blanco */
    line-height: 1;              /* Altura de línea */
}

.tool-label {
    font-size: 0.65rem;          /* Tamaño más pequeño del texto */
    color: #ffffff;              /* Color blanco */
    font-weight: 500;            /* Peso medio */
    text-align: center;          /* Centra el texto */
    line-height: 1.1;            /* Altura de línea reducida */
}

/* ===== ESTILOS DE LOS CONTROLES DE AJUSTE ===== */
.adjustments-grid {
    display: grid;               /* Grid layout para organizar controles */
    grid-template-columns: repeat(3, 1fr); /* 3 columnas iguales para una sola fila */
    gap: 15px;                   /* Espacio entre controles */
}

.adjustment-control {
    display: flex;               /* Flexbox para organizar elementos */
    flex-direction: column;      /* Organiza verticalmente */
    gap: 6px;                    /* Espacio reducido entre elementos */
    text-align: center;          /* Centra el contenido */
}

.adjustment-label {
    color: #ffffff;              /* Color blanco */
    font-size: 0.8rem;           /* Tamaño de fuente reducido */
    font-weight: 500;            /* Peso medio */
    margin: 0;                   /* Sin margen */
    margin-bottom: 4px;          /* Pequeño margen inferior */
}

.adjustment-slider {
    width: 100%;                 /* Ancho completo */
    height: 5px;                 /* Altura del slider reducida */
    background: #4a5568;         /* Fondo gris oscuro */
    border-radius: 3px;          /* Bordes redondeados */
    outline: none;               /* Sin outline */
    cursor: pointer;             /* Cursor de mano */
    -webkit-appearance: none;    /* Elimina estilos por defecto en WebKit */
    appearance: none;            /* Elimina estilos por defecto */
}

.adjustment-slider::-webkit-slider-thumb {
    -webkit-appearance: none;    /* Elimina estilos por defecto */
    appearance: none;            /* Elimina estilos por defecto */
    width: 16px;                 /* Ancho del thumb reducido */
    height: 16px;                /* Alto del thumb reducido */
    background: #6b46c1;         /* Color morado */
    border-radius: 50%;          /* Círculo perfecto */
    cursor: pointer;             /* Cursor de mano */
    transition: all 0.3s ease;   /* Transición suave */
}

.adjustment-slider::-webkit-slider-thumb:hover {
    background: #553c9a;         /* Morado más oscuro al hover */
    transform: scale(1.1);       /* Agranda al hover */
}

.adjustment-slider::-moz-range-thumb {
    width: 16px;                 /* Ancho del thumb reducido */
    height: 16px;                /* Alto del thumb reducido */
    background: #6b46c1;         /* Color morado */
    border-radius: 50%;          /* Círculo perfecto */
    border: none;                /* Sin borde */
    cursor: pointer;             /* Cursor de mano */
    transition: all 0.3s ease;   /* Transición suave */
}

.adjustment-value {
    color: #a0aec0;              /* Color gris claro */
    font-size: 0.7rem;           /* Tamaño más pequeño */
    font-weight: 500;            /* Peso medio */
    text-align: center;          /* Centra el texto */
    min-width: 35px;             /* Ancho mínimo reducido */
    margin-top: 2px;             /* Pequeño margen superior */
}

/* ===== ESTILOS DE LOS BOTONES DE AJUSTE +/- ===== */

/* DEFAULT STATE: Desktop mode - only show sliders */
.desktop-adjustments {
    display: grid !important;    /* Show sliders by default */
}

.responsive-adjustments {
    display: none !important;    /* Hide responsive elements by default */
}

/* Force hide ALL responsive adjustment elements in desktop */
.responsive-adjustments,
.responsive-adjustments *,
.adjustments-buttons,
.adjustments-buttons *,
.adjustment-button-control,
.adjustment-display {
    display: none !important;    /* Force hide all responsive elements */
}

/* Ensure desktop mode (large screens) only shows sliders */
@media (min-width: 1025px) {
    .desktop-adjustments {
        display: grid !important;    /* Force show sliders in desktop */
    }

    .responsive-adjustments,
    .responsive-adjustments *,
    .adjustments-buttons,
    .adjustments-buttons *,
    .adjustment-button-control,
    .adjustment-display {
        display: none !important;    /* Force hide ALL responsive elements */
    }
}

/* Responsive adjustments grid layout - only active in responsive mode */
.responsive-adjustments.adjustments-buttons {
    display: grid !important;    /* Grid layout for responsive buttons */
    grid-template-columns: repeat(3, 1fr) !important; /* 3 equal columns - forced */
    gap: 10px;                   /* Space between controls */
    width: 100%;                 /* Full width */
}

.responsive-adjustments .adjustments-buttons {
    display: grid !important;    /* Grid layout for responsive buttons */
    grid-template-columns: repeat(3, 1fr) !important; /* 3 equal columns - forced */
    gap: 10px;                   /* Space between controls */
    width: 100%;                 /* Full width */
}

.adjustment-button-control {
    display: flex;               /* Flexbox para organizar elementos */
    flex-direction: column;      /* Organiza verticalmente */
    align-items: center;         /* Centra horizontalmente */
    gap: 6px;                    /* Espacio entre elementos */
    padding: 8px 4px;            /* Padding interno */
    text-align: center;          /* Centra el texto */
    min-height: 80px;            /* Altura mínima para 3 elementos */
    justify-content: space-between; /* Distribuye elementos uniformemente */
}

.adjustment-buttons {
    display: flex;               /* Flexbox para organizar botones */
    flex-direction: row;         /* Organiza horizontalmente */
    align-items: center;         /* Centra verticalmente */
    gap: 6px;                    /* Espacio entre botones */
    justify-content: center;     /* Centra los botones */
}

.adjustment-btn {
    background: #4a5568;         /* Fondo gris oscuro */
    border: 1px solid rgba(255, 255, 255, 0.2); /* Borde sutil */
    border-radius: 4px;          /* Bordes redondeados */
    color: #ffffff;              /* Texto blanco */
    width: 32px;                 /* Ancho fijo aumentado */
    height: 32px;                /* Alto fijo aumentado */
    display: flex;               /* Flexbox para centrar */
    align-items: center;         /* Centra verticalmente */
    justify-content: center;     /* Centra horizontalmente */
    cursor: pointer;             /* Cursor de mano */
    font-size: 1rem;             /* Tamaño de fuente aumentado */
    font-weight: 600;            /* Peso semi-bold */
    transition: all 0.3s ease;   /* Transición suave */
}

.adjustment-btn:hover {
    background: #6b46c1;         /* Fondo morado al hover */
    border-color: #6b46c1;       /* Borde morado al hover */
    transform: scale(1.05);      /* Agranda ligeramente al hover */
}

.adjustment-btn:active {
    transform: scale(0.95);      /* Reduce al hacer clic */
}

.adjustment-display {
    color: #ffffff;              /* Color blanco */
    font-size: 0.8rem;           /* Tamaño de fuente aumentado */
    font-weight: 500;            /* Peso medio */
    min-width: 45px;             /* Ancho mínimo aumentado */
    text-align: center;          /* Centra el texto */
    background: rgba(255, 255, 255, 0.1); /* Fondo sutil */
    padding: 2px 6px;            /* Padding interno */
    border-radius: 4px;          /* Bordes redondeados */
}

/* ===== ORDEN FORZADO PARA CONTROLES RESPONSIVE ===== */
/* Force uniform order: Label → Buttons → Percentage */
.responsive-adjustments .adjustment-button-control .adjustment-label {
    order: 1 !important;         /* Label at top - forced */
    font-weight: 600 !important; /* Bold label */
}

.responsive-adjustments .adjustment-button-control .adjustment-buttons {
    order: 2 !important;         /* Buttons in middle - forced */
}

.responsive-adjustments .adjustment-button-control .adjustment-display {
    order: 3 !important;         /* Percentage at bottom - forced */
    display: block !important;   /* Show percentage values in responsive mode */
    background: none !important; /* Remove background styling */
    border: none !important;     /* Remove border styling */
    padding: 0 !important;       /* Remove padding */
    border-radius: 0 !important; /* Remove border radius */
}

/* ===== ESTILOS DE LA VISTA PREVIA ===== */
.tools-preview {
    margin-top: 20px;            /* Espacio superior */
    display: none;               /* Oculto por defecto */
}

.preview-canvas {
    width: 100%;                 /* Ancho completo */
    max-width: 200px;            /* Ancho máximo */
    height: auto;                /* Alto automático */
    border: 1px solid rgba(255, 255, 255, 0.2); /* Borde sutil */
    border-radius: 8px;          /* Bordes redondeados */
    background: #2d3748;         /* Fondo azul oscuro */
    margin: 0 auto;              /* Centra el canvas */
    display: block;              /* Display block para centrado */
}

/* ===== RESPONSIVE PARA TABLETS (768px - 1024px) ===== */
@media (max-width: 1024px) and (min-width: 769px) {
    /* ===== CONTENEDOR PRINCIPAL EN TABLET ===== */
    .marca-container {
        max-width: calc(100% - 20px); /* Usa casi todo el ancho con margen mínimo */
        padding: 15px;               /* Reduce padding interno */
        margin: 20px 10px;           /* Margen pequeño con los laterales */
    }

    /* ===== CONTENIDO EN TABLET - COLUMNAS APILADAS ===== */
    .marca-content {
        grid-template-columns: 1fr;  /* Una sola columna en tablet */
        gap: 20px;                   /* Reduce espacio entre elementos */
    }

    /* ===== HERRAMIENTAS EN TABLET ===== */
    .tools-grid,
    .tools-grid-row1 {
        grid-template-columns: repeat(4, 1fr); /* 4 columnas en primera fila */
        gap: 6px;                    /* Reduce espacio entre botones */
        margin-bottom: 8px;          /* Reduce espacio debajo */
    }

    .tools-grid-row2 {
        gap: 6px;                    /* Reduce espacio entre botones de segunda fila */
    }

    .tool-btn {
        padding: 8px 4px;            /* Reduce padding */
        min-height: 40px;            /* Reduce altura mínima */
    }

    .tool-icon {
        font-size: 0.9rem;           /* Reduce tamaño del icono */
    }

    .tool-label {
        font-size: 0.6rem;           /* Reduce tamaño del texto */
    }

    /* ===== AJUSTES EN TABLET ===== */
    .desktop-adjustments {
        display: none !important;    /* Hide sliders in tablet */
    }

    .responsive-adjustments {
        display: block !important;   /* Show responsive elements in tablet */
    }

    .responsive-adjustments *,
    .adjustments-buttons,
    .adjustments-buttons *,
    .adjustment-button-control,
    .adjustment-display {
        display: revert !important;  /* Allow responsive elements to show */
    }

    .responsive-adjustments.adjustments-buttons,
    .adjustments-buttons {
        display: grid !important;    /* Force grid layout */
        grid-template-columns: repeat(3, 1fr) !important; /* 3 columns side by side */
        gap: 10px;                   /* Space between controls */
        width: 100%;                 /* Full width */
    }

    .adjustment-label {
        font-size: 0.75rem;          /* Reduce tamaño de fuente */
    }

    .adjustment-btn {
        width: 28px;                 /* Tamaño de botones para tablet */
        height: 28px;                /* Tamaño de botones para tablet */
        font-size: 0.9rem;           /* Tamaño de fuente para tablet */
    }

    .adjustment-display {
        font-size: 0.75rem;          /* Tamaño de fuente para tablet */
        min-width: 40px;             /* Ancho mínimo para tablet */
        padding: 2px 4px;            /* Padding reducido */
    }

    /* Force uniform order in tablet mode - Label → Buttons → Percentage */
    .responsive-adjustments .adjustment-button-control .adjustment-label {
        order: 1 !important;         /* Label at top */
    }

    .responsive-adjustments .adjustment-button-control .adjustment-buttons {
        order: 2 !important;         /* Buttons in middle */
    }

    .responsive-adjustments .adjustment-button-control .adjustment-display {
        order: 3 !important;         /* Percentage at bottom */
        display: block !important;   /* Show percentage values */
        background: none !important; /* Remove background styling */
        border: none !important;     /* Remove border styling */
        padding: 0 !important;       /* Remove padding */
        border-radius: 0 !important; /* Remove border radius */
    }

    /* ===== VISTA PREVIA OCULTA EN TABLET ===== */
    .tools-preview {
        display: none;               /* Oculta la vista previa en tablet */
    }

    /* ===== COLUMNAS EN TABLET ===== */
    .marca-column {
        padding: 20px;               /* Reduce padding interno */
    }

    .column-title {
        font-size: 1.2rem;           /* Reduce tamaño del título */
        margin-bottom: 15px;         /* Reduce margen inferior */
    }

    /* ===== TARJETAS DE IMAGEN EN TABLET ===== */
    .image-upload-card {
        height: 180px;               /* Reduce altura */
        margin-bottom: 15px;         /* Reduce margen inferior */
    }

    .image-placeholder-icon {
        font-size: 2.5rem;           /* Reduce tamaño del icono */
    }

    .image-placeholder-text {
        font-size: 0.85rem;          /* Reduce tamaño del texto */
    }

    /* ===== LOGO CIRCULAR EN TABLET ===== */
    .logo-circular .image-upload-card {
        width: 180px;                /* Reduce tamaño del logo circular */
        height: 180px;               /* Mantiene proporción circular */
    }
}

/* ===== RESPONSIVE PARA MÓVILES (≤768px) ===== */
@media (max-width: 768px) {
    /* ===== OCULTAR CONTAINERS POR DEFECTO EN RESPONSIVE ===== */
    .marca-container.responsive-hidden {
        display: none;               /* Oculto por defecto en responsive */
    }

    /* ===== CONTENEDOR PRINCIPAL EN MÓVIL ===== */
    .marca-container {
        max-width: calc(100% - 16px); /* Usa casi todo el ancho con margen mínimo */
        padding: 12px;               /* Padding mínimo */
        margin: 15px 8px;            /* Margen pequeño */
    }

    /* ===== HEADER EN MÓVIL ===== */
    .marca-title {
        font-size: 1.8rem;           /* Título más pequeño */
        text-align: center;          /* Centra el título */
        margin-bottom: 15px;         /* Margen inferior */
    }

    /* ===== CONTENIDO EN MÓVIL ===== */
    .marca-content {
        grid-template-columns: 1fr;  /* Una sola columna en móvil */
        gap: 15px;                   /* Reduce espacio entre elementos */
    }

    /* ===== COLUMNAS EN MÓVIL ===== */
    .marca-column {
        padding: 15px;               /* Reduce padding interno */
        border-radius: 8px;          /* Reduce bordes redondeados */
    }

    .column-title {
        font-size: 1.1rem;           /* Reduce más el tamaño del título */
        margin-bottom: 12px;         /* Reduce margen inferior */
    }

    /* ===== TARJETAS DE IMAGEN EN MÓVIL ===== */
    .image-upload-card {
        height: 150px;               /* Reduce altura */
        margin-bottom: 12px;         /* Reduce margen inferior */
        border-radius: 6px;          /* Reduce bordes redondeados */
    }

    .image-placeholder-icon {
        font-size: 2rem;             /* Reduce tamaño del icono */
    }

    .image-placeholder-text {
        font-size: 0.8rem;           /* Reduce tamaño del texto */
    }

    /* ===== OPCIONES DE LOGO EN MÓVIL ===== */
    .logo-options {
        flex-direction: row !important; /* Mantiene fila en móvil - forzado */
        gap: 15px;                   /* Espacio entre opciones */
        margin-bottom: 15px;         /* Reduce margen inferior */
    }

    /* ===== BOTONES EN MÓVIL ===== */
    .action-buttons {
        flex-direction: row !important; /* Mantiene fila en móvil - forzado */
        gap: 6px;                    /* Espacio muy reducido entre botones */
        justify-content: center !important; /* Centra los botones - forzado */
        align-items: center !important; /* Alinea verticalmente - forzado */
        flex-wrap: nowrap !important; /* No permite salto de línea - forzado */
    }

    .btn-save,
    .btn-cancel,
    .btn-change-image {
        flex: 0 0 auto !important;   /* No se expanden - forzado */
        padding: 6px 8px !important; /* Padding más razonable - forzado */
        min-width: 45px !important;  /* Ancho mínimo razonable - forzado */
        max-width: 80px !important;  /* Ancho máximo razonable - forzado */
        font-size: 0.75rem !important; /* Fuente legible - forzado */
        width: auto !important;      /* Ancho automático - forzado */
    }

    .btn-change-image {
        font-size: 0.9rem;           /* Emoji un poco más grande */
    }

    /* ===== HERRAMIENTAS EN MÓVIL ===== */
    .tools-grid,
    .tools-grid-row1 {
        grid-template-columns: repeat(4, 1fr); /* 4 columnas en primera fila */
        gap: 4px;                    /* Reduce espacio entre botones */
        margin-bottom: 6px;          /* Reduce espacio debajo */
    }

    .tools-grid-row2 {
        gap: 4px;                    /* Reduce espacio entre botones de segunda fila */
    }

    .tool-btn {
        padding: 6px 3px;            /* Reduce padding */
        min-height: 35px;            /* Reduce altura mínima */
    }

    .tool-icon {
        font-size: 0.8rem;           /* Reduce tamaño del icono */
    }

    .tool-label {
        font-size: 0.55rem;          /* Reduce tamaño del texto */
    }

    /* ===== AJUSTES EN MÓVIL ===== */
    .desktop-adjustments {
        display: none !important;    /* Hide sliders in mobile */
    }

    .responsive-adjustments {
        display: block !important;   /* Show responsive elements in mobile */
    }

    .responsive-adjustments *,
    .adjustments-buttons,
    .adjustments-buttons *,
    .adjustment-button-control,
    .adjustment-display {
        display: revert !important;  /* Allow responsive elements to show */
    }

    .responsive-adjustments.adjustments-buttons,
    .adjustments-buttons {
        display: grid !important;    /* Force grid layout */
        grid-template-columns: repeat(3, 1fr) !important; /* 3 columns side by side */
        gap: 8px;                    /* Space between controls */
        width: 100%;                 /* Full width */
    }

    .adjustment-button-control {
        gap: 4px;                    /* Reduce espacio entre elementos */
    }

    .adjustment-btn {
        width: 26px;                 /* Tamaño de botones para móvil */
        height: 26px;                /* Tamaño de botones para móvil */
        font-size: 0.8rem;           /* Tamaño de fuente para móvil */
    }

    .adjustment-display {
        font-size: 0.7rem;           /* Tamaño de fuente para móvil */
        min-width: 35px;             /* Ancho mínimo para móvil */
        padding: 1px 3px;            /* Padding mínimo */
    }

    /* Force uniform order in mobile mode - Label → Buttons → Percentage */
    .responsive-adjustments .adjustment-button-control .adjustment-label {
        order: 1 !important;         /* Label at top */
    }

    .responsive-adjustments .adjustment-button-control .adjustment-buttons {
        order: 2 !important;         /* Buttons in middle */
    }

    .responsive-adjustments .adjustment-button-control .adjustment-display {
        order: 3 !important;         /* Percentage at bottom */
        display: block !important;   /* Show percentage values */
        background: none !important; /* Remove background styling */
        border: none !important;     /* Remove border styling */
        padding: 0 !important;       /* Remove padding */
        border-radius: 0 !important; /* Remove border radius */
    }

    .tools-section-title {
        font-size: 0.9rem;           /* Reduce tamaño del título */
        margin-bottom: 12px;         /* Reduce margen */
    }

    .adjustment-control {
        gap: 6px;                    /* Reduce espacio entre elementos */
    }

    .adjustment-label {
        font-size: 0.8rem;           /* Reduce tamaño de fuente */
    }

    .adjustment-slider {
        height: 5px;                 /* Reduce altura del slider */
    }

    .adjustment-slider::-webkit-slider-thumb {
        width: 16px;                 /* Reduce tamaño del thumb */
        height: 16px;                /* Reduce tamaño del thumb */
    }

    .adjustment-slider::-moz-range-thumb {
        width: 16px;                 /* Reduce tamaño del thumb */
        height: 16px;                /* Reduce tamaño del thumb */
    }

    /* ===== VISTA PREVIA OCULTA EN MÓVIL ===== */
    .tools-preview {
        display: none;               /* Oculta la vista previa en móvil */
    }

    /* ===== BOTÓN CAMBIAR IMAGEN EN MÓVIL ===== */
    .btn-change-image {
        padding: 6px 12px;           /* Reduce padding */
        font-size: 0.8rem;           /* Reduce tamaño de fuente */
        min-width: 100px;            /* Reduce ancho mínimo */
    }

    /* ===== LOGO CIRCULAR EN MÓVIL ===== */
    .logo-circular .image-upload-card {
        width: 150px;                /* Reduce tamaño del logo circular */
        height: 150px;               /* Mantiene proporción circular */
    }
}

/* ===== RESPONSIVE PARA MÓVILES PEQUEÑOS (≤480px) ===== */
@media (max-width: 480px) {
    /* ===== CONTENEDOR PRINCIPAL EN MÓVIL PEQUEÑO ===== */
    .marca-container {
        max-width: calc(100% - 12px); /* Usa casi todo el ancho */
        padding: 10px;               /* Padding mínimo */
        margin: 12px 6px;            /* Margen mínimo */
    }

    /* ===== HEADER EN MÓVIL PEQUEÑO ===== */
    .marca-title {
        font-size: 1.5rem;           /* Título más pequeño */
        margin-bottom: 12px;         /* Margen reducido */
    }

    /* ===== CONTENIDO EN MÓVIL PEQUEÑO ===== */
    .marca-content {
        grid-template-columns: 1fr;  /* Una sola columna en móvil pequeño */
        gap: 12px;                   /* Reduce más el espacio entre elementos */
    }

    /* ===== COLUMNAS EN MÓVIL PEQUEÑO ===== */
    .marca-column {
        padding: 12px;               /* Padding mínimo */
    }

    .column-title {
        font-size: 1rem;             /* Título mínimo */
        margin-bottom: 10px;         /* Margen mínimo */
    }

    /* ===== TARJETAS DE IMAGEN EN MÓVIL PEQUEÑO ===== */
    .image-upload-card {
        height: 120px;               /* Altura mínima */
        margin-bottom: 10px;         /* Margen mínimo */
    }

    .image-placeholder-icon {
        font-size: 1.8rem;           /* Icono mínimo */
    }

    .image-placeholder-text {
        font-size: 0.75rem;          /* Texto mínimo */
    }

    /* ===== OPCIONES DE LOGO EN MÓVIL PEQUEÑO ===== */
    .logo-options {
        margin-bottom: 12px;         /* Margen reducido */
        gap: 12px;                   /* Espacio reducido entre opciones */
    }

    .radio-label {
        font-size: 0.75rem;          /* Texto más pequeño */
    }

    .radio-circle {
        width: 14px;                 /* Círculo más pequeño */
        height: 14px;                /* Círculo más pequeño */
    }

    .radio-option input[type="radio"]:checked + .radio-circle::after {
        width: 4px;                  /* Punto más pequeño */
        height: 4px;                 /* Punto más pequeño */
    }

    /* ===== BOTONES EN MÓVIL PEQUEÑO ===== */
    .action-buttons {
        gap: 4px;                    /* Espacio mínimo entre botones */
    }

    .btn-save,
    .btn-cancel,
    .btn-change-image {
        padding: 5px 6px !important; /* Padding razonable - forzado */
        font-size: 0.7rem !important; /* Fuente legible - forzado */
        min-width: 40px !important;  /* Ancho mínimo razonable - forzado */
        max-width: 70px !important;  /* Ancho máximo razonable - forzado */
        width: auto !important;      /* Ancho automático - forzado */
    }

    .btn-change-image {
        font-size: 0.8rem;           /* Emoji un poco más grande */
    }

    /* ===== LOGO CIRCULAR EN MÓVIL PEQUEÑO ===== */
    .logo-circular .image-upload-card {
        width: 120px;                /* Tamaño mínimo del logo circular */
        height: 120px;               /* Mantiene proporción circular */
    }
}

/* ===== RESPONSIVE PARA MÓVILES MUY PEQUEÑOS (≤360px) ===== */
@media (max-width: 360px) {
    /* ===== CONTENEDOR PRINCIPAL EN MÓVIL MUY PEQUEÑO ===== */
    .marca-container {
        max-width: calc(100% - 8px);  /* Usa casi todo el ancho */
        padding: 8px;                /* Padding mínimo absoluto */
        margin: 10px 4px;            /* Margen mínimo absoluto */
    }

    /* ===== HEADER EN MÓVIL MUY PEQUEÑO ===== */
    .marca-title {
        font-size: 1.3rem;           /* Título mínimo */
        margin-bottom: 10px;         /* Margen mínimo */
    }

    /* ===== CONTENIDO EN MÓVIL MUY PEQUEÑO ===== */
    .marca-content {
        grid-template-columns: 1fr;  /* Una sola columna en móvil muy pequeño */
        gap: 10px;                   /* Espacio mínimo entre elementos */
    }

    /* ===== COLUMNAS EN MÓVIL MUY PEQUEÑO ===== */
    .marca-column {
        padding: 10px;               /* Padding mínimo */
    }

    .column-title {
        font-size: 0.9rem;           /* Título mínimo */
        margin-bottom: 8px;          /* Margen mínimo */
    }

    /* ===== TARJETAS DE IMAGEN EN MÓVIL MUY PEQUEÑO ===== */
    .image-upload-card {
        height: 100px;               /* Altura mínima */
        margin-bottom: 8px;          /* Margen mínimo */
    }

    .image-placeholder-icon {
        font-size: 1.5rem;           /* Icono mínimo */
    }

    .image-placeholder-text {
        font-size: 0.7rem;           /* Texto mínimo */
    }

    /* ===== OPCIONES DE LOGO EN MÓVIL MUY PEQUEÑO ===== */
    .logo-options {
        margin-bottom: 10px;         /* Margen mínimo */
        gap: 10px;                   /* Espacio mínimo */
    }

    .radio-label {
        font-size: 0.7rem;           /* Texto mínimo */
    }

    .radio-circle {
        width: 12px;                 /* Círculo mínimo */
        height: 12px;                /* Círculo mínimo */
    }

    /* ===== BOTONES EN MÓVIL MUY PEQUEÑO ===== */
    .action-buttons {
        gap: 3px;                    /* Espacio mínimo entre botones */
    }

    .btn-save,
    .btn-cancel,
    .btn-change-image {
        padding: 4px 5px !important; /* Padding mínimo pero usable - forzado */
        font-size: 0.65rem !important; /* Fuente mínima pero legible - forzado */
        min-width: 35px !important;  /* Ancho mínimo usable - forzado */
        max-width: 60px !important;  /* Ancho máximo razonable - forzado */
        width: auto !important;      /* Ancho automático - forzado */
    }

    .btn-change-image {
        font-size: 0.75rem;          /* Emoji un poco más grande */
    }

    .radio-label {
        font-size: 0.75rem;          /* Texto mínimo */
    }

    .radio-circle {
        width: 14px;                 /* Círculo mínimo */
        height: 14px;                /* Círculo mínimo */
    }

    .radio-option input[type="radio"]:checked + .radio-circle::after {
        width: 4px;                  /* Punto mínimo */
        height: 4px;                 /* Punto mínimo */
    }

    /* ===== BOTONES EN MÓVIL MUY PEQUEÑO ===== */
    .action-buttons {
        gap: 8px;                    /* Espacio mínimo entre botones */
    }

    .btn-save,
    .btn-cancel {
        padding: 8px;                /* Padding mínimo */
        font-size: 0.75rem;          /* Fuente mínima */
    }

    /* ===== LOGO CIRCULAR EN MÓVIL MUY PEQUEÑO ===== */
    .logo-circular .image-upload-card {
        width: 100px;                /* Tamaño mínimo del logo circular */
        height: 100px;               /* Mantiene proporción circular */
    }
}

/* ===== ESTILOS PARA ESTADOS DE VALIDACIÓN ===== */
.image-upload-card.error {
    border-color: #ef4444;           /* Borde rojo para errores */
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.2); /* Sombra roja */
}

.image-upload-card.success {
    border-color: #10b981;           /* Borde verde para éxito */
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.2); /* Sombra verde */
}

.error-message {
    color: #ef4444;                  /* Color rojo para mensajes de error */
    font-size: 0.8rem;               /* Tamaño pequeño */
    margin-top: 5px;                 /* Margen superior */
    display: block;                  /* Bloque para ocupar línea completa */
    text-align: center;              /* Centra el mensaje */
}

.success-message {
    color: #10b981;                  /* Color verde para mensajes de éxito */
    font-size: 0.8rem;               /* Tamaño pequeño */
    margin-top: 5px;                 /* Margen superior */
    display: block;                  /* Bloque para ocupar línea completa */
    text-align: center;              /* Centra el mensaje */
}
