/* ===== ESTILOS GENERALES ===== */
* {
    margin: 0;                    /* Elimina márgenes por defecto */
    padding: 0;                   /* Elimina padding por defecto */
    box-sizing: border-box;       /* Incluye padding y border en el ancho total */
}

body {
    font-family: Arial, sans-serif; /* Fuente principal del sitio */
    background-color: #2d3748;   /* Fondo azul oscuro como en la imagen */
}

/* ===== ESTILOS DEL CONTENEDOR PRINCIPAL DE ESTADÍSTICAS ===== */
.estadisticas-container {
    max-width: 1200px;           /* Ancho máximo del contenedor */
    margin: 30px auto;            /* Centra el contenedor con margen superior */
    padding: 20px;                /* Espaciado interno del contenedor */
    display: flex;                /* Flexbox para las dos columnas principales */
    gap: 30px;                    /* Espacio entre columna izquierda y derecha */
    align-items: stretch;         /* Hace que ambas columnas tengan la misma altura */
    background-color: #2d3748;    /* Fondo azul oscuro como en la imagen */
    position: relative;           /* Posición relativa para líneas separadoras */
}

/* ===== LÍNEA SEPARADORA ENTRE COLUMNAS (Solo modo normal) ===== */
.estadisticas-container::before {
    content: '';                  /* Contenido vacío para pseudo-elemento */
    position: absolute;           /* Posición absoluta */
    left: calc(50% - 1px);        /* Centrada horizontalmente */
    top: 20px;                    /* Desde el top del contenedor */
    bottom: 20px;                 /* Hasta el bottom del contenedor */
    width: 2px;                   /* Ancho de la línea */
    background-color: #6b46c1;   /* Color del header (morado) */
    z-index: 1;                   /* Por encima del fondo */
}

/* ===== CONTENIDO RESPONSIVE (Oculto por defecto) ===== */
.responsive-content {
    display: none;                /* Oculto por defecto en desktop */
}

/* ===== ESTILOS DE LA COLUMNA IZQUIERDA ===== */
.left-column {
    flex: 1;                      /* Ocupa la misma proporción que la columna derecha */
    display: flex;                /* Flexbox para las filas de la columna izquierda */
    flex-direction: column;       /* Organiza las filas verticalmente */
    gap: 20px;                    /* Espacio entre las filas */
}

/* ===== ESTILOS DE LAS FILAS DE LA COLUMNA IZQUIERDA ===== */
.left-row {
    display: flex;                /* Flexbox para los KPIs horizontalmente */
    gap: 15px;                    /* Espacio entre los KPIs de la fila */
    flex: 1;                      /* Cada fila ocupa la misma altura disponible */
}

/* ===== ESTILOS DE LA COLUMNA DERECHA ===== */
.right-column {
    flex: 1;                      /* Ocupa la misma proporción que la columna izquierda */
    display: flex;                /* Flexbox para las filas de la columna derecha */
    flex-direction: column;       /* Organiza las filas verticalmente */
    gap: 20px;                    /* Espacio entre las filas */
}

/* ===== ESTILOS DE LAS FILAS DE LA COLUMNA DERECHA ===== */
.right-row {
    display: flex;                /* Flexbox para los KPIs horizontalmente */
    gap: 15px;                    /* Espacio entre los KPIs de la fila */
}

/* ===== ESTILOS DE LA FILA COMPLETA (TERCER FILA DERECHA) ===== */
.right-row-full {
    display: flex;                /* Flexbox para el KPI que ocupa toda la fila */
}

/* ===== ESTILOS GENERALES DE LOS KPIs ===== */
.kpi {
    background: #1a202c;         /* Fondo azul oscuro como en la imagen */
    border-radius: 8px;           /* Bordes redondeados igual que admin.html */
    padding: 25px;                /* Espaciado interno de los KPIs */
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3); /* Sombra más pronunciada para tema oscuro */
    border-left: 4px solid #6b46c1; /* Borde morado como en la imagen */
    transition: all 0.2s ease;   /* Transición igual que admin.html */
    display: flex;                /* Flexbox para el contenido interno */
    flex-direction: column;       /* Organiza el contenido verticalmente */
    justify-content: center;      /* Centra el contenido verticalmente */
    align-items: center;          /* Centra el contenido horizontalmente */
    position: relative;           /* Posición relativa para z-index */
    z-index: 2;                   /* Por encima de las líneas separadoras */
}

.kpi:hover {
    transform: translateY(-5px);  /* Eleva el KPI al pasar el mouse igual que admin.html */
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.4); /* Sombra más pronunciada para tema oscuro */
}

/* ===== ESTILOS ESPECÍFICOS PARA KPIs DE COLUMNA IZQUIERDA ===== */
.kpi-left {
    flex: 1;                      /* Cada KPI ocupa el mismo espacio en la fila */
    border-left-color: #6b46c1;  /* Borde morado como en la imagen */
    background: #1a202c;         /* Fondo azul oscuro como en la imagen */
    min-height: 200px;            /* Altura aumentada para gráficos */
    display: flex;                /* Flexbox para organizar contenido y gráfico */
    flex-direction: column;       /* Organiza verticalmente */
    justify-content: space-between; /* Distribuye espacio entre contenido y gráfico */
}

/* ===== ESTILOS ESPECÍFICOS PARA KPIs DE COLUMNA DERECHA (PEQUEÑOS) ===== */
.kpi-right-small {
    flex: 1;                      /* Cada KPI ocupa el mismo espacio en la fila */
    border-left-color: #6b46c1;  /* Borde morado como en la imagen */
    background: #1a202c;         /* Fondo azul oscuro como en la imagen */
    min-height: 120px;            /* Altura mínima para KPIs pequeños derechos */
}

/* ===== ESTILOS ESPECÍFICOS PARA KPI DE COLUMNA DERECHA (GRANDE) ===== */
.kpi-right-large {
    flex: 1;                      /* Ocupa todo el ancho disponible */
    border-left-color: #6b46c1;  /* Borde morado como en la imagen */
    background: #1a202c;         /* Fondo azul oscuro como en la imagen */
    min-height: 140px;            /* Altura mayor para el KPI grande */
}

/* ===== ESTILOS ESPECÍFICOS PARA FILA DE ANÁLISIS ===== */
.right-row-analysis {
    display: flex;                /* Flexbox para organizar los 4 KPIs horizontalmente */
    gap: 15px;                    /* Espacio entre KPIs */
    margin-bottom: 15px;          /* Espacio debajo de la fila */
    margin-top: 25px;             /* Espacio superior para la línea separadora */
    width: 100%;                  /* Ocupa todo el ancho disponible */
    position: relative;           /* Posición relativa para línea separadora */
}

/* ===== LÍNEA SEPARADORA ANTES DE KPIs DE ANÁLISIS (Solo modo normal) ===== */
.right-row-analysis::before {
    content: '';                  /* Contenido vacío para pseudo-elemento */
    position: absolute;           /* Posición absoluta */
    top: -12px;                   /* Posición arriba de la fila */
    left: 0;                      /* Desde el borde izquierdo */
    right: 0;                     /* Hasta el borde derecho */
    height: 2px;                  /* Altura de la línea */
    background-color: #6b46c1;   /* Color del header (morado) */
    z-index: 1;                   /* Por encima del fondo */
}

.kpi-right-analysis {
    flex: 1;                      /* Cada KPI ocupa el mismo espacio (1/4 del ancho) */
    border-left: 4px solid;       /* Borde izquierdo como otros KPIs */
    border-left-color: #6b46c1;  /* Borde morado */
    background: #1a202c;         /* Fondo azul oscuro */
    padding: 15px;                /* Padding interno */
    border-radius: 8px;           /* Bordes redondeados */
    text-align: center;           /* Texto centrado */
    min-height: 100px;            /* Altura mínima */
    display: flex;                /* Flexbox para centrar contenido */
    flex-direction: column;       /* Organiza contenido verticalmente */
    justify-content: center;      /* Centra contenido verticalmente */
}

/* ===== ESTILOS DEL TEXTO DE LOS KPIs ===== */
.kpi-title {
    font-size: 0.8rem;           /* Tamaño de fuente igual que admin.html */
    color: rgba(255,255,255,0.7); /* Color blanco semi-transparente como en la imagen */
    margin-bottom: 8px;           /* Espacio debajo del título */
    font-weight: 500;             /* Peso medio de la fuente */
    text-align: center;           /* Centra el texto del título */
}

.kpi-value {
    font-size: 1.75rem;          /* Tamaño igual que admin.html */
    font-weight: 700;             /* Peso igual que admin.html */
    color: #ffffff;               /* Color blanco como en la imagen */
    margin-bottom: 5px;           /* Espacio debajo del valor */
}

.kpi-subtitle {
    font-size: 0.75rem;          /* Tamaño pequeño para el subtítulo */
    color: rgba(255,255,255,0.6); /* Color blanco semi-transparente como en la imagen */
    text-align: center;           /* Centra el subtítulo */
}

/* ===== ESTILOS PARA INDICADORES DE CAMBIO ===== */
.kpi-change {
    font-weight: 600;             /* Peso medio para destacar */
    font-size: 0.7rem;            /* Tamaño ligeramente menor */
}

.kpi-change.positive {
    color: #10b981;               /* Verde para cambios positivos */
}

.kpi-change.positive::before {
    content: "↗ ";                /* Flecha hacia arriba para positivo */
}

.kpi-change.negative {
    color: #ef4444;               /* Rojo para cambios negativos */
}

.kpi-change.negative::before {
    content: "↘ ";                /* Flecha hacia abajo para negativo */
}

/* ===== ESTILOS PARA HEADER DE KPIs COLUMNA IZQUIERDA ===== */
.kpi-header {
    display: flex;                /* Flexbox para organizar título y valores */
    justify-content: space-between; /* Distribuye espacio entre título y valores */
    align-items: flex-start;      /* Alinea elementos arriba */
    margin-bottom: 15px;          /* Espacio debajo del header */
    padding: 0;                   /* Sin padding adicional */
}

.kpi-header .kpi-title {
    font-size: 0.85rem;           /* Tamaño más pequeño para el título */
    color: rgba(255, 255, 255, 0.8); /* Color gris claro */
    font-weight: 500;             /* Peso medio */
    margin: 0;                    /* Sin margen */
    line-height: 1.2;             /* Altura de línea compacta */
    text-align: left;             /* Alineado a la izquierda */
}

.kpi-main-info {
    text-align: right;            /* Alineado a la derecha */
    display: flex;                /* Flexbox para organizar valor y subtitle */
    flex-direction: column;       /* Organiza verticalmente */
    align-items: flex-end;        /* Alinea a la derecha */
}

.kpi-main-info .kpi-value {
    font-size: 1.8rem;            /* Tamaño grande para el valor */
    color: #ffffff;               /* Color blanco */
    font-weight: 700;             /* Peso bold */
    margin: 0 0 4px 0;            /* Margen solo abajo */
    line-height: 1;               /* Altura de línea compacta */
}

.kpi-main-info .kpi-subtitle {
    font-size: 0.75rem;           /* Tamaño pequeño para subtitle */
    color: rgba(255, 255, 255, 0.6); /* Color gris más claro */
    margin: 0;                    /* Sin margen */
    line-height: 1.2;             /* Altura de línea */
    text-align: right;            /* Alineado a la derecha */
}

.kpi-chart-container {
    height: 80px;                 /* Altura fija para el gráfico */
    width: 100%;                  /* Ancho completo */
    margin-top: 10px;             /* Espacio superior */
    padding: 0 10px;              /* Padding horizontal */
    display: flex;                /* Flexbox para centrar canvas */
    justify-content: center;      /* Centra el canvas */
    align-items: center;          /* Centra verticalmente */
}

.kpi-chart-container canvas {
    max-width: 100%;              /* No excede el ancho del contenedor */
    height: auto;                 /* Altura automática */
    cursor: pointer;              /* Cursor pointer para interactividad */
}

/* ===== ESTILOS DEL HEADER DE ESTADÍSTICAS ===== */
.estadisticas-header {
    max-width: 1200px;            /* Mismo ancho máximo que el contenedor principal */
    margin: 30px auto 0 auto;     /* Centra horizontalmente, margen superior */
    padding: 0 20px;              /* Padding lateral igual al contenedor */
}

.estadisticas-title {
    font-size: 2.5rem;            /* Tamaño grande para el título principal */
    font-weight: bold;            /* Texto en negrita */
    color: #ffffff;               /* Color blanco para tema oscuro */
    margin-bottom: 8px;           /* Espacio pequeño debajo del título */
    text-align: left;             /* Alineación a la izquierda */
}

.estadisticas-slogan {
    font-size: 1.1rem;            /* Tamaño mediano para el slogan */
    color: rgba(255,255,255,0.7); /* Color blanco semi-transparente para tema oscuro */
    margin-bottom: 0;             /* Sin margen inferior */
    text-align: left;             /* Alineación a la izquierda */
    font-weight: 400;             /* Peso normal de la fuente */
}

/* ===== ESTILOS RESPONSIVE PARA ESTADÍSTICAS (6 FILAS) ===== */
@media (max-width: 768px) {
    /* ===== OCULTAR CONTAINERS POR DEFECTO EN RESPONSIVE ===== */
    .estadisticas-container.responsive-hidden,
    .estadisticas-header.responsive-hidden {
        display: none;               /* Oculto por defecto en responsive */
    }

    /* ===== CONTENEDOR PRINCIPAL EN MÓVIL ===== */
    .estadisticas-container {
        flex-direction: column;       /* Cambia de horizontal a vertical */
        gap: 8px;                     /* Espacio entre filas */
        padding: 10px;                /* Padding reducido */
        margin: 10px auto;            /* Margen reducido */
        max-width: 100%;              /* Permite que use todo el ancho disponible */
    }

    /* ===== MOSTRAR CONTENIDO RESPONSIVE Y OCULTAR COLUMNAS ORIGINALES ===== */
    .responsive-content {
        display: block;               /* Muestra el contenido responsive */
    }

    .left-column,
    .right-column {
        display: none;                /* Oculta las columnas originales */
    }

    /* ===== OCULTAR LÍNEAS SEPARADORAS EN RESPONSIVE ===== */
    .estadisticas-container::before,
    .right-row-analysis::before {
        display: none;                /* Oculta las líneas separadoras en responsive */
    }

    /* ===== CREAR FILAS RESPONSIVE ===== */
    .responsive-row {
        display: flex;                /* Flexbox para organizar KPIs horizontalmente */
        justify-content: center;      /* Centra los KPIs en la fila */
        gap: 8px;                     /* Espacio entre KPIs */
        width: 100%;                  /* Ocupa todo el ancho */
        margin-bottom: 8px;           /* Espacio entre filas */
    }

    /* ===== FILA 1 Y 2: 2 KPIs GRANDES (Columna izquierda) ===== */
    .responsive-row-2kpis .kpi {
        flex: 1;                      /* Ocupan el mismo espacio */
        max-width: 48%;               /* Máximo 48% cada uno para que quepan 2 */
        min-height: 120px;            /* Altura mínima */
        padding: 15px;                /* Padding interno */
        background: #1a202c;         /* Fondo oscuro */
        border: 1px solid #6b46c1;   /* Borde morado */
        border-radius: 8px;           /* Bordes redondeados */
        text-align: center;           /* Texto centrado */
    }

    /* ===== FILA 3 Y 4: 3 KPIs PEQUEÑOS (Productos) ===== */
    .responsive-row-3kpis .kpi {
        flex: 1;                      /* Ocupan el mismo espacio */
        max-width: 31%;               /* Máximo 31% cada uno para que quepan 3 */
        min-height: 100px;            /* Altura mínima menor */
        padding: 12px;                /* Padding interno menor */
        background: #1a202c;         /* Fondo oscuro */
        border: 1px solid #6b46c1;   /* Borde morado */
        border-radius: 8px;           /* Bordes redondeados */
        text-align: center;           /* Texto centrado */
    }

    /* ===== FILA 5 Y 6: 2 KPIs MEDIANOS (Análisis) ===== */
    .responsive-row-2kpis-analysis .kpi {
        flex: 1;                      /* Ocupan el mismo espacio */
        max-width: 48%;               /* Máximo 48% cada uno para que quepan 2 */
        min-height: 110px;            /* Altura mínima */
        padding: 14px;                /* Padding interno */
        background: #1a202c;         /* Fondo oscuro */
        border: 1px solid #6b46c1;   /* Borde morado */
        border-radius: 8px;           /* Bordes redondeados */
        text-align: center;           /* Texto centrado */
    }

    /* ===== OCULTAR GRÁFICOS EN RESPONSIVE ===== */
    .kpi-chart-container {
        display: none !important;     /* Oculta todos los gráficos */
    }

    /* ===== ESTILOS DE TEXTO PARA KPIs RESPONSIVE ===== */
    .responsive-row .kpi-title {
        font-size: 0.75rem;           /* Tamaño del título */
        margin-bottom: 6px;           /* Margen inferior */
        color: rgba(255, 255, 255, 0.8); /* Color del título */
        font-weight: 500;             /* Peso de la fuente */
    }

    .responsive-row .kpi-value {
        font-size: 1.4rem;            /* Tamaño del valor */
        margin-bottom: 4px;           /* Margen inferior */
        color: #ffffff;               /* Color blanco */
        font-weight: 600;             /* Peso de la fuente */
    }

    .responsive-row .kpi-subtitle {
        font-size: 0.65rem;           /* Tamaño del subtítulo */
        color: rgba(255, 255, 255, 0.6); /* Color del subtítulo */
        line-height: 1.2;             /* Altura de línea */
    }

    /* ===== AJUSTES ESPECÍFICOS PARA KPIs PEQUEÑOS (3 por fila) ===== */
    .responsive-row-3kpis .kpi-title {
        font-size: 0.65rem;           /* Título más pequeño */
        margin-bottom: 4px;           /* Margen menor */
    }

    .responsive-row-3kpis .kpi-value {
        font-size: 1.1rem;            /* Valor más pequeño */
        margin-bottom: 3px;           /* Margen menor */
    }

    .responsive-row-3kpis .kpi-subtitle {
        font-size: 0.55rem;           /* Subtítulo más pequeño */
    }

    /* ===== CAMBIOS DE COLOR PARA INDICADORES ===== */
    .kpi-change.positive {
        color: #10b981;               /* Verde para cambios positivos */
    }

    .kpi-change.negative {
        color: #ef4444;               /* Rojo para cambios negativos */
    }

    /* ===== RESPONSIVE PARA HEADER DE ESTADÍSTICAS ===== */
    .estadisticas-header {
        margin: 15px auto 0 auto;     /* Reduce margen superior en móvil */
        padding: 0 15px;              /* Reduce padding lateral en móvil */
    }

    .estadisticas-title {
        font-size: 1.2rem;            /* Más pequeño en móvil */
        margin-bottom: 3px;           /* Reduce espacio debajo del título */
    }

    .estadisticas-slogan {
        font-size: 0.7rem;            /* Más pequeño en móvil */
    }
}

/* ===== ESTILOS PARA TOOLTIPS DE GRÁFICOS (Solo modo desktop) ===== */
.chart-tooltip {
    position: fixed;              /* Posición fija para seguir el mouse */
    background: rgba(0, 0, 0, 0.9); /* Fondo negro semi-transparente */
    color: #ffffff;               /* Texto blanco */
    padding: 8px 12px;            /* Padding interno */
    border-radius: 6px;           /* Bordes redondeados */
    font-size: 0.8rem;            /* Tamaño de fuente pequeño */
    pointer-events: none;         /* No interfiere con eventos del mouse */
    z-index: 10000;               /* Z-index alto para estar encima */
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3); /* Sombra */
    transform: translate(-50%, -100%); /* Centra horizontalmente y posiciona arriba */
    margin-top: -10px;            /* Espacio adicional arriba */
    border: 1px solid;            /* Borde que se colorea dinámicamente */
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; /* Fuente consistente */
}

.tooltip-day {
    font-weight: 600;             /* Peso semi-bold para el día */
    margin-bottom: 2px;           /* Espacio debajo del día */
    text-align: center;           /* Centrado */
}

.tooltip-value {
    font-weight: 400;             /* Peso normal para el valor */
    text-align: center;           /* Centrado */
    font-size: 0.9rem;            /* Ligeramente más grande */
}

/* ===== MEJORAS PARA CANVAS DE GRÁFICOS ===== */
.kpi-chart-container canvas {
    transition: opacity 0.3s ease; /* Transición suave para efectos */
}

.kpi-chart-container canvas:hover {
    opacity: 0.9;                 /* Ligera transparencia al hover */
}
