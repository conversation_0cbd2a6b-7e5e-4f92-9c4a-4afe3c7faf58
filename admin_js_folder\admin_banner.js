// ===== VARIABLES GLOBALES DEL BANNER PUBLICITARIO =====
let bannerImages = {
    banner: null                    // Almacena datos de la imagen del banner
};

let bannerConfig = {
    type: 'rectangular',            // Tipo de banner (siempre rectangular)
    adjustments: {
        brightness: 100,            // Brillo (100 = normal)
        contrast: 100,              // Contraste (100 = normal)
        saturation: 100             // Saturación (100 = normal)
    }
};

// ===== FUNCIONES PRINCIPALES DE CONFIGURACIÓN DE BANNER =====

/**
 * Inicializa la funcionalidad del contenedor de banner
 */
function initBannerContainer() {
    // Inicializar eventos de carga de imágenes
    initBannerImageUpload();

    // Inicializar botones de acción
    initBannerActionButtons();

    // Cargar datos guardados
    loadBannerData();

    console.log('Módulo de configuración de banner inicializado correctamente');
}

/**
 * Inicializa los eventos de carga de imágenes
 */
function initBannerImageUpload() {
    // Evento para cargar banner izquierdo
    const bannerCardIzquierdo = document.getElementById('banner-upload-card-izquierdo');
    if (bannerCardIzquierdo) {
        bannerCardIzquierdo.addEventListener('click', function() {
            openBannerImageSelector('izquierdo');
        });
    }
    // Evento para cargar banner derecho
    const bannerCardDerecho = document.getElementById('banner-upload-card-derecho');
    if (bannerCardDerecho) {
        bannerCardDerecho.addEventListener('click', function() {
            openBannerImageSelector('derecho');
        });
    }
    console.log('Eventos de carga de banner izquierdo y derecho inicializados');
}

// Función de opciones eliminada - el banner siempre es rectangular

/**
 * Inicializa los botones de acción
 */
function initBannerActionButtons() {
    // Botones del banner
    const bannerSaveBtn = document.getElementById('banner-save-btn');
    const bannerCancelBtn = document.getElementById('banner-cancel-btn');

    if (bannerSaveBtn) {
        bannerSaveBtn.addEventListener('click', function() {
            saveBannerData();
        });
    }

    if (bannerCancelBtn) {
        bannerCancelBtn.addEventListener('click', function() {
            cancelBannerChanges();
        });
    }

    // Botón cambiar imagen
    const changeImageBtn = document.getElementById('banner-change-image-btn');
    if (changeImageBtn) {
        changeImageBtn.addEventListener('click', function() {
            openBannerImageSelector('banner');
        });
    }

    // Inicializar herramientas de edición
    initBannerImageEditor();
    
    console.log('Botones de acción del banner inicializados');
}

/**
 * Abre el selector de imágenes para banner
 */
function openBannerImageSelector(lado) {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'image/*';
    input.onchange = function(event) {
        const file = event.target.files[0];
        if (file) {
            handleBannerImageUpload(file, lado);
        }
    };
    input.click();
}

/**
 * Maneja la carga de imagen del banner
 */
function handleBannerImageUpload(file, lado) {
    if (!file.type.startsWith('image/')) {
        alert('Por favor selecciona un archivo de imagen válido');
        return;
    }
    if (file.size > 5 * 1024 * 1024) {
        alert('La imagen es demasiado grande. Máximo 5MB');
        return;
    }
    const reader = new FileReader();
    reader.onload = function(e) {
        const card = document.getElementById(`banner-upload-card-${lado}`);
        if (card) {
            card.innerHTML = '';
            const img = document.createElement('img');
            img.src = e.target.result;
            img.alt = `Banner ${lado}`;
            img.style.width = '100%';
            img.style.height = '100%';
            img.style.objectFit = 'cover';
            img.style.borderRadius = '8px';
            card.appendChild(img);
            card.classList.add('has-image');
        }
    };
    reader.readAsDataURL(file);
}

/**
 * Actualiza la tarjeta de imagen del banner
 */
function updateBannerImageCard(type, imageUrl) {
    const card = document.getElementById(`${type}-upload-card`);
    if (!card) return;
    
    // Limpiar contenido anterior
    card.innerHTML = '';
    
    // Crear imagen
    const img = document.createElement('img');
    img.src = imageUrl;
    img.alt = `${type} image`;
    img.style.width = '100%';
    img.style.height = '100%';
    img.style.objectFit = 'cover';
    img.style.borderRadius = '8px';
    
    card.appendChild(img);
    card.classList.add('has-image');
    
    console.log(`Tarjeta de ${type} actualizada con nueva imagen`);
}

/**
 * Muestra el botón de cambiar imagen (lápiz)
 */
function showBannerChangeImageButton() {
    const changeImageBtn = document.getElementById('banner-change-image-btn');
    if (changeImageBtn) {
        changeImageBtn.style.display = 'flex';
        console.log('Botón de cambiar imagen del banner mostrado');
    }
}

/**
 * Oculta el botón de cambiar imagen (lápiz)
 */
function hideBannerChangeImageButton() {
    const changeImageBtn = document.getElementById('banner-change-image-btn');
    if (changeImageBtn) {
        changeImageBtn.style.display = 'none';
        console.log('Botón de cambiar imagen del banner ocultado');
    }
}

/**
 * Actualiza la visualización del banner (siempre rectangular)
 */
function updateBannerDisplay() {
    const bannerCard = document.getElementById('banner-upload-card');
    if (!bannerCard) return;

    // El banner siempre es rectangular
    bannerCard.classList.add('banner-rectangular');

    console.log('Display del banner actualizado - formato rectangular');
}

/**
 * Muestra un mensaje al usuario
 */
function showBannerMessage(message, type = 'info') {
    console.log(`[${type.toUpperCase()}] ${message}`);
    
    // Aquí puedes implementar un sistema de notificaciones más sofisticado
    // Por ahora solo mostramos en consola y alert para errores críticos
    if (type === 'error') {
        alert(message);
    }
}

/**
 * Guarda los datos del banner
 */
function saveBannerData() {
    console.log('Guardando datos del banner...');
    
    const bannerData = {
        images: bannerImages,
        config: bannerConfig,
        timestamp: Date.now()
    };
    
    try {
        localStorage.setItem('bannerData', JSON.stringify(bannerData));
        showBannerMessage('Configuración del banner guardada correctamente', 'success');
        console.log('Datos del banner guardados en localStorage');
    } catch (error) {
        showBannerMessage('Error al guardar la configuración del banner', 'error');
        console.error('Error al guardar en localStorage:', error);
    }
}

/**
 * Cancela los cambios del banner
 */
function cancelBannerChanges() {
    console.log('Cancelando cambios del banner...');

    // Recargar datos guardados
    loadBannerData();

    showBannerMessage('Cambios cancelados', 'info');
}

/**
 * Limpia una imagen del banner y actualiza la interfaz
 */
function clearBannerImage(type) {
    if (bannerImages[type]) {
        bannerImages[type] = null;

        // Limpiar tarjeta
        const card = document.getElementById(`${type}-upload-card`);
        if (card) {
            card.innerHTML = `
                <div class="image-placeholder">
                    <div class="image-placeholder-icon">+</div>
                    <div class="image-placeholder-text">Clic para cargar banner</div>
                </div>
            `;
            card.classList.remove('has-image');
        }

        // Verificar si quedan imágenes para mostrar/ocultar botón
        const hasAnyImage = Object.values(bannerImages).some(img => img && img.url);
        if (!hasAnyImage) {
            hideBannerChangeImageButton();
        }

        console.log(`Imagen ${type} del banner eliminada`);
    }
}

/**
 * Carga los datos guardados del banner
 */
function loadBannerData() {
    console.log('Cargando datos guardados del banner...');
    
    try {
        const savedData = localStorage.getItem('bannerData');
        if (savedData) {
            const bannerData = JSON.parse(savedData);
            
            // Restaurar configuración
            bannerConfig = { ...bannerConfig, ...bannerData.config };
            bannerImages = { ...bannerImages, ...bannerData.images };
            
            // Actualizar interfaz
            updateBannerInterface();
            
            console.log('Datos del banner cargados desde localStorage');
        } else {
            console.log('No hay datos guardados del banner');
        }
    } catch (error) {
        console.error('Error al cargar datos del banner:', error);
        showBannerMessage('Error al cargar la configuración guardada', 'error');
    }
}

/**
 * Actualiza la interfaz con los datos cargados
 */
function updateBannerInterface() {
    let hasImages = false;

    // Actualizar imágenes
    Object.keys(bannerImages).forEach(type => {
        if (bannerImages[type] && bannerImages[type].url) {
            updateBannerImageCard(type, bannerImages[type].url);
            hasImages = true;
        }
    });

    // Mostrar/ocultar botón de cambiar imagen según si hay imágenes
    if (hasImages) {
        showBannerChangeImageButton();
    } else {
        hideBannerChangeImageButton();
    }

    // Actualizar display (siempre rectangular)
    updateBannerDisplay();

    console.log('Interfaz del banner actualizada');
}

// ===== FUNCIONES DE EDICIÓN DE IMAGEN DEL BANNER =====

/**
 * Inicializa las herramientas de edición de imagen
 */
function initBannerImageEditor() {
    console.log('Inicializando herramientas de edición del banner...');

    // Inicializar herramientas de transformación
    initBannerTransformTools();

    // Inicializar controles de ajuste
    initBannerAdjustmentControls();

    console.log('Herramientas de edición del banner inicializadas');
}

/**
 * Inicializa las herramientas de transformación
 */
function initBannerTransformTools() {
    // Botones de rotación
    const rotateLeftBtn = document.getElementById('banner-rotate-left');
    const rotateRightBtn = document.getElementById('banner-rotate-right');

    if (rotateLeftBtn) {
        rotateLeftBtn.addEventListener('click', () => rotateBannerImage(-90));
    }

    if (rotateRightBtn) {
        rotateRightBtn.addEventListener('click', () => rotateBannerImage(90));
    }

    // Botones de volteo
    const flipHorizontalBtn = document.getElementById('banner-flip-horizontal');
    const flipVerticalBtn = document.getElementById('banner-flip-vertical');

    if (flipHorizontalBtn) {
        flipHorizontalBtn.addEventListener('click', () => flipBannerImage('horizontal'));
    }

    if (flipVerticalBtn) {
        flipVerticalBtn.addEventListener('click', () => flipBannerImage('vertical'));
    }

    // Botones de escala
    const scaleUpBtn = document.getElementById('banner-scale-up');
    const scaleDownBtn = document.getElementById('banner-scale-down');

    if (scaleUpBtn) {
        scaleUpBtn.addEventListener('click', () => scaleBannerImage(1.1));
    }

    if (scaleDownBtn) {
        scaleDownBtn.addEventListener('click', () => scaleBannerImage(0.9));
    }

    // Botón de reset
    const resetBtn = document.getElementById('banner-reset');
    if (resetBtn) {
        resetBtn.addEventListener('click', resetBannerTransforms);
    }
}

/**
 * Inicializa los controles de ajuste (brillo, contraste, saturación)
 */
function initBannerAdjustmentControls() {
    // Controles desktop (sliders)
    initBannerDesktopAdjustments();

    // Controles responsive (botones +/-)
    initBannerResponsiveAdjustments();
}

/**
 * Inicializa los controles desktop con sliders
 */
function initBannerDesktopAdjustments() {
    const adjustments = ['brightness', 'contrast', 'saturation'];

    adjustments.forEach(adjustment => {
        const slider = document.getElementById(`banner-${adjustment}-slider`);
        const valueDisplay = document.getElementById(`banner-${adjustment}-value`);

        if (slider) {
            slider.addEventListener('input', function() {
                const value = parseInt(this.value);
                bannerConfig.adjustments[adjustment] = value;

                if (valueDisplay) {
                    valueDisplay.textContent = value + '%';
                }

                applyBannerAdjustments();
            });
        }
    });
}

/**
 * Inicializa los controles responsive con botones +/-
 */
function initBannerResponsiveAdjustments() {
    const adjustments = ['brightness', 'contrast', 'saturation'];

    adjustments.forEach(adjustment => {
        const decreaseBtn = document.getElementById(`banner-${adjustment}-decrease`);
        const increaseBtn = document.getElementById(`banner-${adjustment}-increase`);
        const display = document.getElementById(`banner-${adjustment}-display`);

        if (decreaseBtn) {
            decreaseBtn.addEventListener('click', function() {
                changeBannerAdjustment(adjustment, -5);
            });
        }

        if (increaseBtn) {
            increaseBtn.addEventListener('click', function() {
                changeBannerAdjustment(adjustment, 5);
            });
        }
    });
}

/**
 * Cambia un ajuste del banner en una cantidad específica
 */
function changeBannerAdjustment(adjustment, delta) {
    const currentValue = bannerConfig.adjustments[adjustment];
    const newValue = Math.max(0, Math.min(200, currentValue + delta));

    bannerConfig.adjustments[adjustment] = newValue;

    // Actualizar displays
    updateBannerAdjustmentDisplays(adjustment, newValue);

    // Aplicar cambios
    applyBannerAdjustments();

    console.log(`${adjustment} del banner cambiado a: ${newValue}%`);
}

/**
 * Actualiza los displays de ajuste
 */
function updateBannerAdjustmentDisplays(adjustment, value) {
    // Actualizar slider desktop
    const slider = document.getElementById(`banner-${adjustment}-slider`);
    if (slider) {
        slider.value = value;
    }

    // Actualizar valor desktop
    const valueDisplay = document.getElementById(`banner-${adjustment}-value`);
    if (valueDisplay) {
        valueDisplay.textContent = value + '%';
    }

    // Actualizar display responsive
    const responsiveDisplay = document.getElementById(`banner-${adjustment}-display`);
    if (responsiveDisplay) {
        responsiveDisplay.textContent = value + '%';
    }
}

/**
 * Rota la imagen del banner
 */
function rotateBannerImage(degrees) {
    console.log(`Rotando imagen del banner ${degrees} grados`);

    const bannerCard = document.getElementById('banner-upload-card');
    const img = bannerCard?.querySelector('img');

    if (img) {
        const currentTransform = img.style.transform || '';
        const rotateMatch = currentTransform.match(/rotate\(([^)]+)\)/);
        const currentRotation = rotateMatch ? parseInt(rotateMatch[1]) : 0;
        const newRotation = currentRotation + degrees;

        img.style.transform = currentTransform.replace(/rotate\([^)]+\)/, '') + ` rotate(${newRotation}deg)`;

        showBannerMessage(`Imagen rotada ${degrees > 0 ? 'hacia la derecha' : 'hacia la izquierda'}`, 'info');
    }
}

/**
 * Voltea la imagen del banner
 */
function flipBannerImage(direction) {
    console.log(`Volteando imagen del banner ${direction}`);

    const bannerCard = document.getElementById('banner-upload-card');
    const img = bannerCard?.querySelector('img');

    if (img) {
        const currentTransform = img.style.transform || '';

        if (direction === 'horizontal') {
            const scaleXMatch = currentTransform.match(/scaleX\(([^)]+)\)/);
            const currentScaleX = scaleXMatch ? parseFloat(scaleXMatch[1]) : 1;
            const newScaleX = currentScaleX * -1;

            img.style.transform = currentTransform.replace(/scaleX\([^)]+\)/, '') + ` scaleX(${newScaleX})`;
        } else if (direction === 'vertical') {
            const scaleYMatch = currentTransform.match(/scaleY\(([^)]+)\)/);
            const currentScaleY = scaleYMatch ? parseFloat(scaleYMatch[1]) : 1;
            const newScaleY = currentScaleY * -1;

            img.style.transform = currentTransform.replace(/scaleY\([^)]+\)/, '') + ` scaleY(${newScaleY})`;
        }

        showBannerMessage(`Imagen volteada ${direction === 'horizontal' ? 'horizontalmente' : 'verticalmente'}`, 'info');
    }
}

/**
 * Escala la imagen del banner
 */
function scaleBannerImage(factor) {
    console.log(`Escalando imagen del banner por factor ${factor}`);

    const bannerCard = document.getElementById('banner-upload-card');
    const img = bannerCard?.querySelector('img');

    if (img) {
        const currentTransform = img.style.transform || '';
        const scaleMatch = currentTransform.match(/scale\(([^)]+)\)/);
        const currentScale = scaleMatch ? parseFloat(scaleMatch[1]) : 1;
        const newScale = Math.max(0.1, Math.min(3, currentScale * factor));

        img.style.transform = currentTransform.replace(/scale\([^)]+\)/, '') + ` scale(${newScale})`;

        showBannerMessage(`Imagen ${factor > 1 ? 'ampliada' : 'reducida'}`, 'info');
    }
}

/**
 * Aplica los ajustes de imagen (brillo, contraste, saturación)
 */
function applyBannerAdjustments() {
    const bannerCard = document.getElementById('banner-upload-card');
    const img = bannerCard?.querySelector('img');

    if (img) {
        const { brightness, contrast, saturation } = bannerConfig.adjustments;
        img.style.filter = `brightness(${brightness}%) contrast(${contrast}%) saturate(${saturation}%)`;
    }
}

/**
 * Resetea todas las transformaciones del banner
 */
function resetBannerTransforms() {
    console.log('Reseteando transformaciones del banner');

    const bannerCard = document.getElementById('banner-upload-card');
    const img = bannerCard?.querySelector('img');

    if (img) {
        img.style.transform = '';
        img.style.filter = '';
    }

    // Resetear configuración
    bannerConfig.adjustments = {
        brightness: 100,
        contrast: 100,
        saturation: 100
    };

    // Actualizar controles
    updateAllBannerAdjustmentDisplays();

    showBannerMessage('Transformaciones del banner reseteadas', 'info');
}

/**
 * Actualiza todos los displays de ajuste
 */
function updateAllBannerAdjustmentDisplays() {
    Object.keys(bannerConfig.adjustments).forEach(adjustment => {
        updateBannerAdjustmentDisplays(adjustment, bannerConfig.adjustments[adjustment]);
    });
}

// ===== FUNCIONES DE UTILIDAD =====

/**
 * Verifica si hay una imagen cargada
 */
function hasBannerImage() {
    return bannerImages.banner && bannerImages.banner.url;
}

/**
 * Obtiene los datos actuales del banner
 */
function getBannerData() {
    return {
        images: bannerImages,
        config: bannerConfig
    };
}

/**
 * Función de prueba para verificar la funcionalidad del banner
 */
function testBannerFunctionality() {
    console.log('=== PRUEBA DE FUNCIONALIDAD DEL BANNER ===');
    console.log('Configuración actual:', bannerConfig);
    console.log('Imágenes cargadas:', bannerImages);

    // Verificar elementos del DOM
    const bannerContainer = document.querySelector('.banner-container');
    const bannerCard = document.getElementById('banner-upload-card');
    const saveBtn = document.getElementById('banner-save-btn');

    console.log('Elementos encontrados:');
    console.log('- Contenedor:', bannerContainer ? 'Sí' : 'No');
    console.log('- Tarjeta de carga:', bannerCard ? 'Sí' : 'No');
    console.log('- Botón guardar:', saveBtn ? 'Sí' : 'No');

    return {
        container: !!bannerContainer,
        card: !!bannerCard,
        saveButton: !!saveBtn
    };
}

// ===== INICIALIZACIÓN =====

/**
 * Inicialización cuando el DOM está listo
 */
document.addEventListener('DOMContentLoaded', function() {
    console.log('Inicializando módulo de banner publicitario...');

    // Pequeño delay para asegurar que todos los elementos estén disponibles
    setTimeout(function() {
        try {
            initBannerContainer();
            console.log('✅ Módulo de banner publicitario inicializado correctamente');
        } catch (error) {
            console.error('❌ Error al inicializar el módulo de banner:', error);
        }

        // Hacer funciones disponibles globalmente para pruebas
        window.testBannerFunctionality = testBannerFunctionality;
        window.getBannerData = getBannerData;
        window.saveBannerData = saveBannerData;
        window.loadBannerData = loadBannerData;

        console.log('Funciones del banner disponibles globalmente para pruebas');
    }, 100);
});
