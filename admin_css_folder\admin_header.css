/* ===== ESTILOS GENERALES ===== */
* {
    margin: 0;                    /* Elimina márgenes por defecto */
    padding: 0;                   /* Elimina padding por defecto */
    box-sizing: border-box;       /* Incluye padding y border en el ancho total */
}

body {
    font-family: Arial, sans-serif; /* Fuente principal del sitio */
}

/* ===== ESTILOS DEL HEADER ===== */
.header {
    background: linear-gradient(45deg, #6a1b9a 40%, #ffd54f 90%); /* Gradiente morado a amarillo igual que admin.html */
    padding: 20px;                /* Espaciado interno del header */
    text-align: center;           /* Centra el contenido del header */
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1); /* Sombra debajo del header */
    position: relative;           /* Posición relativa por defecto */
    z-index: 1002;                /* Z-index alto para estar por encima del menú responsive */
}

/* ===== ESTILOS DEL TÍTULO DEL HEADER ===== */
.header-title {
    display: flex;                /* Flexbox para alinear título e icono */
    align-items: center;          /* Centra verticalmente título e icono */
    justify-content: center;      /* Centra horizontalmente título e icono */
    margin-bottom: 15px;          /* Espacio debajo del título */
}

.header-title h1 {
    color: white;                 /* Color blanco del título */
    font-size: 2.5rem;           /* Tamaño grande del título */
    font-weight: bold;            /* Texto en negrita */
    margin-right: 10px;           /* Espacio entre título e icono */
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3); /* Sombra del texto para mejor legibilidad */
}

.header-icon {
    font-size: 2.5rem;           /* Tamaño del icono igual al título */
    color: white;                 /* Color blanco del icono */
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3); /* Sombra del icono */
}

/* ===== ESTILOS DE LA NAVEGACIÓN DEL HEADER ===== */
.header-nav {
    display: flex;                /* Flexbox para alinear enlaces horizontalmente */
    justify-content: center;      /* Centra los enlaces */
    gap: 30px;                    /* Espacio entre enlaces */
    flex-wrap: nowrap;            /* Evita que los enlaces se envuelvan a nueva línea */
    overflow-x: auto;             /* Permite scroll horizontal si es necesario */
}

.header-nav a {
    color: white;                 /* Color blanco de los enlaces */
    text-decoration: none;        /* Quita el subrayado de los enlaces */
    font-size: 0.9rem;           /* Tamaño de fuente de los enlaces */
    padding: 8px 15px;            /* Espaciado interno de los enlaces */
    border-radius: 20px;          /* Bordes redondeados de los enlaces */
    transition: all 0.3s ease;   /* Transición suave para efectos hover */
    background: rgba(255, 255, 255, 0.1); /* Fondo semi-transparente */
    backdrop-filter: blur(10px);  /* Efecto de desenfoque del fondo */
    white-space: nowrap;          /* Evita que el texto se divida en líneas */
}

.header-nav a:hover {
    background: rgba(255, 255, 255, 0.2); /* Fondo más opaco al pasar el mouse */
    transform: translateY(-2px);  /* Eleva el enlace al pasar el mouse */
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2); /* Sombra al pasar el mouse */
}

/* ===== ESTILOS DEL CONTENIDO PRINCIPAL ===== */
.main-content {
    padding: 40px 20px;          /* Espaciado interno del contenido principal */
    text-align: center;           /* Centra el texto del contenido */
    background-color: #2d3748;    /* Fondo azul oscuro como en la imagen */
    min-height: 100vh;            /* Altura mínima igual que admin.html */
}

.main-content h2 {
    color: #ffffff;               /* Color blanco para tema oscuro */
    margin-bottom: 20px;          /* Espacio debajo del subtítulo */
}

/* ===== ESTILOS RESPONSIVE PARA TABLETS ===== */
@media (max-width: 768px) {
    .header {
        padding: 15px 10px;       /* Reduce el padding del header en tablets */
    }
    
    .header-title h1 {
        font-size: 2rem;          /* Reduce el tamaño del título en tablets */
    }
    
    .header-icon {
        font-size: 2rem;          /* Reduce el tamaño del icono en tablets */
    }
    
    .header-nav {
        display: none;            /* Oculta completamente la navegación en tablets */
    }
}

/* ===== ESTILOS RESPONSIVE PARA MÓVILES ===== */
@media (max-width: 768px) {
    .header {
        position: fixed;          /* Posición fija en responsive */
        top: 0;                   /* Pegado al top */
        left: 0;                  /* Pegado a la izquierda */
        width: 100%;              /* Ancho completo */
        z-index: 1002;            /* Por encima del menú responsive */
        padding: 15px 10px;       /* Padding reducido para responsive */
    }

    .header-nav {
        display: none;            /* Oculta la navegación en responsive */
    }
}

@media (max-width: 480px) {
    .header {
        padding: 12px 5px;        /* Reduce más el padding en móviles */
    }

    .header-title h1 {
        font-size: 1.7rem;        /* Reduce más el título en móviles */
    }

    .header-icon {
        font-size: 1.7rem;        /* Reduce más el icono en móviles */
    }
}

/* ===== ESTILOS RESPONSIVE PARA MÓVILES PEQUEÑOS ===== */
@media (max-width: 360px) {
    .header-nav {
        display: none;            /* Oculta completamente la navegación en móviles pequeños */
    }
}

/* ===== FOOTER ===== */
.footer {
    background: linear-gradient(45deg, #6a1b9a 40%, #ffd54f 90%);
    color: white;
    padding: 28px 10px 18px 10px;
    text-align: center;
    box-shadow: 0 -2px 10px rgba(0,0,0,0.08);
    margin-top: 40px;
}

.footer-links {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 32px;
    flex-wrap: wrap;
    margin-bottom: 18px;
}

.footer-divider {
    border: none;
    border-top: 1.5px solid #fff;
    margin: 0 auto 10px auto;
    width: 90%;
    max-width: 600px;
}

.footer-link {
    color: white;
    text-decoration: none;
    font-size: 0.88rem;
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 5px 10px;
    border-radius: 7px;
    background: rgba(255,255,255,0.08);
    transition: background 0.3s, transform 0.3s;
    border: 1.2px solid #fff;
}

.footer-link:hover {
    background: rgba(255,255,255,0.18);
    transform: translateY(-2px) scale(1.04);
}

.footer-icon {
    font-size: 1em;
    margin-right: 2px;
}

.footer-copyright {
    font-size: 0.75rem;
    margin-bottom: 2px;
    font-weight: bold;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.12);
}

.footer-desc {
    font-size: 0.75rem;
    color: #fffde7;
    opacity: 0.92;
    margin-top: 2px;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.10);
}

@media (max-width: 600px) {
    .footer-links {
        gap: 14px;
    }
    .footer-link {
        font-size: 0.80rem;
        padding: 4px 7px;
    }
    .footer-icon {
        font-size: 0.92em;
    }
    .footer-copyright, .footer-desc {
        font-size: 0.68rem;
    }
}

/* ===== FOOTER DESKTOP ===== */
/* El bloque de estilos del footer desktop ha sido trasladado a admin_footer.css */
