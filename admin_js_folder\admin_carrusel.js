// ===== VARIABLES GLOBALES DEL CARRUSEL =====
let carruselImages = {
    'carrusel1': {},  // Almacena las imágenes del carrusel 1 (tarjetas 1-6)
    'carrusel2': {}   // Almacena las imágenes del carrusel 2 (tarjetas 7-12)
};

// ===== FUNCIONES PRINCIPALES DEL CARRUSEL =====

/**
 * Inicializa la funcionalidad de las pestañas del carrusel
 */
function initCarruselTabs() {
    // Obtiene todos los botones de pestañas del carrusel
    const tabButtons = document.querySelectorAll('.carrusel-tab-button');
    // Obtiene todos los contenidos de pestañas del carrusel
    const tabContents = document.querySelectorAll('.carrusel-tab-content');
    
    // Agrega evento click a cada botón de pestaña
    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Obtiene el ID de la pestaña objetivo
            const targetTab = this.getAttribute('data-carrusel-tab');
            
            // Remueve la clase active de todos los botones
            tabButtons.forEach(btn => btn.classList.remove('active'));
            // Remueve la clase active de todos los contenidos
            tabContents.forEach(content => content.classList.remove('active'));
            
            // Agrega la clase active al botón clickeado
            this.classList.add('active');
            // Agrega la clase active al contenido correspondiente
            document.getElementById(targetTab).classList.add('active');
        });
    });
}

/**
 * Inicializa las tarjetas del carrusel con sus números y funcionalidad
 */
function initCarruselCards() {
    const carruseles = ['carrusel1', 'carrusel2'];
    
    carruseles.forEach((carruselId, carruselIndex) => {
        const carruselContainer = document.getElementById(carruselId);
        const grid = carruselContainer.querySelector('.carrusel-grid');
        
        // Crear 6 tarjetas por carrusel
        for (let i = 1; i <= 6; i++) {
            const cardNumber = (carruselIndex * 6) + i; // Numeración consecutiva 1-12
            const card = createCarruselCard(cardNumber, carruselId);
            grid.appendChild(card);
        }
    });
}

/**
 * Crea una tarjeta de carrusel con su número y funcionalidad
 * @param {number} cardNumber - Número de la tarjeta (1-12)
 * @param {string} carruselId - ID del carrusel padre
 * @returns {HTMLElement} - Elemento de la tarjeta
 */
function createCarruselCard(cardNumber, carruselId) {
    const card = document.createElement('div');
    card.className = 'carrusel-card';
    card.dataset.cardNumber = cardNumber;
    card.dataset.carruselId = carruselId;
    
    // Crear número de tarjeta
    const numberElement = document.createElement('div');
    numberElement.className = 'carrusel-card-number';
    numberElement.textContent = cardNumber;
    
    // Crear placeholder de imagen
    const placeholder = document.createElement('div');
    placeholder.className = 'carrusel-placeholder';
    placeholder.innerHTML = `
        <div class="carrusel-placeholder-icon">+</div>
        <div class="carrusel-placeholder-text">Clic para cargar imagen</div>
    `;
    
    // Agregar elementos a la tarjeta
    card.appendChild(numberElement);
    card.appendChild(placeholder);
    
    // Agregar evento click para cargar imagen
    card.addEventListener('click', function() {
        openCarruselImageSelector(cardNumber, carruselId);
    });
    
    return card;
}

/**
 * Abre el selector de archivos para cargar una imagen
 * @param {number} cardNumber - Número de la tarjeta
 * @param {string} carruselId - ID del carrusel
 */
function openCarruselImageSelector(cardNumber, carruselId) {
    // Crear input file temporal
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'image/*';
    input.onchange = function(event) {
        handleCarruselImageUpload(event, cardNumber, carruselId);
    };
    input.click();
}

/**
 * Maneja la carga de imagen seleccionada
 * @param {Event} event - Evento del input file
 * @param {number} cardNumber - Número de la tarjeta
 * @param {string} carruselId - ID del carrusel
 */
function handleCarruselImageUpload(event, cardNumber, carruselId) {
    const file = event.target.files[0];
    
    if (file) {
        // Validar tipo de archivo
        if (!file.type.startsWith('image/')) {
            showMessage('Por favor selecciona un archivo de imagen válido.', 'error');
            return;
        }
        
        // Validar tamaño (máximo 5MB)
        if (file.size > 5 * 1024 * 1024) {
            showMessage('La imagen debe ser menor a 5MB.', 'error');
            return;
        }
        
        // Crear URL temporal para preview
        const reader = new FileReader();
        reader.onload = function(e) {
            // Guardar imagen en el almacén global
            carruselImages[carruselId][cardNumber] = {
                file: file,
                url: e.target.result,
                name: file.name
            };
            
            // Actualizar la tarjeta con la imagen
            updateCardWithImage(cardNumber, carruselId, e.target.result);
            
            // Mostrar mensaje de éxito
            showMessage(`Imagen cargada en tarjeta ${cardNumber}`, 'success');
        };
        reader.readAsDataURL(file);
    }
}

/**
 * Actualiza una tarjeta con la imagen cargada
 * @param {number} cardNumber - Número de la tarjeta
 * @param {string} carruselId - ID del carrusel
 * @param {string} imageUrl - URL de la imagen
 */
function updateCardWithImage(cardNumber, carruselId, imageUrl) {
    const card = document.querySelector(`[data-card-number="${cardNumber}"][data-carrusel-id="${carruselId}"]`);
    
    if (card) {
        // Establecer imagen como fondo
        card.style.backgroundImage = `url(${imageUrl})`;
        card.classList.add('has-image');
        
        // Ocultar placeholder
        const placeholder = card.querySelector('.carrusel-placeholder');
        if (placeholder) {
            placeholder.style.display = 'none';
        }
    }
}

/**
 * Elimina la imagen de una tarjeta
 * @param {number} cardNumber - Número de la tarjeta
 * @param {string} carruselId - ID del carrusel
 */
function removeCardImage(cardNumber, carruselId) {
    const card = document.querySelector(`[data-card-number="${cardNumber}"][data-carrusel-id="${carruselId}"]`);
    
    if (card) {
        // Remover imagen de fondo
        card.style.backgroundImage = '';
        card.classList.remove('has-image');
        
        // Mostrar placeholder
        const placeholder = card.querySelector('.carrusel-placeholder');
        if (placeholder) {
            placeholder.style.display = 'flex';
        }
        
        // Eliminar del almacén global
        if (carruselImages[carruselId][cardNumber]) {
            delete carruselImages[carruselId][cardNumber];
        }
        
        showMessage(`Imagen eliminada de tarjeta ${cardNumber}`, 'info');
    }
}

/**
 * Obtiene todas las imágenes cargadas en un carrusel específico
 * @param {string} carruselId - ID del carrusel
 * @returns {Object} - Objeto con las imágenes del carrusel
 */
function getCarruselImages(carruselId) {
    return carruselImages[carruselId] || {};
}

/**
 * Obtiene todas las imágenes cargadas en todos los carruseles
 * @returns {Object} - Objeto con todas las imágenes
 */
function getAllCarruselImages() {
    return carruselImages;
}

/**
 * Carga imágenes desde datos guardados (para persistencia)
 * @param {Object} savedImages - Datos de imágenes guardadas
 */
function loadSavedImages(savedImages) {
    if (!savedImages) return;
    
    Object.keys(savedImages).forEach(carruselId => {
        const carruselData = savedImages[carruselId];
        
        Object.keys(carruselData).forEach(cardNumber => {
            const imageData = carruselData[cardNumber];
            
            if (imageData && imageData.url) {
                // Actualizar almacén global
                carruselImages[carruselId][cardNumber] = imageData;
                
                // Actualizar tarjeta visual
                updateCardWithImage(parseInt(cardNumber), carruselId, imageData.url);
            }
        });
    });
}

/**
 * Guarda las imágenes en localStorage (persistencia básica)
 */
function saveCarruselImages() {
    try {
        // Crear objeto para guardar (sin los archivos File, solo URLs)
        const dataToSave = {};
        
        Object.keys(carruselImages).forEach(carruselId => {
            dataToSave[carruselId] = {};
            
            Object.keys(carruselImages[carruselId]).forEach(cardNumber => {
                const imageData = carruselImages[carruselId][cardNumber];
                dataToSave[carruselId][cardNumber] = {
                    url: imageData.url,
                    name: imageData.name
                };
            });
        });
        
        localStorage.setItem('carruselImages', JSON.stringify(dataToSave));
    } catch (error) {
        console.warn('No se pudieron guardar las imágenes del carrusel:', error);
    }
}

/**
 * Carga las imágenes desde localStorage
 */
function loadCarruselImages() {
    try {
        const savedData = localStorage.getItem('carruselImages');
        if (savedData) {
            const parsedData = JSON.parse(savedData);
            loadSavedImages(parsedData);
        }
    } catch (error) {
        console.warn('No se pudieron cargar las imágenes del carrusel:', error);
    }
}

/**
 * Muestra un mensaje al usuario
 * @param {string} message - Mensaje a mostrar
 * @param {string} type - Tipo de mensaje ('success', 'error', 'info')
 */
function showMessage(message, type = 'info') {
    // Implementación básica con alert (se puede mejorar con toast/notification)
    console.log(`[${type.toUpperCase()}] ${message}`);
    
    // Opcional: mostrar alert solo para errores importantes
    if (type === 'error') {
        alert(message);
    }
}

// ===== FUNCIONES DE UTILIDAD =====

/**
 * Obtiene el número total de imágenes cargadas
 * @returns {number} - Número total de imágenes
 */
function getTotalImagesCount() {
    let count = 0;
    Object.keys(carruselImages).forEach(carruselId => {
        count += Object.keys(carruselImages[carruselId]).length;
    });
    return count;
}

/**
 * Obtiene el número de imágenes cargadas en un carrusel específico
 * @param {string} carruselId - ID del carrusel
 * @returns {number} - Número de imágenes en el carrusel
 */
function getCarruselImagesCount(carruselId) {
    return Object.keys(carruselImages[carruselId] || {}).length;
}

/**
 * Verifica si una tarjeta tiene imagen cargada
 * @param {number} cardNumber - Número de la tarjeta
 * @param {string} carruselId - ID del carrusel
 * @returns {boolean} - True si tiene imagen
 */
function hasCardImage(cardNumber, carruselId) {
    return !!(carruselImages[carruselId] && carruselImages[carruselId][cardNumber]);
}

// ===== EVENTOS DE TECLADO =====

/**
 * Maneja eventos de teclado para funcionalidad adicional
 */
function initKeyboardEvents() {
    document.addEventListener('keydown', function(event) {
        // Cambiar pestañas con números 1, 2
        if (event.key >= '1' && event.key <= '2') {
            const tabIndex = parseInt(event.key) - 1;
            const tabButtons = document.querySelectorAll('.carrusel-tab-button');
            
            if (tabButtons[tabIndex]) {
                tabButtons[tabIndex].click();
            }
        }
        
        // Guardar imágenes con Ctrl+S
        if (event.ctrlKey && event.key === 's') {
            event.preventDefault();
            saveCarruselImages();
            showMessage('Imágenes guardadas', 'success');
        }
    });
}

// ===== INICIALIZACIÓN =====

/**
 * Inicializa todo el módulo del carrusel cuando el DOM está listo
 */
document.addEventListener('DOMContentLoaded', function() {
    // Inicializar pestañas
    initCarruselTabs();
    
    // Inicializar tarjetas
    initCarruselCards();
    
    // Inicializar eventos de teclado
    initKeyboardEvents();
    
    // Cargar imágenes guardadas
    loadCarruselImages();
    
    // Guardar imágenes automáticamente cada 30 segundos
    setInterval(saveCarruselImages, 30000);
    
    console.log('Módulo de carruseles inicializado correctamente');
});
