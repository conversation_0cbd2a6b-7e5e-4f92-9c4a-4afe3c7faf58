// ===== VARIABLES GLOBALES DE CONFIGURACIÓN DE MARCA =====
let marcaImages = {
    logo: null          // Almacena la imagen del logo
};

let logoShape = 'cuadrado'; // Forma del logo por defecto

// Variables para edición de imagen
let imageEditor = {
    originalImage: null,        // Imagen original sin modificaciones
    currentImage: null,         // Imagen con modificaciones actuales
    canvas: null,              // Canvas para edición
    ctx: null,                 // Contexto del canvas
    previewCanvas: null,       // Canvas de vista previa
    previewCtx: null,          // Contexto del canvas de vista previa
    transformations: {         // Transformaciones aplicadas
        rotation: 0,           // Rotación en grados
        flipH: false,          // Volteo horizontal
        flipV: false,          // Volteo vertical
        scale: 1,              // Escala (1 = normal)
        brightness: 100,       // Brillo (100 = normal)
        contrast: 100,         // Contraste (100 = normal)
        saturation: 100        // Saturación (100 = normal)
    }
};

// ===== FUNCIONES PRINCIPALES DE CONFIGURACIÓN DE MARCA =====

/**
 * Inicializa la funcionalidad del contenedor de marca
 */
function initMarcaContainer() {
    // Inicializar eventos de carga de imágenes
    initMarcaImageUpload();
    
    // Inicializar opciones de logo
    initLogoOptions();
    
    // Inicializar botones de acción
    initMarcaActionButtons();
    
    // Cargar datos guardados
    loadMarcaData();
    
    console.log('Módulo de configuración de marca inicializado correctamente');
}

/**
 * Inicializa los eventos de carga de imágenes
 */
function initMarcaImageUpload() {
    // Evento para cargar logo
    const logoCard = document.getElementById('logo-upload-card');
    if (logoCard) {
        logoCard.addEventListener('click', function(event) {
            // Evitar que se active si se hace clic en el icono de edición
            if (!event.target.closest('.edit-overlay')) {
                openMarcaImageSelector('logo');
            }
        });
    }
    

    
    // Eventos para iconos de edición
    const logoEditIcon = document.getElementById('logo-edit-icon');
    console.log('Logo edit icon encontrado:', logoEditIcon ? 'Sí' : 'No');
    if (logoEditIcon) {
        logoEditIcon.addEventListener('click', function(event) {
            console.log('Click en icono de edición de logo');
            event.stopPropagation();
            event.preventDefault();
            openMarcaImageEditor('logo');
        });

        // Agregar evento de hover para debug
        logoEditIcon.addEventListener('mouseenter', function() {
            console.log('Hover sobre icono de edición de logo');
        });
    }


}

/**
 * Inicializa las opciones de forma del logo
 */
function initLogoOptions() {
    const radioButtons = document.querySelectorAll('input[name="logo-shape"]');
    
    radioButtons.forEach(radio => {
        radio.addEventListener('change', function() {
            if (this.checked) {
                logoShape = this.value;
                updateLogoShape();
                showMarcaMessage(`Logo cambiado a forma ${logoShape}`, 'info');
            }
        });
    });
}

/**
 * Inicializa los botones de acción
 */
function initMarcaActionButtons() {
    // Botones del logo
    const logoSaveBtn = document.getElementById('logo-save-btn');
    const logoCancelBtn = document.getElementById('logo-cancel-btn');

    if (logoSaveBtn) {
        logoSaveBtn.addEventListener('click', function() {
            saveMarcaLogo();
        });
    }

    if (logoCancelBtn) {
        logoCancelBtn.addEventListener('click', function() {
            cancelMarcaLogo();
        });
    }

    // Botón cambiar imagen
    const changeImageBtn = document.getElementById('change-image-btn');
    if (changeImageBtn) {
        changeImageBtn.addEventListener('click', function() {
            openMarcaImageSelector('logo');
        });
    }

    // Inicializar herramientas de edición
    initImageEditor();
    

}

/**
 * Abre el selector de archivos para cargar una imagen
 * @param {string} type - Tipo de imagen ('logo' o 'banner')
 */
function openMarcaImageSelector(type) {
    // Crear input file temporal
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'image/*';
    input.onchange = function(event) {
        handleMarcaImageUpload(event, type);
    };
    input.click();
}

/**
 * Maneja la carga de imagen seleccionada
 * @param {Event} event - Evento del input file
 * @param {string} type - Tipo de imagen ('logo' o 'banner')
 */
function handleMarcaImageUpload(event, type) {
    const file = event.target.files[0];
    
    if (file) {
        // Validar tipo de archivo
        if (!file.type.startsWith('image/')) {
            showMarcaMessage('Por favor selecciona un archivo de imagen válido.', 'error');
            return;
        }
        
        // Validar tamaño (máximo 5MB)
        if (file.size > 5 * 1024 * 1024) {
            showMarcaMessage('La imagen debe ser menor a 5MB.', 'error');
            return;
        }
        
        // Crear URL temporal para preview
        const reader = new FileReader();
        reader.onload = function(e) {
            // Guardar imagen en el almacén global
            marcaImages[type] = {
                file: file,
                url: e.target.result,
                name: file.name,
                originalUrl: e.target.result // Para poder cancelar cambios
            };
            
            // Actualizar la tarjeta con la imagen
            updateMarcaImageCard(type, e.target.result);
            
            // Mostrar mensaje de éxito
            showMarcaMessage(`Imagen de ${type} cargada correctamente`, 'success');
        };
        reader.readAsDataURL(file);
    }
}

/**
 * Actualiza la tarjeta del logo con la imagen cargada
 * @param {string} type - Tipo de imagen (solo 'logo')
 * @param {string} imageUrl - URL de la imagen
 */
function updateMarcaImageCard(type, imageUrl) {
    if (type !== 'logo') return; // Solo manejar logo

    const card = document.getElementById('logo-upload-card');

    console.log(`Actualizando tarjeta ${type} con imagen:`, imageUrl ? 'Sí' : 'No');

    if (card) {
        // Establecer imagen como fondo
        card.style.backgroundImage = `url(${imageUrl})`;
        card.classList.add('has-image');

        console.log(`Clase has-image agregada a ${type}:`, card.classList.contains('has-image'));

        // Ocultar placeholder
        const placeholder = card.querySelector('.image-placeholder');
        if (placeholder) {
            placeholder.style.display = 'none';
        }

        // Verificar que el overlay de edición existe
        const editOverlay = card.querySelector('.edit-overlay');
        const editIcon = card.querySelector('.edit-icon');
        console.log(`Overlay de edición encontrado en ${type}:`, editOverlay ? 'Sí' : 'No');
        console.log(`Icono de edición encontrado en ${type}:`, editIcon ? 'Sí' : 'No');

        // Aplicar forma circular si es logo y está seleccionada
        if (type === 'logo' && logoShape === 'circular') {
            const logoColumn = card.parentElement;
            if (logoColumn) {
                logoColumn.classList.add('logo-circular');
            }
        }

        // Forzar un reflow para asegurar que los estilos se apliquen
        card.offsetHeight;

        // Mostrar botón cambiar imagen si es logo
        if (type === 'logo') {
            const changeImageBtn = document.getElementById('change-image-btn');
            if (changeImageBtn) {
                changeImageBtn.style.display = 'flex';
            }

            // Inicializar imagen en el editor
            initImageForEditor(imageUrl);
        }

        console.log(`Tarjeta ${type} actualizada exitosamente`);
    } else {
        console.error(`No se encontró la tarjeta ${cardId}`);
    }
}

/**
 * Actualiza la forma del logo
 */
function updateLogoShape() {
    const logoColumn = document.querySelector('.logo-column');
    
    if (logoColumn) {
        // Remover clases anteriores
        logoColumn.classList.remove('logo-circular', 'logo-cuadrado');
        
        // Agregar nueva clase
        if (logoShape === 'circular') {
            logoColumn.classList.add('logo-circular');
        } else {
            logoColumn.classList.add('logo-cuadrado');
        }
    }
}

/**
 * Función de edición de imágenes (eliminada)
 * @param {string} type - Tipo de imagen ('logo' o 'banner')
 */
function openMarcaImageEditor(type) {
    console.log('=== EDITOR DE IMAGEN ELIMINADO ===');
    showMarcaMessage('La función de edición de imágenes ha sido eliminada.', 'info');
}

/**
 * Guarda la configuración del logo
 */
function saveMarcaLogo() {
    if (!marcaImages.logo) {
        showMarcaMessage('No hay imagen de logo para guardar.', 'error');
        return;
    }
    
    try {
        // Guardar en localStorage
        const logoData = {
            url: marcaImages.logo.url,
            name: marcaImages.logo.name,
            shape: logoShape
        };
        
        localStorage.setItem('marcaLogo', JSON.stringify(logoData));
        
        // Marcar como guardado
        marcaImages.logo.saved = true;
        
        showMarcaMessage('Logo guardado correctamente', 'success');
        
        // Actualizar estado visual
        const logoCard = document.getElementById('logo-upload-card');
        if (logoCard) {
            logoCard.classList.add('success');
            setTimeout(() => {
                logoCard.classList.remove('success');
            }, 2000);
        }
        
    } catch (error) {
        console.error('Error al guardar logo:', error);
        showMarcaMessage('Error al guardar el logo', 'error');
    }
}

/**
 * Cancela los cambios del logo
 */
function cancelMarcaLogo() {
    if (marcaImages.logo && marcaImages.logo.originalUrl) {
        // Restaurar imagen original
        marcaImages.logo.url = marcaImages.logo.originalUrl;
        updateMarcaImageCard('logo', marcaImages.logo.originalUrl);
        showMarcaMessage('Cambios del logo cancelados', 'info');
    } else {
        // Remover imagen completamente
        removeMarcaImage('logo');
        showMarcaMessage('Logo removido', 'info');
    }
}





/**
 * Remueve la imagen del logo
 * @param {string} type - Tipo de imagen (solo 'logo')
 */
function removeMarcaImage(type) {
    if (type !== 'logo') return; // Solo manejar logo

    const card = document.getElementById('logo-upload-card');
    
    if (card) {
        // Remover imagen de fondo
        card.style.backgroundImage = '';
        card.classList.remove('has-image');
        
        // Mostrar placeholder
        const placeholder = card.querySelector('.image-placeholder');
        if (placeholder) {
            placeholder.style.display = 'flex';
        }
        
        // Eliminar del almacén global
        marcaImages.logo = null;

        // Ocultar botón cambiar imagen
        const changeImageBtn = document.getElementById('change-image-btn');
        if (changeImageBtn) {
            changeImageBtn.style.display = 'none';
        }
    }
}

/**
 * Carga los datos guardados desde localStorage
 */
function loadMarcaData() {
    try {
        // Cargar logo
        const savedLogo = localStorage.getItem('marcaLogo');
        if (savedLogo) {
            const logoData = JSON.parse(savedLogo);
            marcaImages.logo = {
                url: logoData.url,
                name: logoData.name,
                saved: true,
                originalUrl: logoData.url
            };
            
            logoShape = logoData.shape || 'cuadrado';
            
            // Actualizar interfaz
            updateMarcaImageCard('logo', logoData.url);
            
            // Actualizar radio button
            const radioButton = document.querySelector(`input[name="logo-shape"][value="${logoShape}"]`);
            if (radioButton) {
                radioButton.checked = true;
            }
            
            // Actualizar forma
            updateLogoShape();
        }
        

        
    } catch (error) {
        console.warn('No se pudieron cargar los datos de marca:', error);
    }
}

/**
 * Muestra un mensaje al usuario
 * @param {string} message - Mensaje a mostrar
 * @param {string} type - Tipo de mensaje ('success', 'error', 'info')
 */
function showMarcaMessage(message, type = 'info') {
    // Implementación básica con console.log (se puede mejorar con toast/notification)
    console.log(`[MARCA ${type.toUpperCase()}] ${message}`);
    
    // Opcional: mostrar alert solo para errores importantes
    if (type === 'error') {
        alert(message);
    }
}

// ===== FUNCIONES DE UTILIDAD =====

/**
 * Obtiene la configuración actual de la marca
 * @returns {Object} - Configuración de la marca
 */
function getMarcaConfig() {
    return {
        logo: marcaImages.logo,
        logoShape: logoShape
    };
}

/**
 * Verifica si hay cambios sin guardar
 * @returns {boolean} - True si hay cambios sin guardar
 */
function hasMarcaUnsavedChanges() {
    return (marcaImages.logo && !marcaImages.logo.saved);
}

// ===== INICIALIZACIÓN =====

/**
 * Función de verificación del editor (eliminada)
 */
function checkEditorAvailability() {
    console.log('Editor de imágenes eliminado');
    return false;
}

/**
 * Función de prueba para simular la apertura del editor
 */
function testMarcaEditor() {
    console.log('=== PRUEBA DEL EDITOR DE MARCA ===');

    // Crear datos de imagen de prueba
    const testImageData = {
        url: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iIzZiNDZjMSIvPjx0ZXh0IHg9IjUwIiB5PSI1NSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE0IiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSI+VEVTVDWVYVC90ZXh0Pjwvc3ZnPg==',
        name: 'test-image.svg',
        file: null
    };

    // Simular que hay una imagen cargada
    marcaImages.logo = testImageData;

    console.log('Datos de prueba creados:', testImageData);

    // Actualizar la tarjeta con la imagen de prueba
    updateMarcaImageCard('logo', testImageData.url);

    // Intentar abrir el editor
    openMarcaImageEditor('logo');
}

/**
 * Función de prueba para verificar el overlay de edición
 */
function testMarcaOverlay() {
    console.log('=== PRUEBA DEL OVERLAY DE EDICIÓN ===');

    const logoCard = document.getElementById('logo-upload-card');

    console.log('Tarjeta de logo encontrada:', logoCard ? 'Sí' : 'No');

    if (logoCard) {
        const overlay = logoCard.querySelector('.edit-overlay');
        const icon = logoCard.querySelector('.edit-icon');

        console.log('Overlay en logo:', overlay ? 'Sí' : 'No');
        console.log('Icono en logo:', icon ? 'Sí' : 'No');

        // Forzar mostrar el overlay para prueba
        if (overlay) {
            overlay.style.display = 'flex';
            overlay.style.opacity = '1';
            console.log('Overlay forzado a mostrarse');

            setTimeout(() => {
                overlay.style.display = '';
                overlay.style.opacity = '';
                console.log('Overlay restaurado');
            }, 3000);
        }
    }
}

/**
 * Inicializa el módulo cuando el DOM está listo
 */
document.addEventListener('DOMContentLoaded', function() {
    // Esperar un poco para asegurar que todos los módulos estén cargados
    setTimeout(function() {
        initMarcaContainer();
        console.log('Módulo de marca inicializado');

        // Verificar disponibilidad del editor después de la inicialización
        setTimeout(function() {
            const available = checkEditorAvailability();
            console.log('Editor disponible:', available ? 'Sí' : 'No');

            // Hacer funciones disponibles globalmente para pruebas
            window.testMarcaEditor = testMarcaEditor;
            window.testMarcaOverlay = testMarcaOverlay;
            window.checkEditorAvailability = checkEditorAvailability;
            window.openMarcaImageEditor = openMarcaImageEditor;

            console.log('Funciones de prueba disponibles globalmente');
            console.log('- Ejecuta testMarcaEditor() para probar el editor');
            console.log('- Ejecuta testMarcaOverlay() para probar el overlay');
            console.log('- Ejecuta checkEditorAvailability() para verificar disponibilidad');
        }, 500);
    }, 100);
});

// ===== FUNCIONES DEL EDITOR DE IMAGEN =====

/**
 * Inicializa el editor de imagen
 */
function initImageEditor() {
    console.log('Inicializando editor de imagen...');

    // Obtener canvas de vista previa
    imageEditor.previewCanvas = document.getElementById('logo-preview-canvas');
    if (imageEditor.previewCanvas) {
        imageEditor.previewCtx = imageEditor.previewCanvas.getContext('2d');
    }

    // Eventos de herramientas de transformación
    const rotateRightBtn = document.getElementById('rotate-right-btn');
    const rotateLeftBtn = document.getElementById('rotate-left-btn');
    const flipHorizontalBtn = document.getElementById('flip-horizontal-btn');
    const flipVerticalBtn = document.getElementById('flip-vertical-btn');
    const resetBtn = document.getElementById('reset-btn');

    if (rotateRightBtn) {
        rotateRightBtn.addEventListener('click', () => rotateImage(90));
    }
    if (rotateLeftBtn) {
        rotateLeftBtn.addEventListener('click', () => rotateImage(-90));
    }
    if (flipHorizontalBtn) {
        flipHorizontalBtn.addEventListener('click', () => flipImage('horizontal'));
    }
    if (flipVerticalBtn) {
        flipVerticalBtn.addEventListener('click', () => flipImage('vertical'));
    }
    if (resetBtn) {
        resetBtn.addEventListener('click', () => resetTransformations());
    }

    // Eventos de herramientas de escala
    const scaleUpBtn = document.getElementById('scale-up-btn');
    const scaleDownBtn = document.getElementById('scale-down-btn');

    if (scaleUpBtn) {
        scaleUpBtn.addEventListener('click', () => scaleImage(1.1));
    }
    if (scaleDownBtn) {
        scaleDownBtn.addEventListener('click', () => scaleImage(0.9));
    }

    // Eventos de sliders de ajuste
    const brightnessSlider = document.getElementById('brightness-slider');
    const contrastSlider = document.getElementById('contrast-slider');
    const saturationSlider = document.getElementById('saturation-slider');

    if (brightnessSlider) {
        brightnessSlider.addEventListener('input', (e) => {
            imageEditor.transformations.brightness = parseInt(e.target.value);
            updateAdjustmentValue('brightness', e.target.value);
            applyImageTransformations();
        });
    }

    if (contrastSlider) {
        contrastSlider.addEventListener('input', (e) => {
            imageEditor.transformations.contrast = parseInt(e.target.value);
            updateAdjustmentValue('contrast', e.target.value);
            applyImageTransformations();
        });
    }

    if (saturationSlider) {
        saturationSlider.addEventListener('input', (e) => {
            imageEditor.transformations.saturation = parseInt(e.target.value);
            updateAdjustmentValue('saturation', e.target.value);
            applyImageTransformations();
        });
    }

    // Eventos de botones +/- para responsive
    const adjustmentButtons = document.querySelectorAll('.adjustment-btn');
    adjustmentButtons.forEach(button => {
        button.addEventListener('click', (e) => {
            const adjustment = e.target.dataset.adjustment;
            const action = e.target.dataset.action;
            handleAdjustmentButton(adjustment, action);
        });
    });

    console.log('Editor de imagen inicializado');
}

/**
 * Inicializa una imagen en el editor
 * @param {string} imageUrl - URL de la imagen
 */
function initImageForEditor(imageUrl) {
    console.log('Inicializando imagen en editor:', imageUrl);

    const img = new Image();
    img.crossOrigin = 'anonymous';

    img.onload = function() {
        imageEditor.originalImage = img;
        imageEditor.currentImage = img;

        // Resetear transformaciones
        resetTransformations();

        // Renderizar imagen inicial
        renderImagePreview();

        console.log('Imagen cargada en editor:', img.width, 'x', img.height);
    };

    img.onerror = function() {
        console.error('Error al cargar imagen en editor');
    };

    img.src = imageUrl;
}

/**
 * Rota la imagen
 * @param {number} degrees - Grados de rotación
 */
function rotateImage(degrees) {
    imageEditor.transformations.rotation += degrees;
    imageEditor.transformations.rotation = imageEditor.transformations.rotation % 360;

    console.log('Rotando imagen:', degrees, 'grados. Total:', imageEditor.transformations.rotation);
    applyImageTransformations();
}

/**
 * Voltea la imagen
 * @param {string} direction - 'horizontal' o 'vertical'
 */
function flipImage(direction) {
    if (direction === 'horizontal') {
        imageEditor.transformations.flipH = !imageEditor.transformations.flipH;
        console.log('Volteo horizontal:', imageEditor.transformations.flipH);
    } else if (direction === 'vertical') {
        imageEditor.transformations.flipV = !imageEditor.transformations.flipV;
        console.log('Volteo vertical:', imageEditor.transformations.flipV);
    }

    applyImageTransformations();
}

/**
 * Escala la imagen
 * @param {number} factor - Factor de escala (1.1 para agrandar, 0.9 para achicar)
 */
function scaleImage(factor) {
    const newScale = imageEditor.transformations.scale * factor;

    // Limitar la escala entre 0.1 y 3.0
    if (newScale >= 0.1 && newScale <= 3.0) {
        imageEditor.transformations.scale = newScale;
        console.log('Escalando imagen:', factor, 'Nueva escala:', newScale.toFixed(2));
        applyImageTransformations();
    } else {
        console.log('Escala limitada. Escala actual:', imageEditor.transformations.scale.toFixed(2));
    }
}

/**
 * Resetea todas las transformaciones
 */
function resetTransformations() {
    imageEditor.transformations = {
        rotation: 0,
        flipH: false,
        flipV: false,
        scale: 1,
        brightness: 100,
        contrast: 100,
        saturation: 100
    };

    // Actualizar sliders
    updateSliderValues();

    console.log('Transformaciones reseteadas');
    applyImageTransformations();
}

/**
 * Actualiza los valores de los sliders y displays
 */
function updateSliderValues() {
    const brightnessSlider = document.getElementById('brightness-slider');
    const contrastSlider = document.getElementById('contrast-slider');
    const saturationSlider = document.getElementById('saturation-slider');

    if (brightnessSlider) {
        brightnessSlider.value = imageEditor.transformations.brightness;
        updateAdjustmentValue('brightness', imageEditor.transformations.brightness);
    }

    if (contrastSlider) {
        contrastSlider.value = imageEditor.transformations.contrast;
        updateAdjustmentValue('contrast', imageEditor.transformations.contrast);
    }

    if (saturationSlider) {
        saturationSlider.value = imageEditor.transformations.saturation;
        updateAdjustmentValue('saturation', imageEditor.transformations.saturation);
    }
}

/**
 * Actualiza el valor mostrado de un ajuste
 * @param {string} type - Tipo de ajuste
 * @param {number} value - Valor del ajuste
 */
function updateAdjustmentValue(type, value) {
    const valueElement = document.getElementById(`${type}-value`);
    if (valueElement) {
        valueElement.textContent = `${value}%`;
    }

    // También actualizar el display de botones +/-
    const displayElement = document.getElementById(`${type}-display`);
    if (displayElement) {
        displayElement.textContent = `${value}%`;
    }
}

/**
 * Maneja los clics en botones +/- de ajustes
 * @param {string} adjustment - Tipo de ajuste (brightness, contrast, saturation)
 * @param {string} action - Acción (increase, decrease)
 */
function handleAdjustmentButton(adjustment, action) {
    const step = 10; // Incremento/decremento de 10%
    let currentValue = imageEditor.transformations[adjustment];

    if (action === 'increase') {
        currentValue = Math.min(200, currentValue + step);
    } else if (action === 'decrease') {
        currentValue = Math.max(0, currentValue - step);
    }

    // Actualizar valor en el editor
    imageEditor.transformations[adjustment] = currentValue;

    // Actualizar slider correspondiente
    const slider = document.getElementById(`${adjustment}-slider`);
    if (slider) {
        slider.value = currentValue;
    }

    // Actualizar valores mostrados
    updateAdjustmentValue(adjustment, currentValue);

    // Aplicar transformaciones
    applyImageTransformations();

    console.log(`${adjustment} ajustado a ${currentValue}%`);
}

/**
 * Aplica todas las transformaciones a la imagen
 */
function applyImageTransformations() {
    if (!imageEditor.originalImage) return;

    // Crear canvas temporal para aplicar transformaciones
    const tempCanvas = document.createElement('canvas');
    const tempCtx = tempCanvas.getContext('2d');

    const img = imageEditor.originalImage;

    // Configurar tamaño del canvas considerando rotación
    if (imageEditor.transformations.rotation % 180 === 90) {
        tempCanvas.width = img.height;
        tempCanvas.height = img.width;
    } else {
        tempCanvas.width = img.width;
        tempCanvas.height = img.height;
    }

    // Aplicar transformaciones
    tempCtx.save();

    // Mover al centro para rotación
    tempCtx.translate(tempCanvas.width / 2, tempCanvas.height / 2);

    // Aplicar rotación
    tempCtx.rotate((imageEditor.transformations.rotation * Math.PI) / 180);

    // Aplicar escala y volteos
    const scaleX = (imageEditor.transformations.flipH ? -1 : 1) * imageEditor.transformations.scale;
    const scaleY = (imageEditor.transformations.flipV ? -1 : 1) * imageEditor.transformations.scale;
    tempCtx.scale(scaleX, scaleY);

    // Aplicar filtros CSS
    const brightness = imageEditor.transformations.brightness;
    const contrast = imageEditor.transformations.contrast;
    const saturation = imageEditor.transformations.saturation;

    tempCtx.filter = `brightness(${brightness}%) contrast(${contrast}%) saturate(${saturation}%)`;

    // Dibujar imagen
    tempCtx.drawImage(img, -img.width / 2, -img.height / 2);

    tempCtx.restore();

    // Actualizar imagen actual
    imageEditor.currentImage = tempCanvas;

    // Renderizar vista previa
    renderImagePreview();

    // Actualizar imagen principal
    updateMainImage();
}

/**
 * Renderiza la vista previa de la imagen
 */
function renderImagePreview() {
    if (!imageEditor.previewCanvas || !imageEditor.currentImage) return;

    const canvas = imageEditor.previewCanvas;
    const ctx = imageEditor.previewCtx;

    // Configurar tamaño del canvas de vista previa
    const maxSize = 200;
    const img = imageEditor.currentImage;

    let width, height;
    if (img.width > img.height) {
        width = Math.min(maxSize, img.width);
        height = (img.height * width) / img.width;
    } else {
        height = Math.min(maxSize, img.height);
        width = (img.width * height) / img.height;
    }

    canvas.width = width;
    canvas.height = height;

    // Limpiar canvas
    ctx.clearRect(0, 0, width, height);

    // Dibujar imagen
    ctx.drawImage(img, 0, 0, width, height);
}

/**
 * Actualiza la imagen principal con las transformaciones aplicadas
 */
function updateMainImage() {
    if (!imageEditor.currentImage) return;

    // Convertir canvas a URL
    const dataUrl = imageEditor.currentImage.toDataURL('image/png');

    // Actualizar imagen en la tarjeta principal
    const logoCard = document.getElementById('logo-upload-card');
    if (logoCard) {
        logoCard.style.backgroundImage = `url(${dataUrl})`;
    }

    // Actualizar datos de la imagen
    if (marcaImages.logo) {
        marcaImages.logo.url = dataUrl;
        marcaImages.logo.saved = false; // Marcar como no guardado
    }
}
