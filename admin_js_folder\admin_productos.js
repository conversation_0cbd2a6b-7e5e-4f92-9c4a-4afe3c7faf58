// Archivo JavaScript para administración de productos

// ===== FUNCIONALIDAD DE PESTAÑAS ===== 
document.addEventListener('DOMContentLoaded', function() {
    // Obtiene todos los botones de pestañas
    const tabButtons = document.querySelectorAll('.tab-button');
    // Obtiene todos los contenidos de pestañas
    const tabContents = document.querySelectorAll('.tab-content');
    
    // Agrega evento click a cada botón de pestaña
    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Obtiene el ID de la pestaña objetivo
            const targetTab = this.getAttribute('data-tab');
            
            // Remueve la clase active de todos los botones
            tabButtons.forEach(btn => btn.classList.remove('active'));
            // Remueve la clase active de todos los contenidos
            tabContents.forEach(content => content.classList.remove('active'));
            
            // Agrega la clase active al botón clickeado
            this.classList.add('active');
            // Agrega la clase active al contenido correspondiente
            document.getElementById(targetTab).classList.add('active');
        });
    });
});

// ===== FUNCIONALIDAD DEL SLIDER DE PRODUCTOS =====
let sliderPositions = {
    'destacados': 0,      // Posición actual del slider de destacados
    'ofertas': 0,         // Posición actual del slider de ofertas
    'novedades': 0,       // Posición actual del slider de novedades
    'mas-vistos': 0,      // Posición actual del slider de No te lo Pierdas
    'tendencias': 0,      // Posición actual del slider de tendencias
    'liquidaciones': 0    // Posición actual del slider de liquidaciones
};

// Función para mover el slider de productos
function moveSlider(tabName, direction) {
    // Obtiene el contenedor del slider específico
    const slider = document.getElementById(`slider-${tabName}`);
    // Obtiene todas las tarjetas del slider
    const cards = slider.querySelectorAll('.producto-card');
    // Calcula el ancho de una tarjeta más el gap
    const cardWidth = 300; // 280px de ancho + 20px de gap
    // Número máximo de tarjetas visibles a la vez
    const visibleCards = 4;
    // Calcula el número máximo de posiciones
    const maxPosition = Math.max(0, cards.length - visibleCards);
    
    // Actualiza la posición según la dirección
    sliderPositions[tabName] += direction;
    
    // Limita la posición dentro de los rangos válidos
    if (sliderPositions[tabName] < 0) {
        sliderPositions[tabName] = 0; // No permite ir más a la izquierda
    }
    if (sliderPositions[tabName] > maxPosition) {
        sliderPositions[tabName] = maxPosition; // No permite ir más a la derecha
    }
    
    // Calcula el desplazamiento en píxeles
    const translateX = -sliderPositions[tabName] * cardWidth;
    
    // Aplica la transformación al slider
    slider.style.transform = `translateX(${translateX}px)`;
    
    // Actualiza la visibilidad de las flechas
    updateArrowVisibility(tabName, maxPosition);
}

// Función para actualizar la visibilidad de las flechas
function updateArrowVisibility(tabName, maxPosition) {
    // Obtiene el contenedor del slider
    const container = document.querySelector(`#${tabName} .productos-slider-container`);
    // Obtiene las flechas del slider
    const prevArrow = container.querySelector('.slider-arrow.prev');
    const nextArrow = container.querySelector('.slider-arrow.next');
    
    // Oculta la flecha izquierda si está en la primera posición
    if (sliderPositions[tabName] === 0) {
        prevArrow.style.opacity = '0.3';
        prevArrow.style.cursor = 'not-allowed';
    } else {
        prevArrow.style.opacity = '1';
        prevArrow.style.cursor = 'pointer';
    }
    
    // Oculta la flecha derecha si está en la última posición
    if (sliderPositions[tabName] >= maxPosition) {
        nextArrow.style.opacity = '0.3';
        nextArrow.style.cursor = 'not-allowed';
    } else {
        nextArrow.style.opacity = '1';
        nextArrow.style.cursor = 'pointer';
    }
}

// ===== INICIALIZACIÓN DE FLECHAS =====
document.addEventListener('DOMContentLoaded', function() {
    // Lista de todas las pestañas
    const tabs = ['destacados', 'ofertas', 'novedades', 'mas-vistos', 'tendencias', 'liquidaciones'];
    
    // Inicializa la visibilidad de flechas para cada pestaña
    tabs.forEach(tabName => {
        const slider = document.getElementById(`slider-${tabName}`);
        const cards = slider.querySelectorAll('.producto-card');
        const visibleCards = 4;
        const maxPosition = Math.max(0, cards.length - visibleCards);
        
        // Actualiza la visibilidad inicial de las flechas
        updateArrowVisibility(tabName, maxPosition);
    });
});

// ===== FUNCIONALIDAD PARA AGREGAR PRODUCTOS ELIMINADA =====
// Las funciones de agregar productos han sido eliminadas del sistema

// ===== FUNCIONALIDAD DE HOVER EN TARJETAS =====
document.addEventListener('DOMContentLoaded', function() {
    // Obtiene todas las tarjetas de productos (excluyendo las de agregar)
    const productCards = document.querySelectorAll('.producto-card:not(.add-producto-card)');
    
    // Agrega efectos de hover adicionales si es necesario
    productCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            // Efecto adicional al pasar el mouse (opcional)
            this.style.borderLeftWidth = '6px';
        });
        
        card.addEventListener('mouseleave', function() {
            // Restaura el estado original al quitar el mouse
            this.style.borderLeftWidth = '4px';
        });
    });
});

// ===== FUNCIONALIDAD DE TECLADO PARA NAVEGACIÓN =====
document.addEventListener('keydown', function(event) {
    // Obtiene la pestaña activa actual
    const activeTab = document.querySelector('.tab-content.active');
    if (!activeTab) return;
    
    const tabId = activeTab.id;
    
    // Navegación con flechas del teclado
    if (event.key === 'ArrowLeft') {
        event.preventDefault();
        moveSlider(tabId, -1); // Mueve hacia la izquierda
    } else if (event.key === 'ArrowRight') {
        event.preventDefault();
        moveSlider(tabId, 1);  // Mueve hacia la derecha
    }
});

// ===== FUNCIONALIDAD DE REDIMENSIONAMIENTO DE VENTANA =====
window.addEventListener('resize', function() {
    // Recalcula las posiciones de los sliders al redimensionar la ventana
    const tabs = ['destacados', 'ofertas', 'novedades', 'mas-vistos', 'tendencias', 'liquidaciones'];
    
    tabs.forEach(tabName => {
        // Resetea la posición del slider
        sliderPositions[tabName] = 0;
        const slider = document.getElementById(`slider-${tabName}`);
        slider.style.transform = 'translateX(0px)';
        
        // Recalcula la visibilidad de las flechas
        const cards = slider.querySelectorAll('.producto-card');
        const visibleCards = 4;
        const maxPosition = Math.max(0, cards.length - visibleCards);
        updateArrowVisibility(tabName, maxPosition);
    });
});

// ===== FUNCIONALIDAD DE AUTO-SCROLL (OPCIONAL) =====
let autoScrollIntervals = {};

// Función para iniciar auto-scroll en una pestaña
function startAutoScroll(tabName, interval = 5000) {
    // Detiene cualquier auto-scroll existente
    stopAutoScroll(tabName);
    
    // Inicia un nuevo intervalo de auto-scroll
    autoScrollIntervals[tabName] = setInterval(() => {
        const slider = document.getElementById(`slider-${tabName}`);
        const cards = slider.querySelectorAll('.producto-card');
        const visibleCards = 4;
        const maxPosition = Math.max(0, cards.length - visibleCards);
        
        // Si llegó al final, vuelve al inicio
        if (sliderPositions[tabName] >= maxPosition) {
            sliderPositions[tabName] = 0;
            slider.style.transform = 'translateX(0px)';
        } else {
            // Avanza una posición
            moveSlider(tabName, 1);
        }
    }, interval);
}

// Función para detener auto-scroll en una pestaña
function stopAutoScroll(tabName) {
    if (autoScrollIntervals[tabName]) {
        clearInterval(autoScrollIntervals[tabName]);
        delete autoScrollIntervals[tabName];
    }
}

// ===== EVENTOS PARA PAUSAR AUTO-SCROLL AL INTERACTUAR =====
document.addEventListener('DOMContentLoaded', function() {
    const sliderContainers = document.querySelectorAll('.productos-slider-container');
    
    sliderContainers.forEach(container => {
        const tabContent = container.closest('.tab-content');
        const tabName = tabContent.id;
        
        // Pausa el auto-scroll al pasar el mouse sobre el slider
        container.addEventListener('mouseenter', () => {
            stopAutoScroll(tabName);
        });
        
        // Reinicia el auto-scroll al quitar el mouse del slider
        container.addEventListener('mouseleave', () => {
            // Solo reinicia si la pestaña está activa
            if (tabContent.classList.contains('active')) {
                startAutoScroll(tabName);
            }
        });
    });
    
    // Controla el auto-scroll al cambiar de pestaña
    const tabButtons = document.querySelectorAll('.tab-button');
    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetTab = this.getAttribute('data-tab');
            
            // Detiene todos los auto-scrolls
            Object.keys(autoScrollIntervals).forEach(tabName => {
                stopAutoScroll(tabName);
            });
            
            // Inicia auto-scroll en la nueva pestaña activa (opcional)
            // startAutoScroll(targetTab);
        });
    });
});
   

// ===== FUNCIONALIDAD DEL POP-UP DE PRODUCTO (SOLO RESPONSIVE) =====

// Función para verificar si estamos en modo responsive
function isResponsiveMode() {
    return window.innerWidth <= 1024; // Solo se activa en pantallas ≤1024px
}

// Función para abrir el pop-up con información del producto (solo en responsive)
function openProductPopup(productData) {
    // Verifica si estamos en modo responsive
    if (!isResponsiveMode()) {
        return; // No hace nada en desktop
    }
    
    // Obtiene el overlay del pop-up
    const popup = document.getElementById('producto-popup');
    
    // Rellena la información del producto en el pop-up
    const popupImageElement = document.getElementById('popup-image');

    // Maneja la imagen del producto
    if (productData.imageUrl && productData.imageUrl !== 'Imagen del Producto') {
        // Si hay una imagen real, la muestra
        popupImageElement.style.backgroundImage = `url('${productData.imageUrl}')`;
        popupImageElement.style.backgroundSize = 'cover';
        popupImageElement.style.backgroundPosition = 'center';
        popupImageElement.textContent = '';
    } else {
        // Si no hay imagen, muestra el texto placeholder
        popupImageElement.style.backgroundImage = 'none';
        popupImageElement.textContent = productData.image || 'Imagen del Producto';
    }

    document.getElementById('popup-categoria').textContent = productData.categoria || 'Sin categoría';
    document.getElementById('popup-nombre').textContent = productData.nombre || 'Producto sin nombre';
    document.getElementById('popup-precio-actual').textContent = productData.precioActual || '$0.00';
    
    // Maneja el precio anterior (puede no existir)
    const precioAnteriorElement = document.getElementById('popup-precio-anterior');
    if (productData.precioAnterior) {
        precioAnteriorElement.textContent = productData.precioAnterior;
        precioAnteriorElement.style.display = 'inline';
    } else {
        precioAnteriorElement.style.display = 'none';
    }
    
    document.getElementById('popup-seccion').textContent = productData.seccion || 'Sin sección';
    
    // Muestra el pop-up
    popup.classList.add('active');
    
    // Previene el scroll del body cuando el pop-up está abierto
    preventBodyScroll();
}

// Función para cerrar el pop-up
function closeProductPopup() {
    const popup = document.getElementById('producto-popup');
    popup.classList.remove('active');
    
    // Restaura el scroll del body
    restoreBodyScroll();
}

// Función para extraer datos de una tarjeta de producto
function extractProductData(card) {
    const data = {};
    
    // Extrae la información de la tarjeta
    const categoriaElement = card.querySelector('.producto-categoria');
    const nombreElement = card.querySelector('.producto-nombre');
    const precioActualElement = card.querySelector('.precio-actual');
    const precioAnteriorElement = card.querySelector('.precio-anterior');
    const seccionElement = card.querySelector('.seccion-texto'); // Actualizado para usar .seccion-texto
    const imageElement = card.querySelector('.producto-image');

    // Asigna los valores extraídos
    data.categoria = categoriaElement ? categoriaElement.textContent : '';
    data.nombre = nombreElement ? nombreElement.textContent : '';
    data.precioActual = precioActualElement ? precioActualElement.textContent : '';
    data.precioAnterior = precioAnteriorElement ? precioAnteriorElement.textContent : '';
    data.seccion = seccionElement ? seccionElement.textContent : '';
    data.image = imageElement ? imageElement.textContent : '';

    // Extrae la URL de la imagen de fondo si existe
    if (imageElement) {
        const backgroundImage = window.getComputedStyle(imageElement).backgroundImage;
        if (backgroundImage && backgroundImage !== 'none') {
            // Extrae la URL de la imagen del CSS background-image
            const urlMatch = backgroundImage.match(/url\(['"]?(.*?)['"]?\)/);
            if (urlMatch && urlMatch[1]) {
                data.imageUrl = urlMatch[1];
            }
        }

        // También verifica si hay una imagen inline en el style
        const inlineStyle = imageElement.getAttribute('style');
        if (inlineStyle && inlineStyle.includes('background-image')) {
            const urlMatch = inlineStyle.match(/background-image:\s*url\(['"]?(.*?)['"]?\)/);
            if (urlMatch && urlMatch[1]) {
                data.imageUrl = urlMatch[1];
            }
        }
    }
    
    return data;
}

// ===== FUNCIÓN PARA ENCONTRAR TARJETA POR NOMBRE =====
function findProductCardByName(productName) {
    console.log('🔍 Buscando tarjeta del producto:', `"${productName}"`);

    // Busca en todas las tarjetas de productos
    const allProductCards = document.querySelectorAll('.producto-card:not(.add-producto-card)');
    console.log('📋 Total de tarjetas a revisar:', allProductCards.length);

    for (let i = 0; i < allProductCards.length; i++) {
        const card = allProductCards[i];
        const nombreElement = card.querySelector('.producto-nombre');

        if (nombreElement) {
            const cardName = nombreElement.textContent.trim();
            console.log(`  ${i + 1}. Comparando: "${cardName}" vs "${productName}"`);

            if (cardName === productName.trim()) {
                console.log('✅ Tarjeta encontrada para:', productName);
                return card;
            }
        } else {
            console.warn(`  ${i + 1}. Tarjeta sin elemento .producto-nombre`);
        }
    }

    console.warn('⚠️ No se encontró tarjeta para:', productName);
    console.log('🔍 Intentando búsqueda más flexible...');

    // Búsqueda más flexible (ignora mayúsculas/minúsculas y espacios extra)
    for (let i = 0; i < allProductCards.length; i++) {
        const card = allProductCards[i];
        const nombreElement = card.querySelector('.producto-nombre');

        if (nombreElement) {
            const cardName = nombreElement.textContent.trim().toLowerCase();
            const searchName = productName.trim().toLowerCase();

            if (cardName === searchName) {
                console.log('✅ Tarjeta encontrada con búsqueda flexible:', productName);
                return card;
            }
        }
    }

    console.error('❌ No se encontró tarjeta incluso con búsqueda flexible');
    return null;
}

// ===== FUNCIÓN GLOBAL PARA ELIMINAR TARJETA DE PRODUCTO =====
function deleteProductCard(card, productData) {
    try {
        console.log('🗑️ Eliminando tarjeta del producto:', productData.nombre || 'Producto sin nombre');

        if (!card) {
            console.error('❌ No se proporcionó tarjeta para eliminar');
            return;
        }

        // Encuentra el slider padre
        const slider = card.closest('.productos-slider');
        console.log('📍 Slider padre encontrado:', !!slider);

        // Elimina la tarjeta con animación suave
        card.style.transition = 'all 0.3s ease';
        card.style.transform = 'scale(0.8)';
        card.style.opacity = '0';
        card.style.pointerEvents = 'none'; // Evita clics durante la animación

        setTimeout(() => {
            // Verifica que la tarjeta aún exista antes de eliminarla
            if (card.parentNode) {
                card.remove();
                console.log('🗑️ Tarjeta removida del DOM');
            }

            // Actualiza la navegación del slider si es necesario
            if (slider) {
                const sliderId = slider.id;
                if (sliderId) {
                    const sectionName = sliderId.replace('slider-', '');
                    console.log('🔄 Actualizando navegación para sección:', sectionName);

                    // Verifica si la función updateSliderNavigation existe
                    if (typeof updateSliderNavigation === 'function') {
                        updateSliderNavigation(sectionName);
                    } else {
                        console.warn('⚠️ Función updateSliderNavigation no encontrada');
                    }
                }
            }

            // Actualiza los KPIs inmediatamente después de eliminar
            console.log('📊 Actualizando KPIs después de eliminación...');
            if (typeof updateProductKPIs === 'function') {
                updateProductKPIs();
                // Fuerza actualización adicional para asegurar
                if (typeof forceKPIUpdate === 'function') {
                    forceKPIUpdate();
                }
            } else {
                console.warn('⚠️ Función updateProductKPIs no encontrada');
            }

            console.log('✅ Eliminación completada exitosamente');
        }, 300);

    } catch (error) {
        console.error('❌ Error al eliminar tarjeta:', error);
        // Si hay error, elimina sin animación como fallback
        try {
            if (card && card.parentNode) {
                card.remove();
                console.log('🗑️ Tarjeta eliminada sin animación (fallback)');

                // Actualiza los KPIs también en el fallback
                console.log('📊 Actualizando KPIs después de eliminación (fallback)...');
                if (typeof updateProductKPIs === 'function') {
                    updateProductKPIs();
                    // Fuerza actualización adicional para asegurar
                    if (typeof forceKPIUpdate === 'function') {
                        forceKPIUpdate();
                    }
                }
            }
        } catch (fallbackError) {
            console.error('❌ Error crítico en eliminación:', fallbackError);
        }
    }
}

// ===== FUNCIÓN PARA OBTENER DATOS DEL POPUP ACTUAL =====
function getCurrentPopupData() {
    console.log('📋 Extrayendo datos del popup actual...');
    const data = {};

    // Extrae los datos del popup actual
    const categoriaElement = document.getElementById('popup-categoria');
    const nombreElement = document.getElementById('popup-nombre');
    const precioActualElement = document.getElementById('popup-precio-actual');
    const precioAnteriorElement = document.getElementById('popup-precio-anterior');
    const seccionElement = document.getElementById('popup-seccion');

    data.categoria = categoriaElement ? categoriaElement.textContent.trim() : '';
    data.nombre = nombreElement ? nombreElement.textContent.trim() : '';
    data.precioActual = precioActualElement ? precioActualElement.textContent.trim() : '';
    data.precioAnterior = precioAnteriorElement ? precioAnteriorElement.textContent.trim() : '';
    data.seccion = seccionElement ? seccionElement.textContent.trim() : '';

    console.log('📋 Datos extraídos del popup:', {
        categoria: data.categoria,
        nombre: data.nombre,
        precioActual: data.precioActual,
        precioAnterior: data.precioAnterior,
        seccion: data.seccion
    });

    // Extrae la imagen del popup
    const popupImageElement = document.getElementById('popup-image');
    if (popupImageElement) {
        const backgroundImage = window.getComputedStyle(popupImageElement).backgroundImage;
        console.log('📋 Background image del popup:', backgroundImage);

        if (backgroundImage && backgroundImage !== 'none' && backgroundImage !== 'initial') {
            const urlMatch = backgroundImage.match(/url\(['"]?(.*?)['"]?\)/);
            if (urlMatch && urlMatch[1]) {
                data.imageUrl = urlMatch[1];
                console.log('📋 URL de imagen extraída:', data.imageUrl);
            }
        } else {
            data.imageUrl = null;
            console.log('📋 No se encontró imagen en el popup');
        }
    }

    return data;
}

// ===== PREVENCIÓN DE SCROLL EN DISPOSITIVOS MÓVILES =====
let scrollPosition = 0;

// Función para prevenir scroll en móviles
function preventBodyScroll() {
    scrollPosition = window.pageYOffset;
    document.body.style.overflow = 'hidden';
    document.body.style.position = 'fixed';
    document.body.style.top = `-${scrollPosition}px`;
    document.body.style.width = '100%';
}

// Función para restaurar scroll en móviles
function restoreBodyScroll() {
    document.body.style.removeProperty('overflow');
    document.body.style.removeProperty('position');
    document.body.style.removeProperty('top');
    document.body.style.removeProperty('width');
    window.scrollTo(0, scrollPosition);
}

// ===== EVENTOS DEL POP-UP =====
document.addEventListener('DOMContentLoaded', function() {
    
    // Agrega evento click a todas las tarjetas de productos (excluyendo las de agregar)
    function addProductCardListeners() {
        const productCards = document.querySelectorAll('.producto-card:not(.add-producto-card)');
        
        productCards.forEach(card => {
            // Remueve listeners existentes para evitar duplicados
            card.removeEventListener('click', handleProductCardClick);
            // Agrega el nuevo listener
            card.addEventListener('click', handleProductCardClick);
        });
    }
    
    // Manejador del click en tarjetas de producto
    function handleProductCardClick(event) {
        event.preventDefault();
        event.stopPropagation();
        
        // Solo funciona en modo responsive
        if (!isResponsiveMode()) {
            return; // No hace nada en desktop
        }
        
        // Extrae los datos del producto de la tarjeta clickeada
        const productData = extractProductData(this);
        
        // Abre el pop-up con los datos extraídos
        openProductPopup(productData);
    }
    
    // Inicializa los listeners
    addProductCardListeners();
    
    // Reinicializa los listeners cuando cambian las pestañas
    const tabButtons = document.querySelectorAll('.tab-button');
    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Espera un poco para que el contenido de la pestaña se cargue
            setTimeout(() => {
                addProductCardListeners();
            }, 100);
        });
    });
    
    // Cierra el pop-up al hacer click en el overlay (fondo)
    const popupOverlay = document.getElementById('producto-popup');
    if (popupOverlay) {
        popupOverlay.addEventListener('click', function(event) {
            // Solo cierra si se hace click en el overlay, no en el contenido
            if (event.target === this) {
                closeProductPopup();
            }
        });
    }
    
    // Cierra el pop-up con la tecla Escape (solo en responsive)
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape' && isResponsiveMode()) {
            const popup = document.getElementById('producto-popup');
            if (popup && popup.classList.contains('active')) {
                closeProductPopup();
            }
        }
    });
    
    // ===== FUNCIONALIDAD DE LOS BOTONES DE ACCIÓN =====
    
    // Botón de editar
    document.addEventListener('click', function(event) {
        if (event.target.classList.contains('popup-btn-edit')) {
            console.log('✏️ Click detectado en botón editar del popup');

            // Obtiene los datos del producto actual del popup
            const currentProductData = getCurrentPopupData();
            console.log('📋 Datos del producto obtenidos para edición:', currentProductData);

            // Encuentra la tarjeta original en el DOM
            const originalCard = findProductCardByName(currentProductData.nombre);

            if (originalCard) {
                // Cierra el popup
                closeProductPopup();

                // Pequeño delay para asegurar que el popup se cierre antes de abrir el modal
                setTimeout(() => {
                    // Abre el modal de edición usando la función del modal de productos
                    if (typeof abrirModalProducto === 'function') {
                        console.log('🚀 Abriendo modal de edición desde popup...');
                        abrirModalProducto(originalCard);
                    } else {
                        console.error('❌ Función abrirModalProducto no está disponible');
                        console.log('🔍 Funciones disponibles:', Object.getOwnPropertyNames(window).filter(name => name.includes('modal')));
                    }
                }, 300);
            } else {
                console.error('❌ No se encontró la tarjeta original del producto:', currentProductData.nombre);
            }
        }
    });
    
    // Botón de eliminar
    document.addEventListener('click', function(event) {
        if (event.target.classList.contains('popup-btn-delete')) {
            console.log('🔘 Click detectado en botón eliminar del popup');
            console.log('🔍 Elemento clickeado:', event.target);
            console.log('🔍 Clases del elemento:', event.target.classList.toString());

            // Obtiene los datos del producto actual del popup
            const currentProductData = getCurrentPopupData();
            console.log('📋 Datos del producto obtenidos:', currentProductData);

            // Verifica que se obtuvieron los datos
            if (!currentProductData || !currentProductData.nombre) {
                console.error('❌ No se pudieron obtener los datos del producto del popup');
                showError('Error: No se pudieron obtener los datos del producto.');
                return;
            }

            // Confirma antes de eliminar
            if (confirm(`¿Estás seguro de que quieres eliminar el producto "${currentProductData.nombre}"?`)) {
                console.log('🗑️ Eliminando producto desde popup:', currentProductData.nombre);

                // Encuentra la tarjeta original en el DOM
                const originalCard = findProductCardByName(currentProductData.nombre);
                console.log('🔍 Tarjeta original encontrada:', !!originalCard);

                if (originalCard) {
                    // Cierra el popup primero
                    console.log('🚪 Cerrando popup...');
                    closeProductPopup();

                    // Elimina la tarjeta
                    console.log('🗑️ Eliminando tarjeta...');
                    deleteProductCard(originalCard, currentProductData);

                    // Muestra mensaje de éxito
                    showSuccess(`Producto "${currentProductData.nombre}" eliminado exitosamente.`);
                } else {
                    console.error('❌ No se encontró la tarjeta original del producto:', currentProductData.nombre);
                    console.log('🔍 Buscando todas las tarjetas disponibles...');

                    // Debug: muestra todas las tarjetas disponibles
                    const allCards = document.querySelectorAll('.producto-card:not(.add-producto-card)');
                    console.log('📋 Tarjetas encontradas:', allCards.length);
                    allCards.forEach((card, index) => {
                        const nombreElement = card.querySelector('.producto-nombre');
                        const nombre = nombreElement ? nombreElement.textContent.trim() : 'Sin nombre';
                        console.log(`  ${index + 1}. "${nombre}"`);
                    });

                    showError('Error: No se pudo encontrar el producto para eliminar.');
                }
            } else {
                console.log('❌ Eliminación cancelada por el usuario');
            }
        }
    });
});

// ===== MANEJO DE REDIMENSIONAMIENTO DE VENTANA =====
window.addEventListener('resize', function() {
    // Si cambiamos a desktop mientras el pop-up está abierto, lo cerramos
    if (!isResponsiveMode()) {
        const popup = document.getElementById('producto-popup');
        if (popup && popup.classList.contains('active')) {
            closeProductPopup();
        }
    }
});

// ===== FUNCIONES AUXILIARES =====

// Función para actualizar los listeners después de cambios dinámicos
function updateProductCardListeners() {
    const productCards = document.querySelectorAll('.producto-card:not(.add-producto-card)');
    
    productCards.forEach(card => {
        card.removeEventListener('click', handleProductCardClick);
        card.addEventListener('click', handleProductCardClick);
    });
}

// Función global para manejar el click en tarjetas
function handleProductCardClick(event) {
    event.preventDefault();
    event.stopPropagation();
    
    // Solo funciona en modo responsive
    if (!isResponsiveMode()) {
        return;
    }
    
    const productData = extractProductData(this);
    openProductPopup(productData);
}

// ===== FUNCIONALIDAD DE ICONOS DE EDITAR Y ELIMINAR EN MODO NORMAL =====
document.addEventListener('DOMContentLoaded', function() {
    // Función para agregar listeners a los iconos de acción
    function addActionIconListeners() {
        console.log('🔧 Agregando listeners a iconos de acción...');

        // Iconos de editar
        const editIcons = document.querySelectorAll('.edit-icon');
        console.log(`✏️ Encontrados ${editIcons.length} iconos de editar`);

        editIcons.forEach((icon, index) => {
            console.log(`📝 Configurando icono de editar ${index + 1}`);

            icon.removeEventListener('click', handleEditClick); // Evita duplicados
            icon.addEventListener('click', handleEditClick);

            // Asegurar visibilidad y funcionalidad
            icon.style.cursor = 'pointer';
            icon.style.display = 'flex';
            icon.style.alignItems = 'center';
            icon.style.justifyContent = 'center';
            icon.style.opacity = '1';
            icon.style.visibility = 'visible';
            icon.style.pointerEvents = 'auto';
            icon.setAttribute('data-action', 'edit');
        });

        // Iconos de eliminar
        const deleteIcons = document.querySelectorAll('.delete-icon');
        console.log(`🗑️ Encontrados ${deleteIcons.length} iconos de eliminar`);

        deleteIcons.forEach((icon, index) => {
            console.log(`🗑️ Configurando icono de eliminar ${index + 1}`);

            icon.removeEventListener('click', handleDeleteClick); // Evita duplicados
            icon.addEventListener('click', handleDeleteClick);

            // Asegurar visibilidad y funcionalidad
            icon.style.cursor = 'pointer';
            icon.style.display = 'flex';
            icon.style.alignItems = 'center';
            icon.style.justifyContent = 'center';
            icon.style.opacity = '1';
            icon.style.visibility = 'visible';
            icon.style.pointerEvents = 'auto';
            icon.setAttribute('data-action', 'delete');
        });
    }

    // Manejador para el click en editar
    function handleEditClick(event) {
        event.stopPropagation(); // Evita que se active el click de la tarjeta

        // Encuentra la tarjeta padre
        const card = event.target.closest('.producto-card');
        if (card) {
            console.log('✏️ Editando producto desde icono en modo normal');

            // Abre el modal de edición usando la función del modal de productos
            if (typeof abrirModalProducto === 'function') {
                abrirModalProducto(card);
            } else {
                console.error('❌ Función abrirModalProducto no está disponible');
            }
        }
    }

    // Manejador para el click en eliminar
    function handleDeleteClick(event) {
        event.stopPropagation(); // Evita que se active el click de la tarjeta

        // Encuentra la tarjeta padre
        const card = event.target.closest('.producto-card');
        if (card) {
            // Extrae los datos del producto
            const productData = extractProductData(card);

            // Confirma antes de eliminar
            if (confirm(`¿Estás seguro de que quieres eliminar el producto "${productData.nombre}"?`)) {
                console.log('🗑️ Eliminando producto:', productData.nombre);

                // Elimina la tarjeta del DOM
                deleteProductCard(card, productData);

                // Muestra mensaje de éxito
                showSuccess(`Producto "${productData.nombre}" eliminado exitosamente.`);

                // TODO: En una implementación real, aquí harías la llamada al servidor
                /*
                fetch('api/delete_product.php', {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ productId: productData.id })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        deleteProductCard(card, productData);
                        showSuccess(`Producto "${productData.nombre}" eliminado exitosamente.`);
                    } else {
                        showError('Error al eliminar el producto: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showError('Error de conexión al eliminar el producto.');
                });
                */
            }
        }
    }

    // Inicializa los listeners
    console.log('🚀 Inicializando listeners de iconos de acción...');
    addActionIconListeners();

    // Reinicializa los listeners cuando cambian las pestañas
    const tabButtons = document.querySelectorAll('.tab-button');
    console.log(`🔄 Encontrados ${tabButtons.length} botones de pestaña`);
    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            console.log('📑 Cambio de pestaña detectado, re-inicializando listeners...');
            // Espera un poco para que el contenido de la pestaña se cargue
            setTimeout(() => {
                addActionIconListeners();
            }, 100);
        });
    });

    // Forzar re-inicialización después de un delay para asegurar que todo esté cargado
    setTimeout(() => {
        console.log('⏰ Re-inicializando listeners después del delay...');
        addActionIconListeners();
    }, 2000);
});

// ===== FUNCIONALIDAD DEL MODAL ELIMINADA =====
// Todas las funciones de modal han sido eliminadas del sistema

// ===== FUNCIONES DE MODAL ELIMINADAS =====
// Todas las funciones de modal han sido eliminadas del sistema

// ===== TODAS LAS FUNCIONES DE MODAL HAN SIDO ELIMINADAS =====

function handleImageUpload(file) {
    // Valida el tipo de archivo
    if (!file.type.startsWith('image/')) {
        showError('Por favor selecciona un archivo de imagen válido.');
        return;
    }
    
    // Valida el tamaño del archivo (máximo 5MB)
    if (file.size > 5 * 1024 * 1024) {
        showError('La imagen no puede ser mayor a 5MB.');
        return;
    }
    
    selectedImageFile = file;
    
    // Crea un FileReader para mostrar la preview
    const reader = new FileReader();
    reader.onload = function(e) {
        showImagePreview(e.target.result);
    };
    reader.readAsDataURL(file);
}

function showImagePreview(imageSrc) {
    console.log('🖼️ Mostrando preview de imagen:', imageSrc);
    const imagePreview = document.getElementById('image-preview');
    if (imagePreview) {
        imagePreview.innerHTML = `<img src="${imageSrc}" alt="Preview" class="preview-image">`;
        imagePreview.classList.add('has-image');
        console.log('✅ Preview de imagen establecido');
    } else {
        console.error('❌ No se encontró el elemento image-preview');
    }
}

function resetImagePreview() {
    const imagePreview = document.getElementById('image-preview');
    imagePreview.innerHTML = `
        <div class="image-placeholder">
            <span class="upload-icon">📷</span>
            <span class="upload-text">Haz clic para subir imagen</span>
        </div>
    `;
    imagePreview.classList.remove('has-image');
}

// ===== FUNCIONALIDAD DE CATEGORÍAS =====
function loadCategories() {
    // Simulación de carga de categorías (aquí se implementará AJAX/PHP)
    // Por ahora usamos datos de ejemplo
    const exampleCategories = [
        { id: 1, name: 'Electrónicos' },
        { id: 2, name: 'Ropa' },
        { id: 3, name: 'Hogar' },
        { id: 4, name: 'Deportes' },
        { id: 5, name: 'Libros' },
        { id: 6, name: 'Belleza' },
        { id: 7, name: 'Automóvil' },
        { id: 8, name: 'Juguetes' }
    ];
    
    populateCategorySelect(exampleCategories);
    currentCategories = exampleCategories;
    
    // TODO: Implementar llamada AJAX real
    /*
    fetch('get_categories.php')
        .then(response => response.json())
        .then(data => {
            populateCategorySelect(data);
            currentCategories = data;
        })
        .catch(error => {
            console.error('Error cargando categorías:', error);
            showError('Error al cargar las categorías.');
        });
    */
}

function populateCategorySelect(categories) {
    const categorySelect = document.getElementById('product-category');
    
    // Limpia opciones existentes (excepto la primera)
    categorySelect.innerHTML = '<option value="">Seleccionar categoría...</option>';
    
    // Agrega las nuevas opciones
    categories.forEach(category => {
        const option = document.createElement('option');
        option.value = category.id;
        option.textContent = category.name;
        categorySelect.appendChild(option);
    });
}

function loadSubcategories(categoryId) {
    // Simulación de subcategorías basadas en la categoría
    const exampleSubcategories = {
        1: [ // Electrónicos
            { id: 11, name: 'Smartphones' },
            { id: 12, name: 'Laptops' },
            { id: 13, name: 'Tablets' },
            { id: 14, name: 'Auriculares' }
        ],
        2: [ // Ropa
            { id: 21, name: 'Camisetas' },
            { id: 22, name: 'Pantalones' },
            { id: 23, name: 'Vestidos' },
            { id: 24, name: 'Zapatos' }
        ],
        3: [ // Hogar
            { id: 31, name: 'Decoración' },
            { id: 32, name: 'Muebles' },
            { id: 33, name: 'Cocina' },
            { id: 34, name: 'Baño' }
        ],
        4: [ // Deportes
            { id: 41, name: 'Fitness' },
            { id: 42, name: 'Fútbol' },
            { id: 43, name: 'Basketball' },
            { id: 44, name: 'Running' }
        ],
        5: [ // Libros
            { id: 51, name: 'Ficción' },
            { id: 52, name: 'No Ficción' },
            { id: 53, name: 'Educativo' },
            { id: 54, name: 'Infantil' }
        ],
        6: [ // Belleza
            { id: 61, name: 'Cuidado Facial' },
            { id: 62, name: 'Maquillaje' },
            { id: 63, name: 'Cuidado Capilar' },
            { id: 64, name: 'Perfumes' }
        ],
        7: [ // Automóvil
            { id: 71, name: 'Accesorios' },
            { id: 72, name: 'Herramientas' },
            { id: 73, name: 'Llantas' },
            { id: 74, name: 'Audio' }
        ],
        8: [ // Juguetes
            { id: 81, name: 'Educativos' },
            { id: 82, name: 'Electrónicos' },
            { id: 83, name: 'Muñecas' },
            { id: 84, name: 'Construcción' }
        ]
    };
    
    const subcategories = exampleSubcategories[categoryId] || [];
    populateSubcategorySelect(subcategories);
    currentSubcategories = subcategories;
    
    // TODO: Implementar llamada AJAX real
    /*
    fetch(`get_subcategories.php?category_id=${categoryId}`)
        .then(response => response.json())
        .then(data => {
            populateSubcategorySelect(data);
            currentSubcategories = data;
        })
        .catch(error => {
            console.error('Error cargando subcategorías:', error);
            showError('Error al cargar las subcategorías.');
        });
    */
}

function populateSubcategorySelect(subcategories) {
    const subcategorySelect = document.getElementById('product-subcategory');
    
    // Limpia opciones existentes
    subcategorySelect.innerHTML = '<option value="">Seleccionar subcategoría...</option>';
    
    // Habilita el select
    subcategorySelect.disabled = false;
    
    // Agrega las nuevas opciones
    subcategories.forEach(subcategory => {
        const option = document.createElement('option');
        option.value = subcategory.id;
        option.textContent = subcategory.name;
        subcategorySelect.appendChild(option);
    });
}

function resetSubcategories() {
    const subcategorySelect = document.getElementById('product-subcategory');
    subcategorySelect.innerHTML = '<option value="">Primero selecciona una categoría</option>';
    subcategorySelect.disabled = true;
    currentSubcategories = [];
}

// ===== EVENTOS DEL FORMULARIO =====
function setupFormEvents() {
    // Evento para cambio de categoría
    const categorySelect = document.getElementById('product-category');
    categorySelect.addEventListener('change', function() {
        const categoryId = this.value;
        if (categoryId) {
            loadSubcategories(parseInt(categoryId));
        } else {
            resetSubcategories();
        }
    });
    
    // Eventos de validación en tiempo real
    const requiredFields = ['product-name', 'product-price-current'];
    requiredFields.forEach(fieldId => {
        const field = document.getElementById(fieldId);
        if (field) {
            field.addEventListener('blur', function() {
                validateField(this);
            });
            
            field.addEventListener('input', function() {
                clearFieldError(this);
            });
        }
    });
    
    // Validación de precios
    const priceFields = ['product-price-current', 'product-price-previous'];
    priceFields.forEach(fieldId => {
        const field = document.getElementById(fieldId);
        if (field) {
            field.addEventListener('input', function() {
                validatePriceField(this);
            });
        }
    });
}

// ===== FUNCIONES DE VALIDACIÓN =====
function validateField(field) {
    const value = field.value.trim();
    const fieldName = field.getAttribute('name');
    
    // Limpia errores previos
    clearFieldError(field);
    
    // Validaciones específicas
    switch (fieldName) {
        case 'product-name':
            if (!value) {
                showFieldError(field, 'El nombre del producto es obligatorio.');
                return false;
            }
            if (value.length < 3) {
                showFieldError(field, 'El nombre debe tener al menos 3 caracteres.');
                return false;
            }
            break;
            
        case 'product-price-current':
            if (!value) {
                showFieldError(field, 'El precio actual es obligatorio.');
                return false;
            }
            if (parseFloat(value) <= 0) {
                showFieldError(field, 'El precio debe ser mayor a 0.');
                return false;
            }
            break;
    }
    
    showFieldSuccess(field);
    return true;
}

function validatePriceField(field) {
    const value = parseFloat(field.value);
    const fieldName = field.getAttribute('name');
    
    if (field.value && (isNaN(value) || value < 0)) {
        showFieldError(field, 'Ingresa un precio válido.');
        return false;
    }
    
    // Validación especial para precio anterior
    if (fieldName === 'product-price-previous' && field.value) {
        const currentPrice = parseFloat(document.getElementById('product-price-current').value);
        if (!isNaN(currentPrice) && value <= currentPrice) {
            showFieldError(field, 'El precio anterior debe ser mayor al precio actual.');
            return false;
        }
    }
    
    clearFieldError(field);
    return true;
}

// ===== FUNCIÓN validateForm ELIMINADA =====

// ===== FUNCIONES DE MENSAJES =====
function showFieldError(field, message) {
    const formGroup = field.closest('.form-group') || field.closest('.form-section');
    formGroup.classList.add('error');
    formGroup.classList.remove('success');
    
    // Remueve mensaje de error existente
    const existingError = formGroup.querySelector('.error-message');
    if (existingError) {
        existingError.remove();
    }
    
    // Crea y agrega nuevo mensaje de error
    const errorElement = document.createElement('span');
    errorElement.className = 'error-message';
    errorElement.textContent = message;
    formGroup.appendChild(errorElement);
}

function showFieldSuccess(field) {
    const formGroup = field.closest('.form-group') || field.closest('.form-section');
    formGroup.classList.add('success');
    formGroup.classList.remove('error');
    
    // Remueve mensaje de error existente
    const existingError = formGroup.querySelector('.error-message');
    if (existingError) {
        existingError.remove();
    }
}

function clearFieldError(field) {
    const formGroup = field.closest('.form-group') || field.closest('.form-section');
    formGroup.classList.remove('error', 'success');
    
    // Remueve mensaje de error existente
    const existingError = formGroup.querySelector('.error-message');
    if (existingError) {
        existingError.remove();
    }
}

function clearValidationMessages() {
    // Limpia todas las clases de validación
    const formGroups = document.querySelectorAll('.form-group, .form-section');
    formGroups.forEach(group => {
        group.classList.remove('error', 'success');
    });
    
    // Remueve todos los mensajes de error
    const errorMessages = document.querySelectorAll('.error-message');
    errorMessages.forEach(message => message.remove());
}

function showError(message) {
    console.error('❌ Error:', message);
    // Muestra un mensaje de error con estilo personalizado
    showNotification(message, 'error');
}

function showSuccess(message) {
    console.log('✅ Éxito:', message);
    // Muestra un mensaje de éxito con estilo personalizado
    showNotification(message, 'success');
}

// ===== FUNCIÓN PARA MOSTRAR NOTIFICACIONES =====
function showNotification(message, type = 'info') {
    // Crea el elemento de notificación
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 20px;
        border-radius: 8px;
        color: white;
        font-weight: 500;
        z-index: 10000;
        max-width: 400px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        transform: translateX(100%);
        transition: transform 0.3s ease;
        ${type === 'success' ? 'background: #10b981;' : ''}
        ${type === 'error' ? 'background: #ef4444;' : ''}
        ${type === 'info' ? 'background: #3b82f6;' : ''}
    `;

    notification.textContent = message;

    // Agrega al DOM
    document.body.appendChild(notification);

    // Anima la entrada
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);

    // Remueve después de 4 segundos
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 4000);
}

// ===== FUNCIÓN saveNewProduct ELIMINADA =====

// ===== TODAS LAS FUNCIONES DE MODAL Y GUARDADO HAN SIDO ELIMINADAS =====

// ===== FUNCIONES DE GUARDADO ELIMINADAS =====

// ===== FUNCIÓN PARA ÉXITO EN EDICIÓN DE PRODUCTO =====
function saveProductEditSuccess(productData, originalSection) {
    try {
        console.log('Iniciando saveProductEditSuccess con datos:', productData, 'Sección original:', originalSection);

        // Si cambió la sección, mueve el producto
        if (originalSection && originalSection !== productData.tab) {
            console.log('Sección cambió, moviendo producto...');

            // Remueve el producto de la sección original
            console.log('Removiendo de sección original:', originalSection);
            removeProductFromSection(originalSection, productData);

            // Agrega el producto a la nueva sección
            console.log('Agregando a nueva sección:', productData.tab);
            addProductToTab(productData);

            // Muestra mensaje específico de cambio de sección
            showSuccess(`Producto editado y movido a la sección "${productData.tab}".`);

            // Cambia a la nueva pestaña
            switchToTab(productData.tab);
        } else {
            console.log('Sección no cambió, actualizando en su lugar...');

            // Solo actualiza el producto en su sección actual
            updateProductInSection(productData, originalSection || productData.tab);

            // Muestra mensaje de éxito
            showSuccess('Producto editado exitosamente.');
        }

        // Cierra el modal
        console.log('Cerrando modal...');
        closeAddProductModal();

        console.log('saveProductEditSuccess completado exitosamente');

    } catch (error) {
        console.error('Error en saveProductEditSuccess:', error);
        showError('Error al finalizar la edición: ' + error.message);
    }
}

// ===== FUNCIÓN PARA AGREGAR PRODUCTO A LA PESTAÑA =====
function addProductToTab(productData) {
    const tabSlider = document.getElementById(`slider-${productData.tab}`);
    
    if (!tabSlider) {
        console.error(`No se encontró el slider para la pestaña: ${productData.tab}`);
        return;
    }
    
    // Crea la nueva tarjeta de producto
    const newProductCard = createProductCard(productData);
    
    // Encuentra la tarjeta de "Agregar Producto" para insertarla después
    const addCard = tabSlider.querySelector('.add-producto-card');
    
    if (addCard) {
        // Inserta la nueva tarjeta después de la tarjeta de agregar
        addCard.insertAdjacentElement('afterend', newProductCard);
    } else {
        // Si no hay tarjeta de agregar, agrega al final
        tabSlider.appendChild(newProductCard);
    }
    
    // Actualiza los listeners de las tarjetas (para el pop-up responsive)
    if (typeof updateProductCardListeners === 'function') {
        updateProductCardListeners();
    }

    // Actualiza la navegación del slider
    updateSliderNavigation(productData.tab);

    // Actualiza los KPIs inmediatamente después de agregar
    console.log('📊 Actualizando KPIs después de agregar producto...');
    if (typeof updateProductKPIs === 'function') {
        updateProductKPIs();
        // Fuerza actualización adicional para asegurar
        if (typeof forceKPIUpdate === 'function') {
            forceKPIUpdate();
        }
    } else {
        console.warn('⚠️ Función updateProductKPIs no encontrada');
    }
}

// ===== FUNCIÓN PARA REMOVER PRODUCTO DE SECCIÓN =====
function removeProductFromSection(sectionName, productData) {
    const tabSlider = document.getElementById(`slider-${sectionName}`);
    if (!tabSlider) return;

    // Busca la tarjeta del producto por nombre (método simple para demo)
    const productCards = tabSlider.querySelectorAll('.producto-card:not(.add-producto-card)');
    productCards.forEach(card => {
        const nombreElement = card.querySelector('.producto-nombre');
        if (nombreElement && nombreElement.textContent === productData.name) {
            card.remove();
        }
    });

    // Actualiza la navegación del slider
    updateSliderNavigation(sectionName);

    // Actualiza los KPIs inmediatamente después de remover
    console.log('📊 Actualizando KPIs después de remover producto...');
    if (typeof updateProductKPIs === 'function') {
        updateProductKPIs();
    } else {
        console.warn('⚠️ Función updateProductKPIs no encontrada');
    }
}

// ===== FUNCIÓN PARA ACTUALIZAR PRODUCTO EN SECCIÓN =====
function updateProductInSection(productData, sectionName) {
    const tabSlider = document.getElementById(`slider-${sectionName}`);
    if (!tabSlider) return;

    // Busca la tarjeta del producto por nombre (método simple para demo)
    const productCards = tabSlider.querySelectorAll('.producto-card:not(.add-producto-card)');
    productCards.forEach(card => {
        const nombreElement = card.querySelector('.producto-nombre');
        if (nombreElement && nombreElement.textContent.includes(productData.name)) {
            // Actualiza el contenido de la tarjeta
            updateProductCardContent(card, productData);
        }
    });

    // Actualiza los KPIs después de actualizar producto
    console.log('📊 Actualizando KPIs después de actualizar producto...');
    if (typeof updateProductKPIs === 'function') {
        updateProductKPIs();
    } else {
        console.warn('⚠️ Función updateProductKPIs no encontrada');
    }
}

// ===== FUNCIÓN PARA ACTUALIZAR CONTENIDO DE TARJETA =====
function updateProductCardContent(card, productData) {
    console.log('🔄 Actualizando contenido de tarjeta con datos:', productData);

    // Actualiza la imagen
    const imageElement = card.querySelector('.producto-image');
    if (imageElement) {
        let imageUrl = '';

        if (selectedImageFile) {
            // Si hay un archivo nuevo seleccionado, úsalo
            imageUrl = URL.createObjectURL(selectedImageFile);
            console.log('Actualizando con imagen nueva:', imageUrl);
        } else if (productData.existingImageUrl) {
            // Si no hay archivo nuevo pero hay imagen existente, manténla
            imageUrl = productData.existingImageUrl;
            console.log('Manteniendo imagen existente:', imageUrl);
        }

        if (imageUrl) {
            imageElement.style.backgroundImage = `url('${imageUrl}')`;
            imageElement.setAttribute('data-has-image', 'true');
            imageElement.textContent = '';
            console.log('✅ Imagen actualizada en tarjeta:', imageUrl);
        } else {
            // Si no hay imagen, muestra placeholder
            imageElement.style.backgroundImage = 'none';
            imageElement.setAttribute('data-has-image', 'false');
            imageElement.textContent = 'Imagen del Producto';
            console.log('📷 Mostrando placeholder de imagen');
        }
    }

    // Actualiza la categoría
    const categoriaElement = card.querySelector('.producto-categoria');
    if (categoriaElement) {
        categoriaElement.textContent = `${productData.categoryName} / ${productData.subcategoryName}`;
    }

    // Actualiza el nombre
    const nombreElement = card.querySelector('.producto-nombre');
    if (nombreElement) {
        nombreElement.textContent = productData.name;
    }

    // Actualiza los precios
    const precioActualElement = card.querySelector('.precio-actual');
    if (precioActualElement) {
        precioActualElement.textContent = `$${productData.currentPrice.toFixed(2)}`;
    }

    const precioAnteriorElement = card.querySelector('.precio-anterior');
    if (productData.previousPrice) {
        if (precioAnteriorElement) {
            precioAnteriorElement.textContent = `$${productData.previousPrice.toFixed(2)}`;
        } else {
            // Agrega precio anterior si no existía
            const preciosContainer = card.querySelector('.producto-precios');
            const newPrecioAnterior = document.createElement('span');
            newPrecioAnterior.className = 'precio-anterior';
            newPrecioAnterior.textContent = `$${productData.previousPrice.toFixed(2)}`;
            preciosContainer.appendChild(newPrecioAnterior);
        }
    } else if (precioAnteriorElement) {
        // Remueve precio anterior si ya no existe
        precioAnteriorElement.remove();
    }

    // Actualiza la sección
    const seccionTextoElement = card.querySelector('.seccion-texto');
    if (seccionTextoElement) {
        seccionTextoElement.textContent = productData.tab;
    }
}

function createProductCard(productData) {
    const card = document.createElement('div');
    card.className = 'producto-card';

    // Determina qué imagen usar: nueva o existente
    let imageUrl = '';
    if (selectedImageFile) {
        // Si hay un archivo nuevo seleccionado, úsalo
        imageUrl = URL.createObjectURL(selectedImageFile);
        console.log('Usando imagen nueva:', imageUrl);
    } else if (productData.existingImageUrl) {
        // Si no hay archivo nuevo pero hay imagen existente, úsala
        imageUrl = productData.existingImageUrl;
        console.log('Usando imagen existente:', imageUrl);
    }

    card.innerHTML = `
        <div class="producto-image"
             style="${imageUrl ? `background-image: url('${imageUrl}');` : ''}"
             data-has-image="${!!imageUrl}"
             data-aspect-ratio="square">
            ${!imageUrl ? 'Imagen del Producto' : ''}
        </div>
        <div class="producto-categoria">${productData.categoryName} / ${productData.subcategoryName}</div>
        <div class="producto-nombre">${productData.name}</div>
        <div class="producto-precios">
            <span class="precio-actual">$${productData.currentPrice.toFixed(2)}</span>
            ${productData.previousPrice ? `<span class="precio-anterior">$${productData.previousPrice.toFixed(2)}</span>` : ''}
        </div>
        <div class="producto-seccion">
            <span class="seccion-texto">${productData.tab}</span>
            <div class="seccion-actions">
                <span class="action-icon edit-icon" title="Editar producto">✏️</span>
                <span class="action-icon delete-icon" title="Eliminar producto">🗑️</span>
            </div>
        </div>
    `;

    // Optimiza la imagen después de crear la tarjeta
    optimizeProductImage(card);

    return card;
}

// ===== FUNCIÓN PARA OPTIMIZAR IMÁGENES DE PRODUCTOS =====
function optimizeProductImage(card) {
    const imageElement = card.querySelector('.producto-image');
    if (!imageElement) return;

    const hasImage = imageElement.getAttribute('data-has-image') === 'true';

    if (hasImage) {
        // Para imágenes reales, asegura que se adapten correctamente
        const backgroundImage = imageElement.style.backgroundImage;
        if (backgroundImage && backgroundImage !== 'none') {
            console.log('🖼️ Optimizando imagen de producto...');

            // Crea una imagen temporal para verificar dimensiones
            const tempImg = new Image();
            tempImg.onload = function() {
                console.log(`📐 Dimensiones de imagen: ${this.width}x${this.height}`);

                // Calcula la proporción y agrega atributo para CSS
                const aspectRatio = this.width / this.height;
                let aspectRatioClass = 'square';

                if (aspectRatio > 1.3) {
                    aspectRatioClass = 'horizontal';
                } else if (aspectRatio < 0.8) {
                    aspectRatioClass = 'vertical';
                }

                imageElement.setAttribute('data-aspect-ratio', aspectRatioClass);

                // Aplica optimizaciones basadas en las dimensiones y contexto
                const isResponsive = window.innerWidth <= 768;

                if (isResponsive) {
                    // En responsive, optimiza según el tamaño de pantalla y proporción
                    if (window.innerWidth <= 360) {
                        // Móvil muy pequeño - siempre centra
                        imageElement.style.backgroundPosition = 'center center';
                        imageElement.style.backgroundSize = 'cover';
                    } else if (window.innerWidth <= 480) {
                        // Móvil pequeño - ajusta según proporción
                        if (aspectRatio > 1.5) {
                            // Imagen muy horizontal - centra horizontalmente, ajusta verticalmente
                            imageElement.style.backgroundPosition = 'center 30%';
                        } else {
                            imageElement.style.backgroundPosition = 'center center';
                        }
                        imageElement.style.backgroundSize = 'cover';
                    } else {
                        // Móvil normal - optimización estándar
                        if (aspectRatio > 1.3) {
                            // Imagen horizontal - centra con ligero ajuste
                            imageElement.style.backgroundPosition = 'center 40%';
                        } else if (aspectRatio < 0.8) {
                            // Imagen vertical - muestra parte superior
                            imageElement.style.backgroundPosition = 'center 20%';
                        } else {
                            // Imagen cuadrada o proporción normal
                            imageElement.style.backgroundPosition = 'center center';
                        }
                        imageElement.style.backgroundSize = 'cover';
                    }
                } else {
                    // En modo normal, ajusta según proporción
                    if (this.width > this.height) {
                        // Imagen horizontal - centra
                        imageElement.style.backgroundPosition = 'center center';
                    } else {
                        // Imagen vertical - muestra la parte superior
                        imageElement.style.backgroundPosition = 'center top';
                    }
                }

                console.log('✅ Imagen optimizada para', isResponsive ? 'responsive' : 'normal');
            };

            tempImg.onerror = function() {
                console.warn('⚠️ Error al cargar imagen, usando placeholder');
                imageElement.style.backgroundImage = 'none';
                imageElement.setAttribute('data-has-image', 'false');
                imageElement.textContent = 'Imagen del Producto';
            };

            // Extrae la URL de la imagen del CSS
            const urlMatch = backgroundImage.match(/url\(['"]?(.*?)['"]?\)/);
            if (urlMatch && urlMatch[1]) {
                tempImg.src = urlMatch[1];
            }
        }
    }
}

function updateSliderNavigation(tabName) {
    // Recalcula la navegación del slider después de agregar un producto
    const slider = document.getElementById(`slider-${tabName}`);
    const cards = slider.querySelectorAll('.producto-card');
    const visibleCards = 4;
    const maxPosition = Math.max(0, cards.length - visibleCards);
    
    // Actualiza la visibilidad de las flechas
    if (typeof updateArrowVisibility === 'function') {
        updateArrowVisibility(tabName, maxPosition);
    }
}

function switchToTab(tabName) {
    // Encuentra el botón de la pestaña
    const tabButton = document.querySelector(`[data-tab="${tabName}"]`);
    
    if (tabButton) {
        // Simula un click en la pestaña
        tabButton.click();
    }
}

// ===== FUNCIONES DE LOADING =====
function showLoading() {
    // Deshabilita el botón de guardar
    const saveBtn = document.querySelector('.btn-save');
    saveBtn.disabled = true;
    saveBtn.textContent = 'Guardando...';
    
    // Opcional: Mostrar overlay de loading
    const modalContainer = document.querySelector('.modal-container');
    const loadingOverlay = document.createElement('div');
    loadingOverlay.className = 'loading-overlay active';
    loadingOverlay.innerHTML = '<div class="loading-spinner"></div>';
    modalContainer.appendChild(loadingOverlay);
}

function hideLoading() {
    try {
        console.log('🔄 Ocultando loading...');

        // Habilita el botón de guardar
        const saveBtn = document.querySelector('.btn-save');
        if (saveBtn) {
            saveBtn.disabled = false;
            saveBtn.textContent = 'Guardar Producto';
            console.log('✅ Botón de guardar habilitado');
        } else {
            console.warn('⚠️ No se encontró el botón de guardar');
        }

        // Remueve overlay de loading
        const loadingOverlay = document.querySelector('.loading-overlay');
        if (loadingOverlay) {
            loadingOverlay.remove();
            console.log('✅ Overlay de loading removido');
        } else {
            console.warn('⚠️ No se encontró el overlay de loading');
        }

        console.log('✅ Loading ocultado exitosamente');
    } catch (error) {
        console.error('❌ Error al ocultar loading:', error);
    }
}

// Función de emergencia para cancelar loading manualmente
window.forceStopLoading = function() {
    console.log('🚨 FUNCIÓN DE EMERGENCIA: Forzando detención de loading');
    hideLoading();
    const modal = document.getElementById('add-product-modal');
    if (modal) {
        modal.classList.remove('active');
    }
    console.log('🚨 Loading forzadamente detenido');
};

// Función de debugging para verificar estado del modal
window.debugModal = function() {
    const modal = document.getElementById('add-product-modal');
    console.log('=== DEBUG MODAL ===');
    console.log('Modal existe:', !!modal);
    console.log('Modal activo:', modal?.classList.contains('active'));
    console.log('Modo:', modal?.getAttribute('data-mode'));
    console.log('Sección original:', modal?.getAttribute('data-original-section'));

    // Verifica campos
    const fields = [
        'product-name',
        'product-category',
        'product-subcategory',
        'product-price-current',
        'product-price-previous'
    ];

    fields.forEach(fieldId => {
        const field = document.getElementById(fieldId);
        console.log(`Campo ${fieldId}:`, field ? field.value : 'NO EXISTE');
    });

    // Verifica pestaña seleccionada
    const selectedTab = document.querySelector('input[name="product-tab"]:checked');
    console.log('Pestaña seleccionada:', selectedTab ? selectedTab.value : 'NINGUNA');

    console.log('Imagen nueva seleccionada:', !!selectedImageFile);
    console.log('URL imagen existente:', window.currentProductImageUrl);

    // Verifica el preview de imagen
    const imagePreview = document.getElementById('image-preview');
    console.log('Preview de imagen existe:', !!imagePreview);
    console.log('Preview tiene clase has-image:', imagePreview?.classList.contains('has-image'));

    console.log('=== FIN DEBUG ===');
};

// ===== EVENTOS DEL MODAL =====
function setupModalEvents() {
    // Cierra el modal al hacer click en el overlay
    const modalOverlay = document.getElementById('add-product-modal');
    modalOverlay.addEventListener('click', function(event) {
        if (event.target === this) {
            closeAddProductModal();
        }
    });
    
    // Cierra el modal con la tecla Escape
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape') {
            const modal = document.getElementById('add-product-modal');
            if (modal && modal.classList.contains('active')) {
                closeAddProductModal();
            }
        }
    });
    
    // Previene el envío del formulario con Enter
    const form = document.getElementById('add-product-form');
    form.addEventListener('submit', function(event) {
        event.preventDefault();
        saveNewProduct();
    });
}

// ===== ACTUALIZACIÓN DE LISTENERS DE TARJETAS AGREGAR =====
function updateAddProductCardListeners() {
    const addProductCards = document.querySelectorAll('.add-producto-card');
    
    addProductCards.forEach(card => {
        // Remueve listeners existentes
        card.removeEventListener('click', handleAddProductClick);
        // Agrega el nuevo listener
        card.addEventListener('click', handleAddProductClick);
    });
}

function handleAddProductClick(event) {
    event.preventDefault();
    event.stopPropagation();
    
    // Abre el modal de agregar producto
    openAddProductModal();
}

// ===== INICIALIZACIÓN =====
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 Inicializando admin_productos.js...');

    // Configura todos los eventos del modal
    setupImageUpload();
    setupFormEvents();
    setupModalEvents();

    // Actualiza los listeners de las tarjetas de agregar
    updateAddProductCardListeners();

    // Actualiza los listeners cuando cambian las pestañas
    const tabButtons = document.querySelectorAll('.tab-button');
    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            setTimeout(() => {
                updateAddProductCardListeners();
            }, 100);
        });
    });

    // Optimiza imágenes existentes al cargar la página
    optimizeAllProductImages();

    // Re-optimiza imágenes cuando cambia el tamaño de ventana
    let resizeTimeout;
    window.addEventListener('resize', function() {
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(() => {
            console.log('🔄 Reoptimizando imágenes por cambio de tamaño de ventana');
            optimizeAllProductImages();
            adjustModalForKeyboard(); // Ajusta modal cuando cambia el viewport
        }, 300);
    });

    // Configura detección de teclado virtual
    setupVirtualKeyboardDetection();

    // Pestañas de KPIs eliminadas
    console.log('🔧 Pestañas de KPIs eliminadas del sistema');

    // Configura el observer para detectar cambios en productos
    setupProductObserver();

    // Configura actualizaciones en tiempo real de KPIs
    setupRealTimeKPIUpdates();

    // Actualiza los KPIs inicialmente con delay para asegurar que el DOM esté listo
    setTimeout(() => {
        console.log('🚀 Ejecutando conteo inicial de KPIs...');
        updateProductKPIs();
    }, 500);

    // Backup: Actualiza nuevamente después de un delay mayor
    setTimeout(() => {
        console.log('🔄 Ejecutando conteo de respaldo de KPIs...');
        updateProductKPIs();
    }, 2000);

    // Función de prueba para verificar elementos KPI
    setTimeout(() => {
        console.log('🔍 Verificando elementos KPI...');
        const tabs = ['destacados', 'ofertas', 'novedades', 'mas-vistos', 'tendencias', 'liquidaciones'];
        tabs.forEach(tab => {
            const desktop = document.getElementById(`count-${tab}-desktop`);
            const responsive = document.getElementById(`count-${tab}-responsive`);
            console.log(`${tab}: Desktop=${!!desktop}, Responsive=${!!responsive}`);
        });
    }, 1000);
});

// ===== ACTUALIZACIÓN DE KPIs AL CARGAR COMPLETAMENTE LA VENTANA =====
window.addEventListener('load', function() {
    console.log('🌐 Ventana completamente cargada, actualizando KPIs...');
    setTimeout(() => {
        updateProductKPIs();
    }, 100);
});

// ===== FUNCIÓN PARA FORZAR ACTUALIZACIÓN DE KPIs =====
function forceUpdateKPIs() {
    console.log('🔧 Forzando actualización de KPIs...');
    updateProductKPIs();
}

// ===== FUNCIÓN GLOBAL PARA TESTING =====
window.testKPIUpdates = function() {
    console.log('🧪 === TESTING KPI UPDATES ===');

    // Cuenta productos actuales
    const tabs = ['destacados', 'ofertas', 'novedades', 'mas-vistos', 'tendencias', 'liquidaciones'];
    console.log('📊 Conteos actuales:');

    tabs.forEach(tab => {
        const tabElement = document.getElementById(tab);
        if (tabElement) {
            const count = tabElement.querySelectorAll('.producto-card:not(.add-producto-card)').length;
            const desktop = document.getElementById(`count-${tab}-desktop`);
            const responsive = document.getElementById(`count-${tab}-responsive`);

            console.log(`  ${tab}: Real=${count}, Desktop=${desktop?.textContent || 'N/A'}, Responsive=${responsive?.textContent || 'N/A'}`);
        }
    });

    console.log('\n🔄 Ejecutando actualización forzada...');
    forceKPIUpdate();

    setTimeout(() => {
        console.log('\n📊 Conteos después de actualización:');
        tabs.forEach(tab => {
            const tabElement = document.getElementById(tab);
            if (tabElement) {
                const count = tabElement.querySelectorAll('.producto-card:not(.add-producto-card)').length;
                const desktop = document.getElementById(`count-${tab}-desktop`);
                const responsive = document.getElementById(`count-${tab}-responsive`);

                console.log(`  ${tab}: Real=${count}, Desktop=${desktop?.textContent || 'N/A'}, Responsive=${responsive?.textContent || 'N/A'}`);
            }
        });
    }, 1000);
};

// ===== ACTUALIZACIÓN PERIÓDICA HASTA QUE FUNCIONE =====
let kpiUpdateAttempts = 0;
const maxKpiAttempts = 10;

function attemptKPIUpdate() {
    kpiUpdateAttempts++;
    console.log(`🔄 Intento ${kpiUpdateAttempts} de actualización de KPIs...`);

    const tabs = ['destacados', 'ofertas', 'novedades', 'mas-vistos', 'tendencias', 'liquidaciones'];
    let foundElements = 0;

    tabs.forEach(tab => {
        const desktop = document.getElementById(`count-${tab}-desktop`);
        const responsive = document.getElementById(`count-${tab}-responsive`);
        if (desktop) foundElements++;
        if (responsive) foundElements++;
    });

    if (foundElements >= 12 || kpiUpdateAttempts >= maxKpiAttempts) {
        console.log(`✅ Elementos encontrados: ${foundElements}/12. Ejecutando actualización final...`);
        updateProductKPIs();
    } else {
        console.log(`⏳ Solo ${foundElements}/12 elementos encontrados. Reintentando en 500ms...`);
        setTimeout(attemptKPIUpdate, 500);
    }
}

// Inicia los intentos de actualización
setTimeout(attemptKPIUpdate, 100);

// ===== FUNCIÓN GLOBAL PARA DEPURACIÓN =====
window.debugKPIs = function() {
    console.log('🔍 === DEBUG DE KPIs ===');
    const tabs = ['destacados', 'ofertas', 'novedades', 'mas-vistos', 'tendencias', 'liquidaciones'];

    tabs.forEach(tab => {
        console.log(`\n📋 Pestaña: ${tab}`);

        // Verifica pestaña
        const tabElement = document.getElementById(tab);
        console.log(`  Pestaña existe: ${!!tabElement}`);

        if (tabElement) {
            const products = tabElement.querySelectorAll('.producto-card:not(.add-producto-card)');
            console.log(`  Productos encontrados: ${products.length}`);
        }

        // Verifica KPIs
        const desktop = document.getElementById(`count-${tab}-desktop`);
        const responsive = document.getElementById(`count-${tab}-responsive`);

        console.log(`  KPI Desktop: ${!!desktop} (valor: ${desktop ? desktop.textContent : 'N/A'})`);
        console.log(`  KPI Responsive: ${!!responsive} (valor: ${responsive ? responsive.textContent : 'N/A'})`);
    });

    console.log('\n🔄 Ejecutando actualización manual...');
    updateProductKPIs();
};

// ===== FUNCIÓN PARA FORZAR ACTUALIZACIÓN INMEDIATA DE KPIs =====
function forceKPIUpdate() {
    console.log('⚡ Forzando actualización inmediata de KPIs...');

    // Ejecuta inmediatamente
    updateProductKPIs();

    // Ejecuta nuevamente después de un pequeño delay para asegurar
    setTimeout(() => {
        updateProductKPIs();
    }, 100);

    // Y una vez más después de un delay mayor
    setTimeout(() => {
        updateProductKPIs();
    }, 500);
}

// ===== FUNCIÓN PARA DETECTAR CAMBIOS EN TIEMPO REAL =====
function setupRealTimeKPIUpdates() {
    console.log('🔄 Configurando actualizaciones en tiempo real de KPIs...');

    // Observer más agresivo para detectar cualquier cambio
    const observer = new MutationObserver(function(mutations) {
        let shouldUpdate = false;

        mutations.forEach(function(mutation) {
            // Detecta si se agregaron o removieron nodos
            if (mutation.type === 'childList') {
                // Verifica si los cambios afectan tarjetas de productos
                const addedNodes = Array.from(mutation.addedNodes);
                const removedNodes = Array.from(mutation.removedNodes);

                const affectsProducts = [...addedNodes, ...removedNodes].some(node => {
                    return node.nodeType === Node.ELEMENT_NODE &&
                           (node.classList?.contains('producto-card') ||
                            node.querySelector?.('.producto-card'));
                });

                if (affectsProducts) {
                    console.log('🔄 Cambio detectado en tarjetas de productos');
                    shouldUpdate = true;
                }
            }
        });

        if (shouldUpdate) {
            // Debounce para evitar actualizaciones excesivas
            clearTimeout(window.kpiUpdateTimeout);
            window.kpiUpdateTimeout = setTimeout(() => {
                console.log('⚡ Actualizando KPIs por cambio detectado...');
                updateProductKPIs();
            }, 50);
        }
    });

    // Observa todos los contenedores de pestañas
    const tabs = ['destacados', 'ofertas', 'novedades', 'mas-vistos', 'tendencias', 'liquidaciones'];
    tabs.forEach(tab => {
        const tabElement = document.getElementById(tab);
        if (tabElement) {
            observer.observe(tabElement, {
                childList: true,
                subtree: true
            });
            console.log(`👁️ Observando cambios en pestaña: ${tab}`);
        }
    });
}

// ===== FUNCIÓN setupKPITabs ELIMINADA =====
// Las pestañas de KPIs responsive han sido eliminadas del sistema

// ===== FUNCIÓN PARA ACTUALIZAR KPIs DE PRODUCTOS =====
function updateProductKPIs() {
    console.log('📊 Actualizando KPIs de productos...');

    const tabs = ['destacados', 'ofertas', 'novedades', 'mas-vistos', 'tendencias', 'liquidaciones'];
    let totalProducts = 0;
    let elementsFound = 0;
    let elementsUpdated = 0;

    tabs.forEach(tab => {
        // Cuenta productos en cada pestaña (excluyendo la tarjeta de agregar)
        const tabContent = document.getElementById(tab);
        if (tabContent) {
            const productCards = tabContent.querySelectorAll('.producto-card:not(.add-producto-card)');
            const count = productCards.length;
            console.log(`🔍 Pestaña ${tab}: encontradas ${productCards.length} tarjetas de productos`);

            // Actualiza KPI responsive (pestañas) - si existe
            const kpiResponsive = document.getElementById(`count-${tab}`);
            if (kpiResponsive) {
                kpiResponsive.textContent = count;
                elementsUpdated++;
            }

            // Actualiza KPI responsive (6 filas)
            const kpiResponsiveRows = document.getElementById(`count-${tab}-responsive`);
            if (kpiResponsiveRows) {
                kpiResponsiveRows.textContent = count;
                elementsFound++;
                elementsUpdated++;
                console.log(`✅ KPI responsive actualizado: ${tab} = ${count}`);
            } else {
                console.warn(`⚠️ KPI responsive no encontrado: count-${tab}-responsive`);
            }

            // Actualiza KPI desktop
            const kpiDesktop = document.getElementById(`count-${tab}-desktop`);
            if (kpiDesktop) {
                kpiDesktop.textContent = count;
                elementsFound++;
                elementsUpdated++;
                console.log(`✅ KPI desktop actualizado: ${tab} = ${count}`);
            } else {
                console.warn(`⚠️ KPI desktop no encontrado: count-${tab}-desktop`);
            }

            totalProducts += count;
            console.log(`📦 ${tab}: ${count} productos`);
        } else {
            console.error(`❌ Pestaña no encontrada: ${tab}`);
        }
    });



    console.log(`📊 Total de productos: ${totalProducts}`);
    console.log(`🎯 Elementos KPI encontrados: ${elementsFound}`);
    console.log(`✅ Elementos KPI actualizados: ${elementsUpdated}`);

    // Muestra resumen de conteos por pestaña
    console.log('📋 Resumen de conteos por pestaña:');
    tabs.forEach(tab => {
        const tabContent = document.getElementById(tab);
        if (tabContent) {
            const count = tabContent.querySelectorAll('.producto-card:not(.add-producto-card)').length;
            console.log(`  • ${tab}: ${count} productos`);
        }
    });

    // Verifica si todos los elementos fueron encontrados
    if (elementsFound < 12) { // 6 pestañas × 2 tipos (desktop + responsive) = 12
        console.warn(`⚠️ No todos los elementos KPI fueron encontrados. Esperados: 12, Encontrados: ${elementsFound}`);
        console.log('🔄 Reintentando en 1 segundo...');
        setTimeout(() => {
            updateProductKPIs();
        }, 1000);
    } else {
        console.log('🎉 Todos los KPIs actualizados correctamente');
    }
}

// ===== FUNCIÓN PARA ACTUALIZAR BARRAS DE PROGRESO =====
function updateProgressBars(currentCount) {
    const maxCount = 100;
    const percentage = Math.min((currentCount / maxCount) * 100, 100);

    // Actualiza barra responsive
    const progressFillResponsive = document.getElementById('total-progress-fill');
    const currentResponsive = document.getElementById('total-current');
    const maxResponsive = document.getElementById('total-max');

    if (progressFillResponsive) {
        progressFillResponsive.style.width = `${percentage}%`;
    }
    if (currentResponsive) {
        currentResponsive.textContent = currentCount;
    }
    if (maxResponsive) {
        maxResponsive.textContent = maxCount;
    }

    // Actualiza barra desktop
    const progressFillDesktop = document.getElementById('total-progress-fill-desktop');
    const currentDesktop = document.getElementById('total-current-desktop');
    const maxDesktop = document.getElementById('total-max-desktop');

    if (progressFillDesktop) {
        progressFillDesktop.style.width = `${percentage}%`;
    }
    if (currentDesktop) {
        currentDesktop.textContent = currentCount;
    }
    if (maxDesktop) {
        maxDesktop.textContent = maxCount;
    }

    console.log(`📈 Progreso actualizado: ${currentCount}/${maxCount} (${percentage.toFixed(1)}%)`);

    // Actualiza gráfico de torta desktop
    if (typeof window.updatePieChart === 'function') {
        window.updatePieChart(currentCount, maxCount);
        console.log('🥧 Gráfico de torta actualizado');
    }
}

// ===== FUNCIÓN PARA CONFIGURAR OBSERVER DE PRODUCTOS =====
function setupProductObserver() {
    console.log('👁️ Configurando observer de productos...');

    const tabs = ['destacados', 'ofertas', 'novedades', 'mas-vistos', 'tendencias', 'liquidaciones'];

    tabs.forEach(tab => {
        const tabContent = document.getElementById(tab);
        if (tabContent) {
            const slider = tabContent.querySelector('.productos-slider');
            if (slider) {
                // Crea un observer para detectar cambios en el DOM
                const observer = new MutationObserver(function(mutations) {
                    let shouldUpdate = false;

                    mutations.forEach(function(mutation) {
                        // Detecta si se agregaron o eliminaron tarjetas de productos
                        if (mutation.type === 'childList') {
                            mutation.addedNodes.forEach(function(node) {
                                if (node.nodeType === 1 && node.classList && node.classList.contains('producto-card')) {
                                    shouldUpdate = true;
                                }
                            });

                            mutation.removedNodes.forEach(function(node) {
                                if (node.nodeType === 1 && node.classList && node.classList.contains('producto-card')) {
                                    shouldUpdate = true;
                                }
                            });
                        }
                    });

                    if (shouldUpdate) {
                        console.log(`🔄 Cambio detectado en pestaña: ${tab}`);
                        // Espera un poco para que el DOM se actualice completamente
                        setTimeout(() => {
                            updateProductKPIs();
                        }, 100);
                    }
                });

                // Configura el observer
                observer.observe(slider, {
                    childList: true,
                    subtree: true
                });

                console.log(`✅ Observer configurado para: ${tab}`);
            }
        }
    });

    console.log('✅ Todos los observers configurados correctamente');
}

// ===== FUNCIÓN PARA INICIALIZAR GRÁFICOS DE KPIs =====
function initKPICharts() {
    console.log('📊 Inicializando gráficos de KPIs...');

    // Datos diarios para la semana actual (Lunes a Domingo)
    const chartData = {
        'chart-visitas-totales': {
            label: 'Visitas Totales',
            dailyData: [1650, 1820, 1950, 2100, 2200, 1890, 1237], // Datos diarios
            weeklyTotal: 12847,
            color: '#6b46c1'
        },
        'chart-visitantes-unicos': {
            label: 'Visitantes Únicos',
            dailyData: [1200, 1350, 1450, 1580, 1650, 1420, 984], // Datos diarios
            weeklyTotal: 9234,
            color: '#8b5cf6'
        },
        'chart-productos-vistos': {
            label: 'Productos Vistos',
            dailyData: [580, 650, 720, 780, 850, 690, 487], // Datos diarios
            weeklyTotal: 4567,
            color: '#a855f7'
        },
        'chart-productos-compartidos': {
            label: 'Productos Compartidos',
            dailyData: [95, 120, 140, 160, 180, 135, 62], // Datos diarios
            weeklyTotal: 892,
            color: '#c084fc'
        }
    };

    const dayLabels = ['Lunes', 'Martes', 'Miércoles', 'Jueves', 'Viernes', 'Sábado', 'Domingo'];

    // Crear gráfico para cada KPI
    Object.keys(chartData).forEach(chartId => {
        const canvas = document.getElementById(chartId);
        if (canvas) {
            createLineChart(canvas, chartData[chartId], dayLabels);
            console.log(`✅ Gráfico creado: ${chartId}`);
        } else {
            console.warn(`⚠️ Canvas no encontrado: ${chartId}`);
        }
    });

    console.log('✅ Gráficos de KPIs inicializados correctamente');
}

// ===== FUNCIÓN PARA CREAR GRÁFICO LINEAL =====
function createLineChart(canvas, data, labels) {
    const ctx = canvas.getContext('2d');
    const width = canvas.width;
    const height = canvas.height;

    // Configuración del gráfico
    const padding = 20;
    const chartWidth = width - (padding * 2);
    const chartHeight = height - (padding * 2);

    // Usar datos diarios
    const dailyData = data.dailyData;

    // Calcular valores mínimo y máximo
    const minValue = Math.min(...dailyData);
    const maxValue = Math.max(...dailyData);
    const valueRange = maxValue - minValue;

    // Crear tooltip element
    let tooltip = document.getElementById('chart-tooltip');
    if (!tooltip) {
        tooltip = document.createElement('div');
        tooltip.id = 'chart-tooltip';
        tooltip.style.cssText = `
            position: absolute;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            pointer-events: none;
            z-index: 1000;
            display: none;
            white-space: nowrap;
        `;
        document.body.appendChild(tooltip);
    }

    // Función para convertir datos a coordenadas del canvas
    function getX(index) {
        return padding + (index * chartWidth) / (dailyData.length - 1);
    }

    function getY(value) {
        const normalizedValue = (value - minValue) / valueRange;
        return height - padding - (normalizedValue * chartHeight);
    }

    // Limpiar canvas
    ctx.clearRect(0, 0, width, height);

    // Dibujar línea del gráfico
    ctx.beginPath();
    ctx.strokeStyle = data.color;
    ctx.lineWidth = 2;

    dailyData.forEach((value, index) => {
        const x = getX(index);
        const y = getY(value);

        if (index === 0) {
            ctx.moveTo(x, y);
        } else {
            ctx.lineTo(x, y);
        }
    });

    ctx.stroke();

    // Dibujar puntos
    dailyData.forEach((value, index) => {
        const x = getX(index);
        const y = getY(value);

        // Punto principal
        ctx.beginPath();
        ctx.arc(x, y, 4, 0, 2 * Math.PI);
        ctx.fillStyle = data.color;
        ctx.fill();

        // Borde blanco del punto
        ctx.beginPath();
        ctx.arc(x, y, 4, 0, 2 * Math.PI);
        ctx.strokeStyle = '#ffffff';
        ctx.lineWidth = 2;
        ctx.stroke();
    });

    // Agregar interactividad con tooltips
    canvas.addEventListener('mousemove', function(e) {
        const rect = canvas.getBoundingClientRect();
        const mouseX = e.clientX - rect.left;
        const mouseY = e.clientY - rect.top;

        // Encontrar el punto más cercano
        let closestIndex = -1;
        let closestDistance = Infinity;

        dailyData.forEach((value, index) => {
            const x = getX(index);
            const y = getY(value);
            const distance = Math.sqrt(Math.pow(mouseX - x, 2) + Math.pow(mouseY - y, 2));

            if (distance < 15 && distance < closestDistance) {
                closestDistance = distance;
                closestIndex = index;
            }
        });

        // Mostrar tooltip si está cerca de un punto
        if (closestIndex !== -1) {
            const value = dailyData[closestIndex];
            const day = labels[closestIndex];
            const formattedValue = value.toLocaleString();

            // Mostrar tooltip
            tooltip.innerHTML = `<strong>${day}</strong><br>${formattedValue}`;
            tooltip.style.display = 'block';
            tooltip.style.left = (e.pageX + 10) + 'px';
            tooltip.style.top = (e.pageY - 10) + 'px';

            canvas.style.cursor = 'pointer';
        } else {
            // Ocultar tooltip
            tooltip.style.display = 'none';
            canvas.style.cursor = 'default';
        }
    });

    // Ocultar tooltip al salir del canvas
    canvas.addEventListener('mouseleave', function() {
        tooltip.style.display = 'none';
        canvas.style.cursor = 'default';
    });
}

// ===== FUNCIÓN PARA DETECTAR TECLADO VIRTUAL =====
function setupVirtualKeyboardDetection() {
    if (window.innerWidth <= 768) { // Solo en móviles
        let initialViewportHeight = window.innerHeight;

        window.addEventListener('resize', function() {
            const currentHeight = window.innerHeight;
            const heightDifference = initialViewportHeight - currentHeight;

            // Si la altura se redujo significativamente, probablemente apareció el teclado
            if (heightDifference > 150) {
                console.log('📱 Teclado virtual detectado');
                document.body.classList.add('keyboard-visible');
            } else {
                console.log('📱 Teclado virtual oculto');
                document.body.classList.remove('keyboard-visible');
            }

            adjustModalForKeyboard();
        });
    }
}

// ===== FUNCIÓN PARA AJUSTAR MODAL CUANDO APARECE EL TECLADO =====
function adjustModalForKeyboard() {
    const modal = document.querySelector('.modal-overlay:not([style*="display: none"])');
    if (!modal) return;

    const modalContainer = modal.querySelector('.modal-container');
    const modalContent = modal.querySelector('.modal-content');
    const modalFooter = modal.querySelector('.modal-footer');

    if (window.innerWidth <= 768 && window.innerHeight < 500) {
        // Teclado probablemente visible
        console.log('🔧 Ajustando modal para teclado virtual');

        if (modalContainer) {
            modalContainer.style.maxHeight = `${window.innerHeight - 20}px`;
        }

        if (modalContent) {
            modalContent.style.maxHeight = `${window.innerHeight - 140}px`;
        }

        // Asegura que el footer sea visible
        if (modalFooter) {
            modalFooter.scrollIntoView({ behavior: 'smooth', block: 'end' });
        }
    } else {
        // Teclado oculto, restaura estilos normales
        if (modalContainer) {
            modalContainer.style.maxHeight = '';
        }

        if (modalContent) {
            modalContent.style.maxHeight = '';
        }
    }
}

// ===== FUNCIÓN PARA OPTIMIZAR TODAS LAS IMÁGENES =====
function optimizeAllProductImages() {
    const allProductCards = document.querySelectorAll('.producto-card:not(.add-producto-card)');
    console.log(`🖼️ Optimizando ${allProductCards.length} imágenes de productos...`);

    allProductCards.forEach(card => {
        optimizeProductImage(card);
    });

    console.log('✅ Optimización de todas las imágenes completada');
}

// ===== FUNCIONES AUXILIARES =====
function formatPrice(price) {
    return new Intl.NumberFormat('es-ES', {
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: 2
    }).format(price);
}

function generateProductId() {
    // Genera un ID único para el producto (temporal)
    return 'prod_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
}

function validateImageFile(file) {
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    const maxSize = 5 * 1024 * 1024; // 5MB
    
    if (!allowedTypes.includes(file.type)) {
        return {
            valid: false,
            message: 'Tipo de archivo no permitido. Usa JPG, PNG, GIF o WebP.'
        };
    }
    
    if (file.size > maxSize) {
        return {
            valid: false,
            message: 'El archivo es muy grande. Máximo 5MB.'
        };
    }
    
    return { valid: true };
}

// ===== FUNCIONES PARA INTEGRACIÓN FUTURA CON PHP/AJAX =====
function getCategoriesFromServer() {
    // Función placeholder para obtener categorías del servidor
    return fetch('api/get_categories.php')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                return data.categories;
            } else {
                throw new Error(data.message || 'Error al obtener categorías');
            }
        });
}

function getSubcategoriesFromServer(categoryId) {
    // Función placeholder para obtener subcategorías del servidor
    return fetch(`api/get_subcategories.php?category_id=${categoryId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                return data.subcategories;
            } else {
                throw new Error(data.message || 'Error al obtener subcategorías');
            }
        });
}

function saveProductToServer(formData) {
    // Función placeholder para guardar producto en el servidor
    return fetch('api/save_product.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            return data;
        } else {
            throw new Error(data.message || 'Error al guardar producto');
        }
    });
}

// ===== FUNCIONES DE DEPURACIÓN =====
function debugFormData() {
    // Función para depurar los datos del formulario
    const formData = collectFormData();
    console.log('Datos del formulario:', formData);
    console.log('Archivo de imagen:', selectedImageFile);
    return formData;
}

function debugModalState() {
    // Función para depurar el estado del modal
    const modal = document.getElementById('add-product-modal');
    console.log('Modal activo:', modal.classList.contains('active'));
    console.log('Categorías cargadas:', currentCategories.length);
    console.log('Subcategorías cargadas:', currentSubcategories.length);
    console.log('Imagen seleccionada:', !!selectedImageFile);
}

