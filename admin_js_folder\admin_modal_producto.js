// ===== VARIABLES GLOBALES DEL MODAL DE PRODUCTO =====
let modalProductoOverlay = null;
let modalProductoImagenInput = null;
let modalProductoImagenUpload = null;
let modalProductoImagenSeleccionada = null;
let modalProductoModoEdicion = false;
let modalProductoTarjetaEditando = null;

// ===== INICIALIZACIÓN DEL MODAL DE PRODUCTO =====
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 Inicializando modal de producto...');

    // Obtener referencias a elementos del modal
    modalProductoOverlay = document.getElementById('modal-producto-overlay');
    modalProductoImagenInput = document.getElementById('modal-imagen-input');
    modalProductoImagenUpload = document.getElementById('modal-imagen-upload');

    // Configurar event listeners
    configurarEventListenersModalProducto();

    // Reconectar event listeners de iconos existentes
    reconectarEventListenersIconos();

    console.log('✅ Modal de producto inicializado correctamente');
});

// ===== CONFIGURAR EVENT LISTENERS =====
function configurarEventListenersModalProducto() {
    // Event listeners para abrir el modal desde las tarjetas de agregar
    const tarjetasAgregar = document.querySelectorAll('.add-producto-card');
    tarjetasAgregar.forEach(tarjeta => {
        tarjeta.addEventListener('click', () => abrirModalProducto());
    });

    // Los event listeners para editar productos existentes se configuran en reconectarEventListenersIconos()
    
    // Event listener para cerrar el modal con el botón X
    const btnCerrar = document.getElementById('modal-producto-close');
    if (btnCerrar) {
        btnCerrar.addEventListener('click', cerrarModalProducto);
    }
    
    // Event listener para cerrar el modal clickeando fuera
    if (modalProductoOverlay) {
        modalProductoOverlay.addEventListener('click', function(e) {
            if (e.target === modalProductoOverlay) {
                cerrarModalProducto();
            }
        });
    }
    
    // Event listener para el botón cancelar
    const btnCancelar = document.getElementById('modal-btn-cancelar');
    if (btnCancelar) {
        btnCancelar.addEventListener('click', cerrarModalProducto);
    }
    
    // Event listener para el botón guardar
    const btnGuardar = document.getElementById('modal-btn-guardar');
    if (btnGuardar) {
        btnGuardar.addEventListener('click', guardarProducto);
    }
    
    // Event listener para la carga de imagen
    if (modalProductoImagenInput) {
        modalProductoImagenInput.addEventListener('change', manejarCargaImagen);
    }
    
    // Event listener para hacer click en el área de carga de imagen
    if (modalProductoImagenUpload) {
        modalProductoImagenUpload.addEventListener('click', function() {
            modalProductoImagenInput.click();
        });
    }
    
    // Event listener para cerrar con tecla Escape
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && modalProductoOverlay && modalProductoOverlay.classList.contains('active')) {
            cerrarModalProducto();
        }
    });
}

// ===== ABRIR MODAL DE PRODUCTO =====
function abrirModalProducto(tarjetaParaEditar = null) {
    console.log('📝 Abriendo modal de producto...');

    if (modalProductoOverlay) {
        // Determinar si es modo edición
        modalProductoModoEdicion = tarjetaParaEditar !== null;
        modalProductoTarjetaEditando = tarjetaParaEditar;

        // Si es modo edición, cargar datos de la tarjeta
        if (modalProductoModoEdicion) {
            // Limpiar formulario primero (sin resetear modo edición)
            limpiarFormularioProducto(false);
            // Luego cargar datos de la tarjeta
            setTimeout(() => {
                cargarDatosTarjeta(tarjetaParaEditar);
            }, 50);
            actualizarTituloModal('Editar Producto');
            actualizarTextoBotonGuardar('Actualizar Producto');
        } else {
            // Limpiar formulario para modo creación
            limpiarFormularioProducto(true);
            actualizarTituloModal('Agregar Nuevo Producto');
            actualizarTextoBotonGuardar('Guardar Producto');
        }

        // Mostrar modal
        modalProductoOverlay.classList.add('active');
        document.body.style.overflow = 'hidden'; // Prevenir scroll del body

        // Focus en el primer campo
        const primerCampo = document.getElementById('modal-nombre-input');
        if (primerCampo) {
            setTimeout(() => primerCampo.focus(), 100);
        }

        console.log(`✅ Modal de producto abierto en modo: ${modalProductoModoEdicion ? 'edición' : 'creación'}`);
    }
}

// ===== CERRAR MODAL DE PRODUCTO =====
function cerrarModalProducto() {
    console.log('❌ Cerrando modal de producto...');

    if (modalProductoOverlay) {
        modalProductoOverlay.classList.remove('active');
        document.body.style.overflow = ''; // Restaurar scroll del body

        // Resetear variables de edición
        modalProductoModoEdicion = false;
        modalProductoTarjetaEditando = null;

        // Limpiar formulario después de cerrar
        setTimeout(() => limpiarFormularioProducto(), 300);

        console.log('✅ Modal de producto cerrado');
    }
}

// ===== ACTUALIZAR TÍTULO DEL MODAL =====
function actualizarTituloModal(titulo) {
    const tituloElement = document.querySelector('.modal-producto-title');
    if (tituloElement) {
        tituloElement.textContent = titulo;
    }
}

// ===== ACTUALIZAR TEXTO DEL BOTÓN GUARDAR =====
function actualizarTextoBotonGuardar(texto) {
    const botonGuardar = document.getElementById('modal-btn-guardar');
    if (botonGuardar) {
        botonGuardar.textContent = texto;
    }
}

// ===== CARGAR DATOS DE TARJETA PARA EDICIÓN =====
function cargarDatosTarjeta(tarjeta) {
    console.log('📋 Cargando datos de tarjeta para edición...');

    // Extraer datos de la tarjeta
    const nombre = tarjeta.querySelector('.producto-nombre')?.textContent.trim() || '';
    const categoriaCompleta = tarjeta.querySelector('.producto-categoria')?.textContent.trim() || '';

    // Extraer precio actual (remover $ y espacios)
    const precioActualElement = tarjeta.querySelector('.precio-actual');
    let precioActual = '';
    if (precioActualElement) {
        precioActual = precioActualElement.textContent.replace('$', '').replace(',', '').trim();
    }

    // Extraer precio anterior si existe
    const precioAnteriorElement = tarjeta.querySelector('.precio-anterior');
    let precioAnterior = '';
    if (precioAnteriorElement) {
        precioAnterior = precioAnteriorElement.textContent.replace('$', '').replace(',', '').trim();
    }

    const pestana = tarjeta.querySelector('.seccion-texto')?.textContent.trim() || '';

    console.log('📋 Datos extraídos:', { nombre, categoriaCompleta, precioActual, precioAnterior, pestana });

    // Separar categoría y subcategoría
    const [categoria, subcategoria] = categoriaCompleta.includes(' / ') ?
        categoriaCompleta.split(' / ') : [categoriaCompleta, ''];

    console.log('📋 Categorías separadas:', { categoria, subcategoria });

    // Llenar campos del formulario
    const nombreInput = document.getElementById('modal-nombre-input');
    const categoriaInput = document.getElementById('modal-categoria-input');
    const subcategoriaInput = document.getElementById('modal-subcategoria-input');
    const precioActualInput = document.getElementById('modal-precio-actual-input');
    const precioAnteriorInput = document.getElementById('modal-precio-anterior-input');

    if (nombreInput) {
        nombreInput.value = nombre;
        console.log('📋 Nombre cargado:', nombre);
    }
    if (categoriaInput) {
        categoriaInput.value = categoria;
        console.log('📋 Categoría cargada:', categoria);
    }
    if (subcategoriaInput) {
        subcategoriaInput.value = subcategoria || '';
        console.log('📋 Subcategoría cargada:', subcategoria);
    }
    if (precioActualInput) {
        precioActualInput.value = precioActual;
        console.log('📋 Precio actual cargado:', precioActual);
    }
    if (precioAnteriorInput) {
        precioAnteriorInput.value = precioAnterior;
        console.log('📋 Precio anterior cargado:', precioAnterior);
    }

    // Seleccionar pestaña
    const radiosPestana = document.querySelectorAll('input[name="modal-pestana"]');
    console.log('📋 Seleccionando pestaña:', pestana);
    radiosPestana.forEach(radio => {
        radio.checked = radio.value === pestana;
        if (radio.checked) {
            console.log('📋 Pestaña seleccionada:', radio.value);
        }
    });

    // Cargar imagen si existe
    const imagenElement = tarjeta.querySelector('.producto-image');
    if (imagenElement) {
        // Verificar si tiene imagen de fondo
        const backgroundImage = window.getComputedStyle(imagenElement).backgroundImage;
        console.log('📋 Background image:', backgroundImage);

        if (backgroundImage && backgroundImage !== 'none' && backgroundImage !== 'initial') {
            // Extraer URL de la imagen
            let imageUrl = backgroundImage;

            // Limpiar la URL (remover url(), comillas, etc.)
            if (imageUrl.startsWith('url(')) {
                imageUrl = imageUrl.slice(4, -1);
            }
            imageUrl = imageUrl.replace(/['"]/g, '');

            if (imageUrl && imageUrl !== 'none' && imageUrl !== '') {
                modalProductoImagenSeleccionada = imageUrl;
                if (modalProductoImagenUpload) {
                    modalProductoImagenUpload.classList.add('has-image');
                    modalProductoImagenUpload.style.backgroundImage = `url(${imageUrl})`;
                    modalProductoImagenUpload.style.backgroundSize = 'cover';
                    modalProductoImagenUpload.style.backgroundPosition = 'center';
                    modalProductoImagenUpload.innerHTML = '<div class="modal-imagen-placeholder">Imagen cargada ✓</div>';
                }
                console.log('📋 Imagen cargada:', imageUrl);
            }
        } else {
            // Verificar si tiene imagen como src (img tag)
            const imgTag = imagenElement.querySelector('img');
            if (imgTag && imgTag.src) {
                modalProductoImagenSeleccionada = imgTag.src;
                if (modalProductoImagenUpload) {
                    modalProductoImagenUpload.classList.add('has-image');
                    modalProductoImagenUpload.style.backgroundImage = `url(${imgTag.src})`;
                    modalProductoImagenUpload.style.backgroundSize = 'cover';
                    modalProductoImagenUpload.style.backgroundPosition = 'center';
                    modalProductoImagenUpload.innerHTML = '<div class="modal-imagen-placeholder">Imagen cargada ✓</div>';
                }
                console.log('📋 Imagen cargada desde img tag:', imgTag.src);
            } else {
                console.log('📋 No se encontró imagen en la tarjeta');
            }
        }
    }

    // Verificar que los datos se cargaron correctamente
    setTimeout(() => {
        verificarDatosCargados();
    }, 100);

    console.log('✅ Datos de tarjeta cargados correctamente');
}

// ===== VERIFICAR QUE LOS DATOS SE CARGARON CORRECTAMENTE =====
function verificarDatosCargados() {
    console.log('🔍 Verificando datos cargados en el formulario...');

    const nombreValue = document.getElementById('modal-nombre-input')?.value || '';
    const categoriaValue = document.getElementById('modal-categoria-input')?.value || '';
    const subcategoriaValue = document.getElementById('modal-subcategoria-input')?.value || '';
    const precioActualValue = document.getElementById('modal-precio-actual-input')?.value || '';
    const precioAnteriorValue = document.getElementById('modal-precio-anterior-input')?.value || '';

    const pestanaSeleccionada = document.querySelector('input[name="modal-pestana"]:checked')?.value || '';

    console.log('🔍 Valores en formulario:', {
        nombre: nombreValue,
        categoria: categoriaValue,
        subcategoria: subcategoriaValue,
        precioActual: precioActualValue,
        precioAnterior: precioAnteriorValue,
        pestana: pestanaSeleccionada,
        imagen: modalProductoImagenSeleccionada ? 'Sí' : 'No'
    });

    // Verificar si hay campos vacíos que deberían tener datos
    if (!nombreValue) console.warn('⚠️ Nombre no se cargó');
    if (!categoriaValue) console.warn('⚠️ Categoría no se cargó');
    if (!precioActualValue) console.warn('⚠️ Precio actual no se cargó');
    if (!pestanaSeleccionada) console.warn('⚠️ Pestaña no se seleccionó');

    console.log('✅ Verificación de datos completada');
}

// ===== LIMPIAR FORMULARIO =====
function limpiarFormularioProducto(resetearModoEdicion = true) {
    console.log('🧹 Limpiando formulario de producto...');

    // Limpiar campos de texto
    const campos = [
        'modal-nombre-input',
        'modal-categoria-input',
        'modal-subcategoria-input',
        'modal-precio-actual-input',
        'modal-precio-anterior-input'
    ];

    campos.forEach(campoId => {
        const campo = document.getElementById(campoId);
        if (campo) {
            campo.value = '';
        }
    });

    // Limpiar selección de pestaña
    const radios = document.querySelectorAll('input[name="modal-pestana"]');
    radios.forEach(radio => {
        radio.checked = false;
    });

    // Limpiar imagen solo si no estamos en modo edición o si se especifica resetear
    if (resetearModoEdicion || !modalProductoModoEdicion) {
        if (modalProductoImagenInput) {
            modalProductoImagenInput.value = '';
        }

        if (modalProductoImagenUpload) {
            modalProductoImagenUpload.classList.remove('has-image');
            modalProductoImagenUpload.style.backgroundImage = '';
            modalProductoImagenUpload.style.backgroundSize = '';
            modalProductoImagenUpload.style.backgroundPosition = '';

            // Restaurar contenido placeholder
            modalProductoImagenUpload.innerHTML = `
                <div class="modal-imagen-icon">📷</div>
                <div class="modal-imagen-placeholder">Haz clic para cargar imagen del producto</div>
            `;
        }

        modalProductoImagenSeleccionada = null;
    }

    // Solo resetear modo de edición si se especifica
    if (resetearModoEdicion) {
        modalProductoModoEdicion = false;
        modalProductoTarjetaEditando = null;
    }

    console.log('✅ Formulario limpiado');
}

// ===== MANEJAR CARGA DE IMAGEN =====
function manejarCargaImagen(event) {
    const archivo = event.target.files[0];
    
    if (archivo) {
        console.log('🖼️ Cargando imagen:', archivo.name);
        
        // Validar tipo de archivo
        if (!archivo.type.startsWith('image/')) {
            alert('Por favor selecciona un archivo de imagen válido.');
            return;
        }
        
        // Validar tamaño (máximo 5MB)
        if (archivo.size > 5 * 1024 * 1024) {
            alert('La imagen es demasiado grande. Por favor selecciona una imagen menor a 5MB.');
            return;
        }
        
        const reader = new FileReader();
        
        reader.onload = function(e) {
            modalProductoImagenSeleccionada = e.target.result;
            
            // Actualizar vista previa
            if (modalProductoImagenUpload) {
                modalProductoImagenUpload.classList.add('has-image');
                modalProductoImagenUpload.style.backgroundImage = `url(${modalProductoImagenSeleccionada})`;
                modalProductoImagenUpload.innerHTML = '<div class="modal-imagen-placeholder">Imagen cargada ✓</div>';
            }
            
            console.log('✅ Imagen cargada correctamente');
        };
        
        reader.onerror = function() {
            console.error('❌ Error al cargar la imagen');
            alert('Error al cargar la imagen. Por favor intenta de nuevo.');
        };
        
        reader.readAsDataURL(archivo);
    }
}

// ===== GUARDAR PRODUCTO =====
function guardarProducto() {
    console.log('💾 Guardando producto...');

    // Obtener datos del formulario
    const datosProducto = obtenerDatosFormulario();

    // Validar datos
    if (!validarDatosProducto(datosProducto)) {
        return;
    }

    if (modalProductoModoEdicion) {
        // Modo edición: actualizar tarjeta existente
        actualizarTarjetaProducto(datosProducto);
        mostrarMensajeExito(`Producto "${datosProducto.nombre}" actualizado exitosamente`);
    } else {
        // Modo creación: crear nueva tarjeta
        crearTarjetaProducto(datosProducto);
        mostrarMensajeExito(`Producto "${datosProducto.nombre}" agregado exitosamente a ${datosProducto.pestana}`);
    }

    // Actualizar KPIs
    if (typeof updateProductKPIs === 'function') {
        updateProductKPIs();
    }

    // Cerrar modal
    cerrarModalProducto();

    console.log('✅ Producto guardado correctamente');
}

// ===== OBTENER DATOS DEL FORMULARIO =====
function obtenerDatosFormulario() {
    const nombre = document.getElementById('modal-nombre-input')?.value.trim() || '';
    const categoria = document.getElementById('modal-categoria-input')?.value.trim() || '';
    const subcategoria = document.getElementById('modal-subcategoria-input')?.value.trim() || '';
    const precioActual = document.getElementById('modal-precio-actual-input')?.value.trim() || '';
    const precioAnterior = document.getElementById('modal-precio-anterior-input')?.value.trim() || '';
    
    // Obtener pestaña seleccionada
    const pestanaSeleccionada = document.querySelector('input[name="modal-pestana"]:checked')?.value || '';
    
    return {
        nombre,
        categoria,
        subcategoria,
        precioActual,
        precioAnterior,
        pestana: pestanaSeleccionada,
        imagen: modalProductoImagenSeleccionada
    };
}

// ===== VALIDAR DATOS DEL PRODUCTO =====
function validarDatosProducto(datos) {
    // Validar campos obligatorios
    if (!datos.nombre) {
        alert('Por favor ingresa el nombre del producto.');
        document.getElementById('modal-nombre-input')?.focus();
        return false;
    }
    
    if (!datos.categoria) {
        alert('Por favor ingresa la categoría del producto.');
        document.getElementById('modal-categoria-input')?.focus();
        return false;
    }
    
    if (!datos.precioActual) {
        alert('Por favor ingresa el precio actual del producto.');
        document.getElementById('modal-precio-actual-input')?.focus();
        return false;
    }
    
    if (!datos.pestana) {
        alert('Por favor selecciona en qué pestaña quieres agregar el producto.');
        return false;
    }
    
    // Validar formato de precios
    if (isNaN(parseFloat(datos.precioActual))) {
        alert('El precio actual debe ser un número válido.');
        document.getElementById('modal-precio-actual-input')?.focus();
        return false;
    }
    
    if (datos.precioAnterior && isNaN(parseFloat(datos.precioAnterior))) {
        alert('El precio anterior debe ser un número válido.');
        document.getElementById('modal-precio-anterior-input')?.focus();
        return false;
    }
    
    return true;
}

// ===== CREAR TARJETA DE PRODUCTO =====
function crearTarjetaProducto(datos) {
    console.log('🏗️ Creando tarjeta de producto para pestaña:', datos.pestana);
    
    // Obtener contenedor de la pestaña
    const contenedor = document.getElementById(`slider-${datos.pestana}`);
    if (!contenedor) {
        console.error('❌ No se encontró el contenedor para la pestaña:', datos.pestana);
        return;
    }
    
    // Crear elemento de la tarjeta
    const tarjeta = document.createElement('div');
    tarjeta.className = 'producto-card';
    
    // Construir HTML de la tarjeta
    const categoriaCompleta = datos.subcategoria ? 
        `${datos.categoria} / ${datos.subcategoria}` : 
        datos.categoria;
    
    const precioAnteriorHTML = datos.precioAnterior ? 
        `<span class="precio-anterior">$${parseFloat(datos.precioAnterior).toFixed(2)}</span>` : 
        '';
    
    const imagenHTML = datos.imagen ? 
        `style="background-image: url(${datos.imagen}); background-size: cover; background-position: center;"` : 
        '';
    
    tarjeta.innerHTML = `
        <div class="producto-image" ${imagenHTML}>${datos.imagen ? '' : 'Imagen del Producto'}</div>
        <div class="producto-categoria">${categoriaCompleta}</div>
        <div class="producto-nombre">${datos.nombre}</div>
        <div class="producto-precios">
            <span class="precio-actual">$${parseFloat(datos.precioActual).toFixed(2)}</span>
            ${precioAnteriorHTML}
        </div>
        <div class="producto-seccion">
            <span class="seccion-texto">${datos.pestana}</span>
            <div class="seccion-actions">
                <span class="action-icon edit-icon" title="Editar producto">✏️</span>
                <span class="action-icon delete-icon" title="Eliminar producto">🗑️</span>
            </div>
        </div>
    `;
    
    // Insertar después de la tarjeta de agregar
    const tarjetaAgregar = contenedor.querySelector('.add-producto-card');
    if (tarjetaAgregar) {
        // Insertar inmediatamente después de la tarjeta de agregar
        tarjetaAgregar.insertAdjacentElement('afterend', tarjeta);
    } else {
        // Si no hay tarjeta de agregar, insertar al principio
        contenedor.insertBefore(tarjeta, contenedor.firstChild);
    }

    // Agregar event listeners a los nuevos iconos
    agregarEventListenersIconos(tarjeta);

    console.log('✅ Tarjeta de producto creada exitosamente');
}

// ===== ACTUALIZAR TARJETA DE PRODUCTO EXISTENTE =====
function actualizarTarjetaProducto(datos) {
    console.log('🔄 Actualizando tarjeta de producto...');

    if (!modalProductoTarjetaEditando) {
        console.error('❌ No hay tarjeta para editar');
        return;
    }

    const tarjeta = modalProductoTarjetaEditando;
    const pestanaOriginal = tarjeta.querySelector('.seccion-texto')?.textContent.trim();

    // Si cambió de pestaña, mover la tarjeta
    if (datos.pestana !== pestanaOriginal) {
        // Remover de la pestaña original
        tarjeta.remove();

        // Crear nueva tarjeta en la pestaña destino
        crearTarjetaProducto(datos);

        console.log(`✅ Producto movido de ${pestanaOriginal} a ${datos.pestana}`);
        return;
    }

    // Actualizar contenido de la tarjeta existente
    const categoriaCompleta = datos.subcategoria ?
        `${datos.categoria} / ${datos.subcategoria}` :
        datos.categoria;

    const precioAnteriorHTML = datos.precioAnterior ?
        `<span class="precio-anterior">$${parseFloat(datos.precioAnterior).toFixed(2)}</span>` :
        '';

    // Actualizar imagen
    const imagenElement = tarjeta.querySelector('.producto-image');
    if (imagenElement) {
        if (datos.imagen) {
            imagenElement.style.backgroundImage = `url(${datos.imagen})`;
            imagenElement.style.backgroundSize = 'cover';
            imagenElement.style.backgroundPosition = 'center';
            imagenElement.textContent = '';
        } else {
            imagenElement.style.backgroundImage = '';
            imagenElement.textContent = 'Imagen del Producto';
        }
    }

    // Actualizar categoría
    const categoriaElement = tarjeta.querySelector('.producto-categoria');
    if (categoriaElement) {
        categoriaElement.textContent = categoriaCompleta;
    }

    // Actualizar nombre
    const nombreElement = tarjeta.querySelector('.producto-nombre');
    if (nombreElement) {
        nombreElement.textContent = datos.nombre;
    }

    // Actualizar precios
    const precioActualElement = tarjeta.querySelector('.precio-actual');
    if (precioActualElement) {
        precioActualElement.textContent = `$${parseFloat(datos.precioActual).toFixed(2)}`;
    }

    // Actualizar precio anterior
    const preciosContainer = tarjeta.querySelector('.producto-precios');
    if (preciosContainer) {
        // Remover precio anterior existente
        const precioAnteriorExistente = preciosContainer.querySelector('.precio-anterior');
        if (precioAnteriorExistente) {
            precioAnteriorExistente.remove();
        }

        // Agregar nuevo precio anterior si existe
        if (datos.precioAnterior) {
            preciosContainer.insertAdjacentHTML('beforeend', precioAnteriorHTML);
        }
    }

    // Actualizar sección (pestaña)
    const seccionTextoElement = tarjeta.querySelector('.seccion-texto');
    if (seccionTextoElement) {
        seccionTextoElement.textContent = datos.pestana;
    }

    console.log('✅ Tarjeta de producto actualizada exitosamente');
}

// ===== AGREGAR EVENT LISTENERS A ICONOS DE NUEVA TARJETA =====
function agregarEventListenersIconos(tarjeta) {
    const editIcon = tarjeta.querySelector('.edit-icon');
    const deleteIcon = tarjeta.querySelector('.delete-icon');

    if (editIcon) {
        editIcon.addEventListener('click', function(e) {
            e.stopPropagation();
            console.log('🖊️ Editar producto clickeado');
            abrirModalProducto(tarjeta);
        });
    }

    if (deleteIcon) {
        deleteIcon.addEventListener('click', function(e) {
            e.stopPropagation();
            if (confirm('¿Estás seguro de que quieres eliminar este producto?')) {
                tarjeta.remove();
                // Actualizar KPIs después de eliminar
                if (typeof updateProductKPIs === 'function') {
                    updateProductKPIs();
                }
                console.log('🗑️ Producto eliminado');
            }
        });
    }
}

// ===== RECONECTAR EVENT LISTENERS DE ICONOS EXISTENTES =====
function reconectarEventListenersIconos() {
    console.log('🔗 Reconectando event listeners de iconos existentes...');

    // Obtener todos los iconos de edición existentes
    const iconosEditar = document.querySelectorAll('.edit-icon');
    console.log(`🔗 Encontrados ${iconosEditar.length} iconos de edición`);

    iconosEditar.forEach((icono, index) => {
        // Remover event listeners existentes (si los hay)
        icono.replaceWith(icono.cloneNode(true));

        // Obtener la referencia actualizada
        const iconoActualizado = document.querySelectorAll('.edit-icon')[index];

        // Agregar nuevo event listener
        iconoActualizado.addEventListener('click', function(e) {
            e.stopPropagation();
            console.log('🖊️ Icono de edición clickeado');

            const tarjetaProducto = this.closest('.producto-card');
            console.log('🖊️ Tarjeta encontrada:', tarjetaProducto);

            if (tarjetaProducto && !tarjetaProducto.classList.contains('add-producto-card')) {
                console.log('🖊️ Abriendo modal para editar producto');
                abrirModalProducto(tarjetaProducto);
            } else {
                console.log('🖊️ Tarjeta no válida para edición');
            }
        });
    });

    console.log('✅ Event listeners de iconos reconectados');
}

// ===== MOSTRAR MENSAJE DE ÉXITO =====
function mostrarMensajeExito(mensaje) {
    // Crear elemento de mensaje
    const mensajeDiv = document.createElement('div');
    mensajeDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #10b981;
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        z-index: 10001;
        font-size: 0.9rem;
        font-weight: 500;
        max-width: 300px;
        opacity: 0;
        transform: translateX(100%);
        transition: all 0.3s ease;
    `;
    mensajeDiv.textContent = mensaje;

    // Agregar al DOM
    document.body.appendChild(mensajeDiv);

    // Animar entrada
    setTimeout(() => {
        mensajeDiv.style.opacity = '1';
        mensajeDiv.style.transform = 'translateX(0)';
    }, 100);

    // Remover después de 3 segundos
    setTimeout(() => {
        mensajeDiv.style.opacity = '0';
        mensajeDiv.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (mensajeDiv.parentNode) {
                mensajeDiv.parentNode.removeChild(mensajeDiv);
            }
        }, 300);
    }, 3000);
}
