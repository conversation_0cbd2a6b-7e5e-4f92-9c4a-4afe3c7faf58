<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Panel</title>

    <!-- Archivos CSS externos -->
    <link rel="stylesheet" href="admin_css_folder/admin_header.css">
    <link rel="stylesheet" href="admin_css_folder/admin_responsive_menu.css">
    <link rel="stylesheet" href="admin_css_folder/admin_estadisticas.css">
    <link rel="stylesheet" href="admin_css_folder/admin_productos.css">
    <link rel="stylesheet" href="admin_css_folder/admin_modal_producto.css">
    <link rel="stylesheet" href="admin_css_folder/admin_carrusel.css">
    <link rel="stylesheet" href="admin_css_folder/admin_marca.css">
    <link rel="stylesheet" href="admin_css_folder/admin_banner.css">
    <link rel="stylesheet" href="admin_css_folder/admin_empresa.css">
    <link rel="stylesheet" href="admin_css_folder/admin_footer.css">

<!--  --></head>

<body>
    <!-- ===== HEADER PRINCIPAL ===== -->
    <header class="header">
        <!-- Contenedor del título y el icono -->
        <div class="header-title">
            <h1>AdminPanel</h1>           <!-- Título principal -->
            <span class="header-icon">⚙️</span> <!-- Icono de configuración -->
        </div>
        
        <!-- Navegación del header -->
        <nav class="header-nav">
            <a href="#mi-plan">Mi Plan</a>                 <!-- Enlace a Mi Plan -->
            <a href="#cambio-plan">Cambio de Plan</a>      <!-- Enlace a Cambio de Plan -->
            <a href="#mi-perfil">Mi Perfil</a>             <!-- Enlace a Mi Perfil -->
            <a href="#ayuda">Ayuda</a>                     <!-- Enlace a Ayuda -->
            <a href="#cerrar-sesion">Cerrar Sesión</a>     <!-- Enlace para cerrar sesión -->
        </nav>
    </header>

    <!-- ===== MENÚ RESPONSIVE FIJO ===== -->
    <div class="responsive-menu" id="responsive-menu">
        <div class="responsive-menu-container">
            <!-- Botones principales del menú -->
            <div class="menu-buttons">
                <button class="menu-btn" data-container="estadisticas">
                    <span class="menu-btn-text">Panel de Estadísticas</span>
                </button>
                <button class="menu-btn" data-container="productos">
                    <span class="menu-btn-text">Gestión de Productos</span>
                </button>
                <button class="menu-btn" data-container="carruseles">
                    <span class="menu-btn-text">Carruseles de Imágenes</span>
                </button>
                <button class="menu-btn" data-container="banner">
                    <span class="menu-btn-text">Banner Publicitario</span>
                </button>
                <button class="menu-btn" data-container="empresa">
                    <span class="menu-btn-text">Datos de la Empresa</span>
                </button>
            </div>
            <!-- Título de opciones de navegación -->
            <div class="nav-options-title">
                <h3>Administrar Cuenta</h3>
            </div>

            <!-- Opciones de navegación del header -->
            <div class="menu-nav-options">
                <div class="nav-options-row">
                    <button class="nav-option-btn" data-nav="mi-perfil">Mi Perfil</button>
                    <button class="nav-option-btn" data-nav="mi-plan">Mi Plan</button>
                </div>
                <div class="nav-options-row">
                    <button class="nav-option-btn" data-nav="cambio-plan">Cambio de Plan</button>
                    <button class="nav-option-btn" data-nav="ayuda">Ayuda</button>
                </div>
                <div class="nav-options-row">
                    <button class="nav-option-btn" data-nav="cerrar-sesion">Cerrar Sesión</button>
                </div>
            </div>
            <!-- FOOTER RESPONSIVE SOLO EN EL MENÚ, AL FINAL -->
        </div>
        <div class="footer-responsive-texts">
            <div class="footer-responsive-row">
                <span class="footer-responsive-item">Privacidad</span>
                <span class="footer-responsive-item">Cookies</span>
                <span class="footer-responsive-item">Reenvolso</span>
                <span class="footer-responsive-item">Seguridad</span>
            </div>
            <div class="footer-responsive-row footer-responsive-row-center">
                <span class="footer-responsive-item">Condiciones y Términos</span>
            </div>
            <hr class="footer-responsive-divider">
            <div class="footer-responsive-legal">
                <div class="footer-responsive-copyright">© 2025 Solo a un CLICK. Todos los derechos reservados.</div>
                <div class="footer-responsive-desc">Solo a un CLICK es una plataforma de exhibición. Los productos publicados son responsabilidad exclusiva de la tienda que los ofrece.</div>
            </div>
        </div>
    </div>

    <!-- ===== BOTÓN DE RETORNO AL MENÚ ===== -->
    <div class="back-to-menu" id="back-to-menu">
        <button class="back-btn" id="back-btn">
            <span class="back-text">Menú</span>
        </button>
    </div>

    <!-- ===== CONTENIDO PRINCIPAL ===== -->
    <main class="main-content">

<!-- ===== HEADER DE ESTADÍSTICAS ===== -->
<div class="estadisticas-header">
    <h1 class="estadisticas-title">Panel de Estadísticas</h1>
    <p class="estadisticas-slogan">Monitorea el rendimiento y crecimiento de tu plataforma en tiempo real</p>
</div>

<!-- ===== CONTENEDOR PRINCIPAL DE ESTADÍSTICAS ===== -->
<div class="estadisticas-container">


    <!-- ===== CONTENIDO RESPONSIVE (6 FILAS) ===== -->
    <div class="responsive-content">

        <!-- ===== FILA 1: 2 KPIs GRANDES (Columna izquierda - Fila 1) ===== -->
        <div class="responsive-row responsive-row-2kpis">
            <div class="kpi">
                <div class="kpi-title">Visitas Totales</div>
                <div class="kpi-value">12,847</div>
                <div class="kpi-subtitle">
                    <span class="kpi-change positive">+8.3%</span>
                    total esta semana
                </div>
            </div>
            <div class="kpi">
                <div class="kpi-title">Visitantes Únicos</div>
                <div class="kpi-value">9,234</div>
                <div class="kpi-subtitle">
                    <span class="kpi-change positive">+12.5%</span>
                    total esta semana
                </div>
            </div>
        </div>

        <!-- ===== FILA 2: 2 KPIs GRANDES (Columna izquierda - Fila 2) ===== -->
        <div class="responsive-row responsive-row-2kpis">
            <div class="kpi">
                <div class="kpi-title">Productos Vistos</div>
                <div class="kpi-value">4,567</div>
                <div class="kpi-subtitle">
                    <span class="kpi-change positive">+15.2%</span>
                    total esta semana
                </div>
            </div>
            <div class="kpi">
                <div class="kpi-title">Productos Compartidos</div>
                <div class="kpi-value">892</div>
                <div class="kpi-subtitle">
                    <span class="kpi-change positive">+23.7%</span>
                    total esta semana
                </div>
            </div>
        </div>

        <!-- ===== FILA 3: 3 KPIs PEQUEÑOS (Productos - Fila 1) ===== -->
        <div class="responsive-row responsive-row-3kpis">
            <div class="kpi" id="kpi-destacados-responsive">
                <div class="kpi-title">Destacados</div>
                <div class="kpi-value" id="count-destacados-responsive">0</div>
                <div class="kpi-subtitle">productos</div>
            </div>
            <div class="kpi" id="kpi-ofertas-responsive">
                <div class="kpi-title">Ofertas</div>
                <div class="kpi-value" id="count-ofertas-responsive">0</div>
                <div class="kpi-subtitle">productos</div>
            </div>
            <div class="kpi" id="kpi-novedades-responsive">
                <div class="kpi-title">Novedades</div>
                <div class="kpi-value" id="count-novedades-responsive">0</div>
                <div class="kpi-subtitle">productos</div>
            </div>
        </div>

        <!-- ===== FILA 4: 3 KPIs PEQUEÑOS (Productos - Fila 2) ===== -->
        <div class="responsive-row responsive-row-3kpis">
            <div class="kpi" id="kpi-mas-vistos-responsive">
                <div class="kpi-title">No te lo Pierdas</div>
                <div class="kpi-value" id="count-mas-vistos-responsive">0</div>
                <div class="kpi-subtitle">productos</div>
            </div>
            <div class="kpi" id="kpi-tendencias-responsive">
                <div class="kpi-title">Tendencias</div>
                <div class="kpi-value" id="count-tendencias-responsive">0</div>
                <div class="kpi-subtitle">productos</div>
            </div>
            <div class="kpi" id="kpi-liquidaciones-responsive">
                <div class="kpi-title">Liquidaciones</div>
                <div class="kpi-value" id="count-liquidaciones-responsive">0</div>
                <div class="kpi-subtitle">productos</div>
            </div>
        </div>

        <!-- ===== FILA 5: 2 KPIs MEDIANOS (Análisis - Fila 1) ===== -->
        <div class="responsive-row responsive-row-2kpis-analysis">
            <div class="kpi">
                <div class="kpi-title">Tasa de Apertura</div>
                <div class="kpi-value">68.4%</div>
                <div class="kpi-subtitle">
                    <span class="kpi-change positive">+5.2%</span>
                    vs mes anterior
                </div>
            </div>
            <div class="kpi">
                <div class="kpi-title">Compartidos WhatsApp</div>
                <div class="kpi-value">1,247</div>
                <div class="kpi-subtitle">
                    <span class="kpi-change positive">+18.3%</span>
                    vs mes anterior
                </div>
            </div>
        </div>

        <!-- ===== FILA 6: 2 KPIs MEDIANOS (Análisis - Fila 2) ===== -->
        <div class="responsive-row responsive-row-2kpis-analysis">
            <div class="kpi">
                <div class="kpi-title">Tiempo Promedio</div>
                <div class="kpi-value">2:34min</div>
                <div class="kpi-subtitle">
                    <span class="kpi-change positive">+0:23min</span>
                    vs mes anterior
                </div>
            </div>
            <div class="kpi">
                <div class="kpi-title">Interacciones Totales</div>
                <div class="kpi-value">8,956</div>
                <div class="kpi-subtitle">
                    <span class="kpi-change positive">+12.7%</span>
                    vs mes anterior
                </div>
            </div>
        </div>

    </div>

    <!-- ===== COLUMNA IZQUIERDA (Solo visible en desktop) ===== -->
    <div class="left-column">

        <!-- Primera fila de la columna izquierda (2 KPIs) -->
        <div class="left-row">
            <div class="kpi kpi-left">              <!-- Primer KPI de la primera fila izquierda -->
                <div class="kpi-header">
                    <div class="kpi-title">Visitas Totales</div>
                    <div class="kpi-main-info">
                        <div class="kpi-value">12,847</div>
                        <div class="kpi-subtitle">
                            <span class="kpi-change positive">+8.3%</span>
                            total esta semana
                        </div>
                    </div>
                </div>
                <div class="kpi-chart-container">
                    <canvas id="chart-visitas-totales" width="200" height="80"></canvas>
                </div>
            </div>
            <div class="kpi kpi-left">              <!-- Segundo KPI de la primera fila izquierda -->
                <div class="kpi-header">
                    <div class="kpi-title">Visitantes Únicos</div>
                    <div class="kpi-main-info">
                        <div class="kpi-value">9,234</div>
                        <div class="kpi-subtitle">
                            <span class="kpi-change positive">+12.5%</span>
                            total esta semana
                        </div>
                    </div>
                </div>
                <div class="kpi-chart-container">
                    <canvas id="chart-visitantes-unicos" width="200" height="80"></canvas>
                </div>
            </div>
        </div>

        <!-- Segunda fila de la columna izquierda (2 KPIs) -->
        <div class="left-row">
            <div class="kpi kpi-left">              <!-- Primer KPI de la segunda fila izquierda -->
                <div class="kpi-header">
                    <div class="kpi-title">Productos Vistos</div>
                    <div class="kpi-main-info">
                        <div class="kpi-value">4,567</div>
                        <div class="kpi-subtitle">
                            <span class="kpi-change positive">+15.2%</span>
                            total esta semana
                        </div>
                    </div>
                </div>
                <div class="kpi-chart-container">
                    <canvas id="chart-productos-vistos" width="200" height="80"></canvas>
                </div>
            </div>
            <div class="kpi kpi-left">              <!-- Segundo KPI de la segunda fila izquierda -->
                <div class="kpi-header">
                    <div class="kpi-title">Productos Compartidos</div>
                    <div class="kpi-main-info">
                        <div class="kpi-value">892</div>
                        <div class="kpi-subtitle">
                            <span class="kpi-change positive">+23.7%</span>
                            total esta semana
                        </div>
                    </div>
                </div>
                <div class="kpi-chart-container">
                    <canvas id="chart-productos-compartidos" width="200" height="80"></canvas>
                </div>
            </div>
        </div>

    </div>

    <!-- ===== COLUMNA DERECHA (Solo visible en desktop) ===== -->
    <div class="right-column">

        <!-- Primera fila de la columna derecha (3 KPIs) -->
        <div class="right-row">
            <div class="kpi kpi-right-small" id="kpi-destacados-desktop">       <!-- Primer KPI de la primera fila derecha -->
                <div class="kpi-title">Destacados</div>
                <div class="kpi-value" id="count-destacados-desktop">0</div>
                <div class="kpi-subtitle">productos</div>
            </div>
            <div class="kpi kpi-right-small" id="kpi-ofertas-desktop">       <!-- Segundo KPI de la primera fila derecha -->
                <div class="kpi-title">Ofertas</div>
                <div class="kpi-value" id="count-ofertas-desktop">0</div>
                <div class="kpi-subtitle">productos</div>
            </div>
            <div class="kpi kpi-right-small" id="kpi-novedades-desktop">       <!-- Tercer KPI de la primera fila derecha -->
                <div class="kpi-title">Novedades</div>
                <div class="kpi-value" id="count-novedades-desktop">0</div>
                <div class="kpi-subtitle">productos</div>
            </div>
        </div>

        <!-- Segunda fila de la columna derecha (3 KPIs) -->
        <div class="right-row">
            <div class="kpi kpi-right-small" id="kpi-mas-vistos-desktop">       <!-- Primer KPI de la segunda fila derecha -->
                <div class="kpi-title">No te lo Pierdas</div>
                <div class="kpi-value" id="count-mas-vistos-desktop">0</div>
                <div class="kpi-subtitle">productos</div>
            </div>
            <div class="kpi kpi-right-small" id="kpi-tendencias-desktop">       <!-- Segundo KPI de la segunda fila derecha -->
                <div class="kpi-title">Tendencias</div>
                <div class="kpi-value" id="count-tendencias-desktop">0</div>
                <div class="kpi-subtitle">productos</div>
            </div>
            <div class="kpi kpi-right-small" id="kpi-liquidaciones-desktop">       <!-- Tercer KPI de la segunda fila derecha -->
                <div class="kpi-title">Liquidaciones</div>
                <div class="kpi-value" id="count-liquidaciones-desktop">0</div>
                <div class="kpi-subtitle">productos</div>
            </div>
        </div>

        <!-- Tercera fila de la columna derecha (4 KPIs de análisis) -->
        <div class="right-row-analysis">
            <div class="kpi kpi-right-analysis" id="kpi-tasa-apertura-desktop">           <!-- Primer KPI de análisis -->
                <div class="kpi-title">Tasa de Apertura</div>
                <div class="kpi-value">68.4%</div>
                <div class="kpi-subtitle">
                    <span class="kpi-change positive">+5.2%</span>
                    vs mes anterior
                </div>
            </div>
            <div class="kpi kpi-right-analysis" id="kpi-compartidos-whatsapp-desktop">        <!-- Segundo KPI de análisis -->
                <div class="kpi-title">Compartidos WhatsApp</div>
                <div class="kpi-value">1,247</div>
                <div class="kpi-subtitle">
                    <span class="kpi-change positive">+18.3%</span>
                    vs mes anterior
                </div>
            </div>
            <div class="kpi kpi-right-analysis" id="kpi-tiempo-visualizacion-desktop">         <!-- Tercer KPI de análisis -->
                <div class="kpi-title">Tiempo Promedio</div>
                <div class="kpi-value">2:34min</div>
                <div class="kpi-subtitle">
                    <span class="kpi-change positive">+0:23min</span>
                    vs mes anterior
                </div>
            </div>
            <div class="kpi kpi-right-analysis" id="kpi-interacciones-desktop">         <!-- Cuarto KPI de análisis -->
                <div class="kpi-title">Interacciones Totales</div>
                <div class="kpi-value">8,956</div>
                <div class="kpi-subtitle">
                    <span class="kpi-change positive">+12.7%</span>
                    vs mes anterior
                </div>
            </div>
        </div>



    </div>
</div>


<!-- ===== CONTENEDOR DE GESTIÓN DE PRODUCTOS ===== -->
<div class="gestion-productos-container">
    
    <!-- ===== HEADER DE GESTIÓN DE PRODUCTOS ===== -->
    <div class="gestion-productos-header">
        <h2 class="gestion-productos-title">Gestión de Productos</h2>
    </div>
    
    <!-- ===== CONTENEDOR DE PESTAÑAS ===== -->
    <div class="tabs-container">
        
        <!-- ===== NAVEGACIÓN DE PESTAÑAS ===== -->
        <div class="tabs-nav">
            <button class="tab-button active" data-tab="destacados">Destacados</button>
            <button class="tab-button" data-tab="ofertas">Ofertas</button>
            <button class="tab-button" data-tab="novedades">Novedades</button>
            <button class="tab-button" data-tab="mas-vistos">No te lo Pierdas</button>
            <button class="tab-button" data-tab="tendencias">Tendencias</button>
            <button class="tab-button" data-tab="liquidaciones">Liquidaciones</button>
        </div>
        
        <!-- ===== CONTENIDO DE PESTAÑA: DESTACADOS ===== -->
        <div class="tab-content active" id="destacados">
            <div class="productos-slider-container">
                <!-- Flecha izquierda -->
                <button class="slider-arrow prev" onclick="moveSlider('destacados', -1)">‹</button>
                
                <!-- Contenedor de productos -->
                <div class="productos-slider" id="slider-destacados">

                    <!-- Tarjeta de agregar producto -->
                    <div class="producto-card add-producto-card">
                        <div class="add-producto-icon">+</div>
                        <div class="add-producto-text">Agregar Producto</div>
                    </div>

                    <!-- Producto de ejemplo 1 -->
                    <div class="producto-card">
                        <div class="producto-image">Imagen del Producto</div>
                        <div class="producto-categoria">Electrónicos / Smartphones</div>
                        <div class="producto-nombre">iPhone 15 Pro Max 256GB</div>
                        <div class="producto-precios">
                            <span class="precio-actual">$1,299.99</span>
                            <span class="precio-anterior">$1,499.99</span>
                        </div>
                        <div class="producto-seccion">
                            <span class="seccion-texto">destacados</span>
                            <div class="seccion-actions">
                                <span class="action-icon edit-icon" title="Editar producto">✏️</span>
                                <span class="action-icon delete-icon" title="Eliminar producto">🗑️</span>
                            </div>
                        </div>
                    </div>

                    <!-- Producto de ejemplo 2 -->
                    <div class="producto-card">
                        <div class="producto-image">Imagen del Producto</div>
                        <div class="producto-categoria">Ropa / Camisetas</div>
                        <div class="producto-nombre">Camiseta Premium Algodón</div>
                        <div class="producto-precios">
                            <span class="precio-actual">$29.99</span>
                        </div>
                        <div class="producto-seccion">
                            <span class="seccion-texto">destacados</span>
                            <div class="seccion-actions">
                                <span class="action-icon edit-icon" title="Editar producto">✏️</span>
                                <span class="action-icon delete-icon" title="Eliminar producto">🗑️</span>
                            </div>
                        </div>
                    </div>

                    <!-- Producto de ejemplo 3 -->
                    <div class="producto-card">
                        <div class="producto-image">Imagen del Producto</div>
                        <div class="producto-categoria">Hogar / Decoración</div>
                        <div class="producto-nombre">Lámpara LED Moderna</div>
                        <div class="producto-precios">
                            <span class="precio-actual">$89.99</span>
                            <span class="precio-anterior">$119.99</span>
                        </div>
                        <div class="producto-seccion">
                            <span class="seccion-texto">destacados</span>
                            <div class="seccion-actions">
                                <span class="action-icon edit-icon" title="Editar producto">✏️</span>
                                <span class="action-icon delete-icon" title="Eliminar producto">🗑️</span>
                            </div>
                        </div>
                    </div>
                    
                </div>
                
                <!-- Flecha derecha -->
                <button class="slider-arrow next" onclick="moveSlider('destacados', 1)">›</button>
            </div>
        </div>
        
        <!-- ===== CONTENIDO DE PESTAÑA: OFERTAS ===== -->
        <div class="tab-content" id="ofertas">
            <div class="productos-slider-container">
                <!-- Flecha izquierda -->
                <button class="slider-arrow prev" onclick="moveSlider('ofertas', -1)">‹</button>
                
                <!-- Contenedor de productos -->
                <div class="productos-slider" id="slider-ofertas">

                    <!-- Tarjeta de agregar producto -->
                    <div class="producto-card add-producto-card">
                        <div class="add-producto-icon">+</div>
                        <div class="add-producto-text">Agregar Producto</div>
                    </div>

                    <!-- Producto de ejemplo 1 -->
                    <div class="producto-card">
                        <div class="producto-image">Imagen del Producto</div>
                        <div class="producto-categoria">Deportes / Zapatillas</div>
                        <div class="producto-nombre">Nike Air Max 270</div>
                        <div class="producto-precios">
                            <span class="precio-actual">$99.99</span>
                            <span class="precio-anterior">$149.99</span>
                        </div>
                        <div class="producto-seccion">
                            <span class="seccion-texto">ofertas</span>
                            <div class="seccion-actions">
                                <span class="action-icon edit-icon" title="Editar producto">✏️</span>
                                <span class="action-icon delete-icon" title="Eliminar producto">🗑️</span>
                            </div>
                        </div>
                    </div>

                    <!-- Producto de ejemplo 2 -->
                    <div class="producto-card">
                        <div class="producto-image">Imagen del Producto</div>
                        <div class="producto-categoria">Tecnología / Auriculares</div>
                        <div class="producto-nombre">AirPods Pro 2da Gen</div>
                        <div class="producto-precios">
                            <span class="precio-actual">$199.99</span>
                            <span class="precio-anterior">$249.99</span>
                        </div>
                        <div class="producto-seccion">
                            <span class="seccion-texto">ofertas</span>
                            <div class="seccion-actions">
                                <span class="action-icon edit-icon" title="Editar producto">✏️</span>
                                <span class="action-icon delete-icon" title="Eliminar producto">🗑️</span>
                            </div>
                        </div>
                    </div>

                    <!-- Producto de ejemplo 3 -->
                    <div class="producto-card">
                        <div class="producto-image">Imagen del Producto</div>
                        <div class="producto-categoria">Cocina / Electrodomésticos</div>
                        <div class="producto-nombre">Cafetera Espresso</div>
                        <div class="producto-precios">
                            <span class="precio-actual">$159.99</span>
                            <span class="precio-anterior">$199.99</span>
                        </div>
                        <div class="producto-seccion">
                            <span class="seccion-texto">ofertas</span>
                            <div class="seccion-actions">
                                <span class="action-icon edit-icon" title="Editar producto">✏️</span>
                                <span class="action-icon delete-icon" title="Eliminar producto">🗑️</span>
                            </div>
                        </div>
                    </div>
                    
                </div>
                
                <!-- Flecha derecha -->
                <button class="slider-arrow next" onclick="moveSlider('ofertas', 1)">›</button>
            </div>
        </div>
        
        <!-- ===== CONTENIDO DE PESTAÑA: NOVEDADES ===== -->
        <div class="tab-content" id="novedades">
            <div class="productos-slider-container">
                <!-- Flecha izquierda -->
                <button class="slider-arrow prev" onclick="moveSlider('novedades', -1)">‹</button>
                
                <!-- Contenedor de productos -->
                <div class="productos-slider" id="slider-novedades">

                    <!-- Tarjeta de agregar producto -->
                    <div class="producto-card add-producto-card">
                        <div class="add-producto-icon">+</div>
                        <div class="add-producto-text">Agregar Producto</div>
                    </div>

                    <!-- Producto de ejemplo 1 -->
                    <div class="producto-card">
                        <div class="producto-image">Imagen del Producto</div>
                        <div class="producto-categoria">Gaming / Consolas</div>
                        <div class="producto-nombre">PlayStation 5 Slim</div>
                        <div class="producto-precios">
                            <span class="precio-actual">$499.99</span>
                        </div>
                        <div class="producto-seccion">
                            <span class="seccion-texto">novedades</span>
                            <div class="seccion-actions">
                                <span class="action-icon edit-icon" title="Editar producto">✏️</span>
                                <span class="action-icon delete-icon" title="Eliminar producto">🗑️</span>
                            </div>
                        </div>
                    </div>

                    <!-- Producto de ejemplo 2 -->
                    <div class="producto-card">
                        <div class="producto-image">Imagen del Producto</div>
                        <div class="producto-categoria">Moda / Accesorios</div>
                        <div class="producto-nombre">Reloj Inteligente Series 9</div>
                        <div class="producto-precios">
                            <span class="precio-actual">$399.99</span>
                        </div>
                        <div class="producto-seccion">
                            <span class="seccion-texto">novedades</span>
                            <div class="seccion-actions">
                                <span class="action-icon edit-icon" title="Editar producto">✏️</span>
                                <span class="action-icon delete-icon" title="Eliminar producto">🗑️</span>
                            </div>
                        </div>
                    </div>

                    <!-- Producto de ejemplo 3 -->
                    <div class="producto-card">
                        <div class="producto-image">Imagen del Producto</div>
                        <div class="producto-categoria">Libros / Ficción</div>
                        <div class="producto-nombre">Novela Bestseller 2024</div>
                        <div class="producto-precios">
                            <span class="precio-actual">$24.99</span>
                        </div>
                        <div class="producto-seccion">
                            <span class="seccion-texto">novedades</span>
                            <div class="seccion-actions">
                                <span class="action-icon edit-icon" title="Editar producto">✏️</span>
                                <span class="action-icon delete-icon" title="Eliminar producto">🗑️</span>
                            </div>
                        </div>
                    </div>
                    
                </div>
                
                <!-- Flecha derecha -->
                <button class="slider-arrow next" onclick="moveSlider('novedades', 1)">›</button>
            </div>
        </div>
        
        <!-- ===== CONTENIDO DE PESTAÑA: NO TE LO PIERDAS ===== -->
        <div class="tab-content" id="mas-vistos">
            <div class="productos-slider-container">
                <!-- Flecha izquierda -->
                <button class="slider-arrow prev" onclick="moveSlider('mas-vistos', -1)">‹</button>
                
                <!-- Contenedor de productos -->
                <div class="productos-slider" id="slider-mas-vistos">

                    <!-- Tarjeta de agregar producto -->
                    <div class="producto-card add-producto-card">
                        <div class="add-producto-icon">+</div>
                        <div class="add-producto-text">Agregar Producto</div>
                    </div>

                    <!-- Producto de ejemplo 1 -->
                    <div class="producto-card">
                        <div class="producto-image">Imagen del Producto</div>
                        <div class="producto-categoria">Belleza / Cuidado Facial</div>
                        <div class="producto-nombre">Crema Hidratante Premium</div>
                        <div class="producto-precios">
                            <span class="precio-actual">$49.99</span>
                            <span class="precio-anterior">$59.99</span>
                        </div>
                        <div class="producto-seccion">
                            <span class="seccion-texto">no te lo pierdas</span>
                            <div class="seccion-actions">
                                <span class="action-icon edit-icon" title="Editar producto">✏️</span>
                                <span class="action-icon delete-icon" title="Eliminar producto">🗑️</span>
                            </div>
                        </div>
                    </div>

                    <!-- Producto de ejemplo 2 -->
                    <div class="producto-card">
                        <div class="producto-image">Imagen del Producto</div>
                        <div class="producto-categoria">Fitness / Equipamiento</div>
                        <div class="producto-nombre">Mancuernas Ajustables</div>
                        <div class="producto-precios">
                            <span class="precio-actual">$129.99</span>
                        </div>
                        <div class="producto-seccion">
                            <span class="seccion-texto">no te lo pierdas</span>
                            <div class="seccion-actions">
                                <span class="action-icon edit-icon" title="Editar producto">✏️</span>
                                <span class="action-icon delete-icon" title="Eliminar producto">🗑️</span>
                            </div>
                        </div>
                    </div>

                    <!-- Producto de ejemplo 3 -->
                    <div class="producto-card">
                        <div class="producto-image">Imagen del Producto</div>
                        <div class="producto-categoria">Automóvil / Accesorios</div>
                        <div class="producto-nombre">Cargador Inalámbrico Auto</div>
                        <div class="producto-precios">
                            <span class="precio-actual">$39.99</span>
                            <span class="precio-anterior">$49.99</span>
                        </div>
                        <div class="producto-seccion">
                            <span class="seccion-texto">no te lo pierdas</span>
                            <div class="seccion-actions">
                                <span class="action-icon edit-icon" title="Editar producto">✏️</span>
                                <span class="action-icon delete-icon" title="Eliminar producto">🗑️</span>
                            </div>
                        </div>
                    </div>
                    
                </div>
                
                <!-- Flecha derecha -->
                <button class="slider-arrow next" onclick="moveSlider('mas-vistos', 1)">›</button>
            </div>
        </div>
        
        <!-- ===== CONTENIDO DE PESTAÑA: TENDENCIAS ===== -->
        <div class="tab-content" id="tendencias">
            <div class="productos-slider-container">
                <!-- Flecha izquierda -->
                <button class="slider-arrow prev" onclick="moveSlider('tendencias', -1)">‹</button>
                
                <!-- Contenedor de productos -->
                <div class="productos-slider" id="slider-tendencias">

                    <!-- Tarjeta de agregar producto -->
                    <div class="producto-card add-producto-card">
                        <div class="add-producto-icon">+</div>
                        <div class="add-producto-text">Agregar Producto</div>
                    </div>

                    <!-- Producto de ejemplo 1 -->
                    <div class="producto-card">
                        <div class="producto-image">Imagen del Producto</div>
                        <div class="producto-categoria">Moda / Streetwear</div>
                        <div class="producto-nombre">Sudadera Oversized</div>
                        <div class="producto-precios">
                            <span class="precio-actual">$79.99</span>
                        </div>
                        <div class="producto-seccion">
                            <span class="seccion-texto">tendencias</span>
                            <div class="seccion-actions">
                                <span class="action-icon edit-icon" title="Editar producto">✏️</span>
                                <span class="action-icon delete-icon" title="Eliminar producto">🗑️</span>
                            </div>
                        </div>
                    </div>

                    <!-- Producto de ejemplo 2 -->
                    <div class="producto-card">
                        <div class="producto-image">Imagen del Producto</div>
                        <div class="producto-categoria">Tech / Gadgets</div>
                        <div class="producto-nombre">Cámara Instantánea Retro</div>
                        <div class="producto-precios">
                            <span class="precio-actual">$149.99</span>
                            <span class="precio-anterior">$179.99</span>
                        </div>
                        <div class="producto-seccion">
                            <span class="seccion-texto">tendencias</span>
                            <div class="seccion-actions">
                                <span class="action-icon edit-icon" title="Editar producto">✏️</span>
                                <span class="action-icon delete-icon" title="Eliminar producto">🗑️</span>
                            </div>
                        </div>
                    </div>

                    <!-- Producto de ejemplo 3 -->
                    <div class="producto-card">
                        <div class="producto-image">Imagen del Producto</div>
                        <div class="producto-categoria">Plantas / Interior</div>
                        <div class="producto-nombre">Monstera Deliciosa</div>
                        <div class="producto-precios">
                            <span class="precio-actual">$34.99</span>
                        </div>
                        <div class="producto-seccion">
                            <span class="seccion-texto">tendencias</span>
                            <div class="seccion-actions">
                                <span class="action-icon edit-icon" title="Editar producto">✏️</span>
                                <span class="action-icon delete-icon" title="Eliminar producto">🗑️</span>
                            </div>
                        </div>
                    </div>
                    
                </div>
                
                <!-- Flecha derecha -->
                <button class="slider-arrow next" onclick="moveSlider('tendencias', 1)">›</button>
            </div>
        </div>
        
        <!-- ===== CONTENIDO DE PESTAÑA: LIQUIDACIONES ===== -->
        <div class="tab-content" id="liquidaciones">
            <div class="productos-slider-container">
                <!-- Flecha izquierda -->
                <button class="slider-arrow prev" onclick="moveSlider('liquidaciones', -1)">‹</button>
                
                <!-- Contenedor de productos -->
                <div class="productos-slider" id="slider-liquidaciones">

                    <!-- Tarjeta de agregar producto -->
                    <div class="producto-card add-producto-card">
                        <div class="add-producto-icon">+</div>
                        <div class="add-producto-text">Agregar Producto</div>
                    </div>

                    <!-- Producto de ejemplo 1 -->
                    <div class="producto-card">
                        <div class="producto-image">Imagen del Producto</div>
                        <div class="producto-categoria">Electrónicos / Tablets</div>
                        <div class="producto-nombre">iPad Air 4ta Gen</div>
                        <div class="producto-precios">
                            <span class="precio-actual">$399.99</span>
                            <span class="precio-anterior">$599.99</span>
                        </div>
                        <div class="producto-seccion">
                            <span class="seccion-texto">liquidaciones</span>
                            <div class="seccion-actions">
                                <span class="action-icon edit-icon" title="Editar producto">✏️</span>
                                <span class="action-icon delete-icon" title="Eliminar producto">🗑️</span>
                            </div>
                        </div>
                    </div>

                    <!-- Producto de ejemplo 2 -->
                    <div class="producto-card">
                        <div class="producto-image">Imagen del Producto</div>
                        <div class="producto-categoria">Ropa / Temporada Pasada</div>
                        <div class="producto-nombre">Chaqueta de Invierno</div>
                        <div class="producto-precios">
                            <span class="precio-actual">$59.99</span>
                            <span class="precio-anterior">$129.99</span>
                        </div>
                        <div class="producto-seccion">
                            <span class="seccion-texto">liquidaciones</span>
                            <div class="seccion-actions">
                                <span class="action-icon edit-icon" title="Editar producto">✏️</span>
                                <span class="action-icon delete-icon" title="Eliminar producto">🗑️</span>
                            </div>
                        </div>
                    </div>

                    <!-- Producto de ejemplo 3 -->
                    <div class="producto-card">
                        <div class="producto-image">Imagen del Producto</div>
                        <div class="producto-categoria">Hogar / Muebles</div>
                        <div class="producto-nombre">Silla de Oficina Ergonómica</div>
                        <div class="producto-precios">
                            <span class="precio-actual">$199.99</span>
                            <span class="precio-anterior">$349.99</span>
                        </div>
                        <div class="producto-seccion">
                            <span class="seccion-texto">liquidaciones</span>
                            <div class="seccion-actions">
                                <span class="action-icon edit-icon" title="Editar producto">✏️</span>
                                <span class="action-icon delete-icon" title="Eliminar producto">🗑️</span>
                            </div>
                        </div>
                    </div>
                    
                </div>
                
                <!-- Flecha derecha -->
                <button class="slider-arrow next" onclick="moveSlider('liquidaciones', 1)">›</button>
            </div>
        </div>
        
    </div>
</div>
<!-- ===== POP-UP DE PRODUCTO ===== -->
<div class="producto-popup-overlay" id="producto-popup">
    <div class="producto-popup-container">
        <!-- Botón de cerrar -->
        <button class="popup-close-btn" onclick="closeProductPopup()">×</button>

        <!-- Contenido del pop-up -->
        <div class="popup-producto-card">
            <div class="popup-producto-image" id="popup-image">Imagen del Producto</div>
            <div class="popup-producto-categoria" id="popup-categoria">Categoría</div>
            <div class="popup-producto-nombre" id="popup-nombre">Nombre del Producto</div>
            <div class="popup-producto-precios">
                <span class="popup-precio-actual" id="popup-precio-actual">$0.00</span>
                <span class="popup-precio-anterior" id="popup-precio-anterior">$0.00</span>
            </div>
            <div class="popup-producto-seccion" id="popup-seccion">Sección</div>

            <!-- Botones de acción -->
            <div class="popup-actions">
                <button class="popup-btn popup-btn-edit">Editar</button>
                <button class="popup-btn popup-btn-delete">Eliminar</button>
            </div>
        </div>
    </div>
</div>

<!-- ===== MODAL DE AGREGAR PRODUCTO ===== -->
<div class="modal-producto-overlay" id="modal-producto-overlay">
    <div class="modal-producto-container">
        <!-- Botón de cerrar -->
        <button class="modal-producto-close" id="modal-producto-close">×</button>

        <!-- Título del modal -->
        <h2 class="modal-producto-title">Agregar Nuevo Producto</h2>

        <!-- Formulario -->
        <form class="modal-producto-form" id="modal-producto-form">

            <!-- Nombre del producto -->
            <div class="modal-form-group">
                <label class="modal-form-label" for="modal-nombre-input">Nombre del Producto *</label>
                <input type="text" class="modal-form-input" id="modal-nombre-input" placeholder="Ej: iPhone 15 Pro Max 256GB" required>
            </div>

            <!-- Categorías -->
            <div class="modal-categorias-group">
                <div class="modal-form-group">
                    <label class="modal-form-label" for="modal-categoria-input">Categoría *</label>
                    <input type="text" class="modal-form-input" id="modal-categoria-input" placeholder="Ej: Electrónicos" required>
                </div>
                <div class="modal-form-group">
                    <label class="modal-form-label" for="modal-subcategoria-input">Subcategoría</label>
                    <input type="text" class="modal-form-input" id="modal-subcategoria-input" placeholder="Ej: Smartphones">
                </div>
            </div>

            <!-- Precios -->
            <div class="modal-precios-group">
                <div class="modal-form-group">
                    <label class="modal-form-label" for="modal-precio-actual-input">Precio Actual *</label>
                    <input type="number" class="modal-form-input" id="modal-precio-actual-input" placeholder="0.00" step="0.01" min="0" required>
                </div>
                <div class="modal-form-group">
                    <label class="modal-form-label" for="modal-precio-anterior-input">Precio Anterior</label>
                    <input type="number" class="modal-form-input" id="modal-precio-anterior-input" placeholder="0.00" step="0.01" min="0">
                </div>
            </div>

            <!-- Selección de pestaña -->
            <div class="modal-pestanas-group">
                <label class="modal-form-label">Pestaña de Destino *</label>
                <div class="modal-pestanas-grid">
                    <div class="modal-pestana-option">
                        <input type="radio" class="modal-pestana-radio" id="modal-pestana-destacados" name="modal-pestana" value="destacados">
                        <label class="modal-pestana-label" for="modal-pestana-destacados">Destacados</label>
                    </div>
                    <div class="modal-pestana-option">
                        <input type="radio" class="modal-pestana-radio" id="modal-pestana-ofertas" name="modal-pestana" value="ofertas">
                        <label class="modal-pestana-label" for="modal-pestana-ofertas">Ofertas</label>
                    </div>
                    <div class="modal-pestana-option">
                        <input type="radio" class="modal-pestana-radio" id="modal-pestana-novedades" name="modal-pestana" value="novedades">
                        <label class="modal-pestana-label" for="modal-pestana-novedades">Novedades</label>
                    </div>
                    <div class="modal-pestana-option">
                        <input type="radio" class="modal-pestana-radio" id="modal-pestana-mas-vistos" name="modal-pestana" value="mas-vistos">
                        <label class="modal-pestana-label" for="modal-pestana-mas-vistos">No te lo Pierdas</label>
                    </div>
                    <div class="modal-pestana-option">
                        <input type="radio" class="modal-pestana-radio" id="modal-pestana-tendencias" name="modal-pestana" value="tendencias">
                        <label class="modal-pestana-label" for="modal-pestana-tendencias">Tendencias</label>
                    </div>
                    <div class="modal-pestana-option">
                        <input type="radio" class="modal-pestana-radio" id="modal-pestana-liquidaciones" name="modal-pestana" value="liquidaciones">
                        <label class="modal-pestana-label" for="modal-pestana-liquidaciones">Liquidaciones</label>
                    </div>
                </div>
            </div>

            <!-- Carga de imagen -->
            <div class="modal-imagen-group">
                <label class="modal-form-label">Imagen del Producto</label>
                <div class="modal-imagen-upload" id="modal-imagen-upload">
                    <div class="modal-imagen-icon">📷</div>
                    <div class="modal-imagen-placeholder">Haz clic para cargar imagen del producto</div>
                    <input type="file" class="modal-imagen-input" id="modal-imagen-input" accept="image/*">
                </div>
            </div>

        </form>

        <!-- Botones de acción -->
        <div class="modal-producto-actions">
            <button type="button" class="modal-btn modal-btn-cancelar" id="modal-btn-cancelar">Cancelar</button>
            <button type="button" class="modal-btn modal-btn-guardar" id="modal-btn-guardar">Guardar Producto</button>
        </div>

    </div>
</div>

<!-- ===== CONTENEDOR DE CARRUSELES DE IMÁGENES ===== -->
<div class="carruseles-container">

    <!-- ===== HEADER DE CARRUSELES ===== -->
    <div class="carruseles-header">
        <h2 class="carruseles-title">Carruseles de Imágenes</h2>
    </div>

    <!-- ===== CONTENEDOR DE PESTAÑAS DE CARRUSELES ===== -->
    <div class="carrusel-tabs-container">

        <!-- ===== NAVEGACIÓN DE PESTAÑAS ===== -->
        <div class="carrusel-tabs-nav">
            <button class="carrusel-tab-button active" data-carrusel-tab="carrusel1">Carrusel1</button>
            <button class="carrusel-tab-button" data-carrusel-tab="carrusel2">Carrusel2</button>
        </div>

        <!-- ===== CONTENIDO DE PESTAÑA: CARRUSEL1 ===== -->
        <div class="carrusel-tab-content active" id="carrusel1">
            <div class="carrusel-grid">
                <!-- Las tarjetas se generan dinámicamente con JavaScript -->
            </div>
        </div>

        <!-- ===== CONTENIDO DE PESTAÑA: CARRUSEL2 ===== -->
        <div class="carrusel-tab-content" id="carrusel2">
            <div class="carrusel-grid">
                <!-- Las tarjetas se generan dinámicamente con JavaScript -->
            </div>
        </div>

    </div>
</div>

<!-- ===== CONTENEDOR DE BANNER PUBLICITARIO ===== -->
<!-- ===== TÍTULO EXTERNO DEL BANNER ===== -->
<h2 class="banner-external-title">Banner Publicitario</h2>

<div class="banner-container">
    <!-- ===== CONTENIDO PRINCIPAL ===== -->
    <div class="banner-content">

        <!-- ===== COLUMNA IZQUIERDA - BANNER LADO IZQUIERDO ===== -->
        <div class="banner-column banner-column">
            <h3 class="column-title">Banner de tu Negocio (lado izquierdo)</h3>

            <!-- Tarjeta de carga de banner -->
            <div class="image-upload-card" id="banner-upload-card-izquierdo">
                <div class="image-placeholder">
                    <div class="image-placeholder-icon">+</div>
                    <div class="image-placeholder-text">Clic para cargar banner</div>
                </div>
            </div>

            <!-- Información del formato del banner -->
            <div class="banner-format-info">
                <span class="format-info-text">Imagen Rectángular</span>
            </div>

            <!-- Botones de acción del banner -->
            <div class="action-buttons">
                <button class="btn-save" id="banner-save-btn-izquierdo">Guardar</button>
                <button class="btn-cancel" id="banner-cancel-btn-izquierdo">Cancelar</button>
            </div>
        </div>

        <!-- ===== COLUMNA DERECHA - BANNER LADO DERECHO ===== -->
        <div class="banner-column banner-column">
            <h3 class="column-title">Banner de tu Negocio (lado derecho)</h3>

            <!-- Tarjeta de carga de banner -->
            <div class="image-upload-card" id="banner-upload-card-derecho">
                <div class="image-placeholder">
                    <div class="image-placeholder-icon">+</div>
                    <div class="image-placeholder-text">Clic para cargar banner</div>
                </div>
            </div>

            <!-- Información del formato del banner -->
            <div class="banner-format-info">
                <span class="format-info-text">Imagen Rectángular</span>
            </div>

            <!-- Botones de acción del banner -->
            <div class="action-buttons">
                <button class="btn-save" id="banner-save-btn-derecho">Guardar</button>
                <button class="btn-cancel" id="banner-cancel-btn-derecho">Cancelar</button>
            </div>
        </div>

    </div>
</div>

<!-- ===== MODAL DE PRODUCTOS ELIMINADO ===== -->
<!-- Los modales de agregar/editar productos han sido eliminados del sistema -->

<!-- ===== CONTENEDOR DE DATOS DE LA EMPRESA ===== -->
<!-- ===== TÍTULO EXTERNO DE DATOS DE LA EMPRESA ===== -->
<h2 class="empresa-external-title">Datos de la Empresa</h2>

<div class="empresa-container">
    <!-- ===== CONTENIDO PRINCIPAL ===== -->
    <div class="empresa-content">

        <!-- ===== COLUMNA IZQUIERDA - INFORMACIÓN BÁSICA ===== -->
        <div class="empresa-column">
            <h3 class="column-title">Información Básica</h3>

            <!-- Título de la Página -->
            <div class="field-group">
                <label for="titulo-pagina" class="field-label">Título de la Página</label>
                <input type="text" id="titulo-pagina" class="field-input"
                       placeholder="Máximo 60 caracteres. Aparece en la pestaña del navegador.">
                <div class="field-info">Este título aparece en la pestaña del navegador y en los resultados de búsqueda.</div>
            </div>

            <!-- Descripción de la Tienda -->
            <div class="field-group">
                <label for="descripcion-tienda" class="field-label">Descripción de la Tienda</label>
                <textarea id="descripcion-tienda" class="field-textarea" placeholder="Máximo 25 palabras. Descripción breve que aparece en la página principal." oninput="validarDescripcionTienda()"></textarea>
                <div class="field-info" id="descripcion-tienda-info">Descripción breve que aparece en la página principal de tu tienda.</div>
                <div class="field-info" id="descripcion-tienda-error" style="color:#e57373; display:none; font-size:0.95em;">Máximo 25 palabras permitidas. Por favor, hazlo más breve.</div>
            </div>

            <!-- Sobre Nosotros -->
            <div class="field-group">
                <label for="sobre-nosotros" class="field-label">Sobre Nosotros</label>
                <textarea id="sobre-nosotros" class="field-textarea"
                          placeholder="Máximo 50 palabras. Historia y descripción detallada de tu negocio."></textarea>
                <div class="field-info">Historia y descripción detallada que aparece en la sección "Sobre Nosotros".</div>
            </div>
        </div>

        <!-- ===== COLUMNA DERECHA - DATOS DE CONTACTO ===== -->
        <div class="empresa-column">
            <h3 class="column-title">Datos de Contacto</h3>

            <!-- Dirección -->
            <div class="field-group">
                <label for="direccion" class="field-label">Dirección</label>
                <input type="text" id="direccion" class="field-input"
                       placeholder="Dirección completa de tu negocio">
                <div class="field-info">Dirección física donde se encuentra tu negocio.</div>
            </div>

            <!-- Teléfono y WhatsApp en una sola fila -->
            <div class="field-row">
                <div class="field-group" style="flex:1; min-width:0;">
                    <label for="telefono" class="field-label">Teléfono</label>
                    <input type="tel" id="telefono" class="field-input"
                           placeholder="Número de teléfono principal">
                    <div class="field-info">Número de teléfono principal para contacto.</div>
                </div>
                <div class="field-group" style="flex:1; min-width:0;">
                    <label for="whatsapp" class="field-label">WhatsApp</label>
                    <input type="tel" id="whatsapp" class="field-input"
                           placeholder="Número de WhatsApp para contacto">
                    <div class="field-info">Número de WhatsApp para atención al cliente.</div>
                </div>
            </div>

            <!-- Ubicación (Google Maps) -->
            <div class="field-group">
                <label for="ubicacion" class="field-label">Ubicación</label>
                <input type="url" id="ubicacion" class="field-input"
                       placeholder="Pegar link de Google Maps">
                <div class="field-info">Enlace de Google Maps para mostrar la ubicación de tu negocio.</div>
            </div>

            <!-- Redes sociales: Facebook, Instagram y X (antes Twitter) -->
            <div class="field-row">
                <div class="field-group" style="flex:1; min-width:0;">
                    <label for="facebook" class="field-label">Facebook</label>
                    <input type="url" id="facebook" class="field-input" placeholder="URL de Facebook">
                    <div class="field-info">Enlace a tu página de Facebook.</div>
                </div>
                <div class="field-group" style="flex:1; min-width:0;">
                    <label for="instagram" class="field-label">Instagram</label>
                    <input type="url" id="instagram" class="field-input" placeholder="URL de Instagram">
                    <div class="field-info">Enlace a tu perfil de Instagram.</div>
                </div>
                <div class="field-group" style="flex:1; min-width:0;">
                    <label for="x" class="field-label">X (antes Twitter)</label>
                    <input type="url" id="x" class="field-input" placeholder="URL de X (Twitter)">
                    <div class="field-info">Enlace a tu perfil de X (Twitter).</div>
                </div>
            </div>

        </div>

        <!-- ===== BOTONES DE ACCIÓN ===== -->
        <div class="action-buttons">
            <button class="btn-save" id="empresa-save-btn">Guardar</button>
            <button class="btn-cancel" id="empresa-cancel-btn">Cancelar</button>
        </div>

    </div>
</div>

    </main>

    <!-- Archivo JavaScript para menú responsive -->
    <script src="admin_js_folder/admin_responsive_menu.js"></script>
    <!-- Archivo JavaScript para administración de productos -->
    <script src="admin_js_folder/admin_productos.js"></script>
    <!-- Archivo JavaScript para carruseles de imágenes -->
    <script src="admin_js_folder/admin_carrusel.js"></script>
    <!-- Archivo JavaScript para banner publicitario -->
    <script src="admin_js_folder/admin_banner.js"></script>
    <!-- Archivo JavaScript para datos de la empresa -->
    <script src="admin_js_folder/admin_empresa.js"></script>
    <!-- Archivo JavaScript para modal de productos -->
    <script src="admin_js_folder/admin_modal_producto.js"></script>
    <!-- Librería Chart.js para gráficos interactivos -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Archivo JavaScript para gráficos de estadísticas -->
    <script src="admin_js_folder/admin_estadisticas_graficos.js"></script>

    <!-- Script para inicializar KPIs después de cargar todos los scripts -->
    <!-- (Eliminado porque ya está en admin_productos.js) -->
    <!-- Script de prueba para pestañas KPI eliminado -->

    <script>
    function validarDescripcionTienda() {
        const textarea = document.getElementById('descripcion-tienda');
        const error = document.getElementById('descripcion-tienda-error');
        const texto = textarea.value.trim();
        const palabras = texto.length > 0 ? texto.split(/\s+/) : [];
        if (palabras.length > 25) {
            error.style.display = 'block';
            textarea.style.borderColor = '#e57373';
        } else {
            error.style.display = 'none';
            textarea.style.borderColor = '';
        }
    }
    </script>

    <!-- ===== FOOTER DESKTOP ===== -->
    <footer class="footer-desktop">
        <div class="footer-desktop-links">
            <span class="footer-desktop-link">Privacidad</span>
            <span class="footer-desktop-link">Cookies</span>
            <span class="footer-desktop-link">Reenvolso</span>
            <span class="footer-desktop-link">Seguridad</span>
            <span class="footer-desktop-link">Condiciones y Términos</span>
        </div>
        <hr class="footer-desktop-divider">
        <div class="footer-desktop-legal">
            <div class="footer-desktop-copyright">© 2025 Solo a un CLICK. Todos los derechos reservados.</div>
            <div class="footer-desktop-desc">Solo a un CLICK es una plataforma de exhibición. Los productos publicados son responsabilidad exclusiva de la tienda que los ofrece.</div>
        </div>
    </footer>

</body>
</html>
