/* ===== ESTILOS DEL MENÚ RESPONSIVE FIJO ===== */

/* ===== MENÚ PRINCIPAL RESPONSIVE ===== */
.responsive-menu {
    display: none;                    /* Oculto por defecto en desktop */
    position: fixed;                  /* Posición fija */
    top: 0;                          /* Pegado al top */
    left: 0;                         /* Pegado a la izquierda */
    width: 100%;                     /* Ancho completo */
    height: 100vh;                   /* Altura completa de la ventana */
    background-color: #2d3748;       /* Fondo igual que main-content */
    z-index: 1000;                   /* Por encima de otros elementos */
    overflow-y: auto;                /* Scroll vertical si es necesario */
}

.responsive-menu-container {
    padding: 20px;                   /* Espaciado interno */
    padding-top: 140px;              /* Espacio para el header fijo (aumentado) */
    max-width: 100%;                 /* Ancho máximo */
}

/* ===== BOTONES PRINCIPALES DEL MENÚ ===== */
.menu-buttons {
    display: flex;                   /* Flexbox para organizar botones */
    flex-direction: column;          /* Botones en columna vertical */
    gap: 15px;                       /* Espacio entre botones */
    margin-bottom: 30px;             /* Margen inferior */
}

.menu-btn {
    display: flex;                   /* Flexbox para centrar texto */
    align-items: center;             /* Centra verticalmente */
    justify-content: center;         /* Centra horizontalmente */
    width: 100%;                     /* Ancho completo */
    padding: 24px 20px;              /* Espaciado interno más alto */
    background: rgba(106, 27, 154, 0.3); /* Fondo púrpura semi-transparente */
    border: 2px solid rgba(255, 255, 255, 0.3); /* Borde blanco semi-transparente */
    border-radius: 10px;              /* Bordes casi cuadrados */
    color: white;                    /* Texto blanco */
    font-size: 1.1rem;              /* Tamaño de fuente más grande */
    font-weight: 500;                /* Peso de fuente */
    cursor: pointer;                 /* Cursor pointer */
    transition: all 0.3s ease;      /* Transición suave */
    text-align: center;              /* Texto centrado */
}

.menu-btn:hover,
.menu-btn:active {
    background: rgba(106, 27, 154, 0.5); /* Fondo más opaco al hover */
    border-color: rgba(255, 255, 255, 0.5); /* Borde más opaco */
    transform: translateY(-2px);     /* Eleva ligeramente */
    box-shadow: 0 4px 12px rgba(106, 27, 154, 0.3); /* Sombra púrpura */
}

.menu-btn-text {
    font-size: 1.1rem;              /* Tamaño del texto más grande */
    line-height: 1.2;               /* Altura de línea */
    font-weight: 500;                /* Peso de fuente */
    text-align: center;              /* Texto centrado */
}

/* ===== LÍNEA DIVISORIA ===== */
.menu-divider {
    width: 100%;                     /* Ancho completo */
    height: 2px;                     /* Altura de la línea */
    background: linear-gradient(90deg,
        rgba(106, 27, 154, 0.5) 0%,
        rgba(106, 27, 154, 0.8) 50%,
        rgba(106, 27, 154, 0.5) 100%); /* Gradiente púrpura */
    margin: 20px 0;                  /* Margen vertical */
    border-radius: 1px;              /* Bordes ligeramente redondeados */
}

/* ===== TÍTULO DE OPCIONES DE NAVEGACIÓN ===== */
.nav-options-title {
    text-align: center;              /* Centra el título */
    margin: 15px 0;                  /* Margen vertical */
}

.nav-options-title h3 {
    color: white;                    /* Texto blanco */
    font-size: 1.1rem;              /* Tamaño de fuente */
    font-weight: 600;                /* Peso de fuente */
    margin: 0;                       /* Sin margen */
    text-transform: uppercase;       /* Texto en mayúsculas */
    letter-spacing: 0.5px;           /* Espaciado entre letras */
}

/* ===== OPCIONES DE NAVEGACIÓN DEL HEADER ===== */
.menu-nav-options {
    display: flex;                   /* Flexbox para las filas */
    flex-direction: column;          /* Filas en columna */
    gap: 12px;                       /* Espacio entre filas */
}

.nav-options-row {
    display: flex;                   /* Flexbox para botones en fila */
    gap: 10px;                       /* Espacio entre botones */
    justify-content: center;         /* Centra los botones */
}

.nav-option-btn {
    flex: 1;                         /* Botones del mismo tamaño */
    padding: 12px 16px;              /* Espaciado interno */
    background: rgba(106, 27, 154, 0.3); /* Fondo púrpura semi-transparente */
    border: 2px solid rgba(255, 255, 255, 0.3); /* Borde blanco semi-transparente */
    border-radius: 10px;              /* Bordes casi cuadrados */
    color: white;                    /* Texto blanco */
    font-size: 0.9rem;              /* Tamaño de fuente más pequeño */
    font-weight: 500;                /* Peso de fuente */
    cursor: pointer;                 /* Cursor pointer */
    transition: all 0.3s ease;      /* Transición suave */
    text-align: center;              /* Texto centrado */
    white-space: nowrap;             /* Evita salto de línea */
}

.nav-option-btn:hover,
.nav-option-btn:active {
    background: rgba(106, 27, 154, 0.5); /* Fondo más opaco al hover */
    border-color: rgba(255, 255, 255, 0.5); /* Borde más opaco */
    transform: translateY(-1px);     /* Eleva ligeramente */
    box-shadow: 0 2px 8px rgba(106, 27, 154, 0.3); /* Sombra púrpura */
}

/* ===== BOTÓN DE RETORNO AL MENÚ ===== */
.back-to-menu {
    display: none;                   /* Oculto por defecto */
    position: fixed;                 /* Posición fija */
    top: 55px;                       /* Posición dentro del header, parte inferior */
    right: 15px;                     /* Desde la derecha */
    z-index: 1003;                   /* Por encima del header */
}

.back-btn {
    display: flex;                   /* Flexbox para centrar texto */
    align-items: center;             /* Centra verticalmente */
    justify-content: center;         /* Centra horizontalmente */
    padding: 8px 16px;               /* Espaciado interno aún más grande */
    background: rgba(255, 255, 255, 0.15); /* Fondo semi-transparente como header */
    border: 2px solid #6a1b9a;       /* Borde morado igual que el gradiente del header */
    border-radius: 10px;             /* Bordes redondeados más grandes */
    color: #6a1b9a;                  /* Texto morado igual que el borde */
    font-size: 0.9rem;              /* Tamaño de fuente aún más grande */
    font-weight: 600;                /* Peso de fuente más bold */
    cursor: pointer;                 /* Cursor pointer */
    transition: all 0.3s ease;      /* Transición suave */
    backdrop-filter: blur(10px);     /* Efecto de desenfoque como header */
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2); /* Sombra más grande */
}

.back-btn:hover,
.back-btn:active {
    background: rgba(106, 27, 154, 0.1); /* Fondo morado semi-transparente al hover */
    border-color: #8b5cf6;           /* Borde morado más claro al hover */
    color: #8b5cf6;                  /* Texto morado más claro al hover */
    transform: translateY(-1px);     /* Eleva ligeramente */
    box-shadow: 0 4px 12px rgba(106, 27, 154, 0.3); /* Sombra morada */
}

.back-text {
    font-size: 0.9rem;              /* Tamaño del texto más grande */
    line-height: 1;                  /* Altura de línea */
    font-weight: 600;                /* Peso de fuente más bold */
}

/* ===== RESPONSIVE PARA MÓVILES (≤768px) ===== */
@media (max-width: 768px) {
    /* ===== MOSTRAR MENÚ EN RESPONSIVE ===== */
    .responsive-menu {
        display: block;              /* Muestra el menú en responsive */
    }

    /* ===== MOSTRAR BOTÓN DE RETORNO EN RESPONSIVE ===== */
    .back-to-menu {
        display: block;              /* Muestra el botón de retorno */
    }

    /* ===== AJUSTAR MAIN-CONTENT PARA HEADER FIJO ===== */
    .main-content {
        padding-top: 120px;          /* Espacio para el header fijo */
    }

    /* ===== AJUSTES PARA PANTALLAS PEQUEÑAS ===== */
    .responsive-menu-container {
        padding: 15px;               /* Reduce padding */
        padding-top: 120px;          /* Espacio del header ajustado */
    }

    .main-content {
        padding-top: 100px;          /* Reduce espacio para pantallas pequeñas */
    }

    .menu-btn {
        padding: 20px 18px;          /* Mantiene altura pero reduce padding lateral */
        font-size: 1rem;            /* Reduce ligeramente el tamaño de fuente */
    }

    .menu-btn-text {
        font-size: 1rem;            /* Ajusta tamaño del texto */
    }

    .nav-options-title h3 {
        font-size: 1rem;             /* Reduce tamaño del título */
    }

    .nav-option-btn {
        padding: 10px 12px;          /* Reduce padding */
        font-size: 0.85rem;         /* Reduce tamaño de fuente */
    }
}

/* ===== RESPONSIVE PARA MÓVILES PEQUEÑOS (≤480px) ===== */
@media (max-width: 480px) {
    .responsive-menu-container {
        padding: 12px;               /* Padding mínimo */
        padding-top: 100px;          /* Espacio del header ajustado */
    }

    .main-content {
        padding-top: 90px;           /* Espacio reducido para móviles pequeños */
    }

    .menu-btn {
        padding: 18px 15px;          /* Mantiene altura reducida */
        font-size: 0.95rem;         /* Fuente ligeramente más pequeña */
    }

    .menu-btn-text {
        font-size: 0.95rem;         /* Ajusta tamaño del texto */
    }

    .nav-options-title h3 {
        font-size: 0.95rem;          /* Reduce más el tamaño del título */
    }

    .nav-option-btn {
        padding: 8px 10px;           /* Padding mínimo */
        font-size: 0.8rem;          /* Fuente más pequeña */
    }

    .back-to-menu {
        top: 45px;                   /* Ajusta posición para móviles pequeños */
    }

    .back-btn {
        padding: 6px 12px;           /* Padding ajustado */
        font-size: 0.8rem;          /* Fuente ajustada */
    }

    .back-text {
        font-size: 0.8rem;          /* Texto ajustado */
    }
}

/* ===== RESPONSIVE PARA MÓVILES MUY PEQUEÑOS (≤360px) ===== */
@media (max-width: 360px) {
    .responsive-menu-container {
        padding: 10px;               /* Padding mínimo absoluto */
        padding-top: 90px;           /* Espacio del header ajustado */
    }

    .main-content {
        padding-top: 80px;           /* Espacio mínimo para móviles muy pequeños */
    }

    .menu-buttons {
        gap: 12px;                   /* Reduce espacio entre botones */
    }

    .menu-btn {
        padding: 16px 12px;          /* Mantiene altura mínima */
        font-size: 0.9rem;          /* Fuente mínima ajustada */
    }

    .menu-btn-text {
        font-size: 0.9rem;          /* Ajusta tamaño del texto */
    }

    .nav-options-row {
        gap: 8px;                    /* Reduce espacio entre botones */
    }

    .nav-options-title h3 {
        font-size: 0.9rem;           /* Tamaño mínimo del título */
    }

    .nav-option-btn {
        padding: 6px 8px;            /* Padding mínimo absoluto */
        font-size: 0.75rem;         /* Fuente mínima */
    }

    .back-to-menu {
        top: 40px;                   /* Ajusta posición para móviles muy pequeños */
        right: 10px;                 /* Reduce margen derecho */
    }

    .back-btn {
        padding: 5px 10px;           /* Padding mínimo ajustado */
        font-size: 0.75rem;         /* Fuente mínima ajustada */
    }

    .back-text {
        font-size: 0.75rem;         /* Texto mínimo ajustado */
    }
}

.menu-btn, .nav-option-btn, .back-btn {
    border-radius: 10px;
}

/* ===== FOOTER RESPONSIVE ESPECIAL ===== */
.footer-responsive {
    background: linear-gradient(45deg, #6a1b9a 40%, #ffd54f 90%);
    color: white;
    padding: 22px 10px 12px 10px;
    text-align: center;
    box-shadow: 0 -2px 10px rgba(0,0,0,0.08);
    margin-top: 0;
    width: 100%;
}

.footer-responsive-columns {
    display: flex;
    justify-content: center;
    align-items: flex-start;
    gap: 38px;
    margin-bottom: 10px;
    flex-wrap: wrap;
}

.footer-responsive-col {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.footer-responsive-option {
    background: rgba(255,255,255,0.08);
    border: 1.2px solid #fff;
    border-radius: 7px;
    color: white;
    font-size: 0.93rem;
    padding: 7px 18px;
    margin-bottom: 0;
    margin-top: 0;
    font-weight: 500;
    transition: background 0.3s, transform 0.3s;
}

.footer-responsive-option:hover {
    background: rgba(255,255,255,0.18);
    transform: translateY(-2px) scale(1.04);
}

.footer-responsive-divider {
    border: none;
    border-top: 1.5px solid #fff;
    margin: 10px auto 10px auto;
    width: 90%;
    max-width: 600px;
}

.footer-responsive-legal {
    margin-top: 6px;
}

.footer-responsive-copyright, .footer-responsive-desc {
    font-family: 'Roboto', 'Arial', 'Helvetica Neue', sans-serif;
    font-weight: 400;
    letter-spacing: 0.01em;
}

.footer-responsive-copyright {
    font-size: 0.70rem;
    margin-bottom: 2px;
    font-weight: bold;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.12);
}

.footer-responsive-desc {
    font-size: 0.68rem;
    color: #fffde7;
    opacity: 0.92;
    margin-top: 2px;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.10);
}

@media (max-width: 600px) {
    .footer-responsive-columns {
        gap: 14px;
    }
    .footer-responsive-option {
        font-size: 0.85rem;
        padding: 5px 10px;
    }
    .footer-responsive-copyright, .footer-responsive-desc {
        font-size: 0.60rem;
    }
}

/* ===== FOOTER RESPONSIVE SOLO TEXTO, DOS FILAS ===== */
.footer-responsive-texts {
    background: linear-gradient(45deg, #6a1b9a 40%, #e6b84a 90%);
    color: white;
    padding: 18px 10px 10px 10px;
    text-align: center;
    box-shadow: 0 -2px 10px rgba(0,0,0,0.08);
    margin-top: 10px;
    width: 100%;
    border-radius: 0 0 12px 12px;
}

.footer-responsive-row {
    display: flex;
    justify-content: center;
    gap: 12px;
    margin-bottom: 4px;
    flex-wrap: wrap;
}

.footer-responsive-row-center {
    justify-content: center;
}

.footer-responsive-item {
    color: white;
    font-size: 0.85rem;
    font-weight: 500;
    padding: 1px 6px;
    border-radius: 4px;
    background: none;
    margin: 0 1px;
    letter-spacing: 0.01em;
    border: none;
    box-shadow: none;
    display: inline-block;
}

.footer-responsive-item:active,
.footer-responsive-item:focus,
.footer-responsive-item:hover {
    background: rgba(255,255,255,0.10);
    color: #fff;
}

.footer-responsive-divider {
    border: none;
    border-top: 1.5px solid #fff;
    margin: 10px auto 10px auto;
    width: 90%;
    max-width: 600px;
}

.footer-responsive-legal {
    margin-top: 6px;
}

.footer-responsive-copyright {
    font-size: 0.70rem;
    margin-bottom: 2px;
    font-weight: bold;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.12);
}

.footer-responsive-desc {
    font-size: 0.68rem;
    color: #fffde7;
    opacity: 0.92;
    margin-top: 2px;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.10);
}

@media (min-width: 1025px) {
    .footer-responsive-texts { display: none; }
}
