/* ===== FUNCIONALIDAD DEL MENÚ RESPONSIVE ===== */

// ===== VARIABLES GLOBALES =====
let currentContainer = null;        // Container actualmente visible
let isMenuVisible = true;           // Estado del menú

// ===== MAPEO DE CONTAINERS =====
const containerMap = {
    'estadisticas': {
        selector: '.estadisticas-container, .estadisticas-header',
        name: 'Panel de Estadísticas'
    },
    'productos': {
        selector: '.gestion-productos-container',
        name: 'Gestión de Productos'
    },
    'carruseles': {
        selector: '.carruseles-container',
        name: 'Carruseles de Imágenes'
    },
    'marca': {
        selector: '.marca-container',
        name: 'Configuración de Marca'
    },
    'banner': {
        selector: '.banner-container, .banner-external-title',
        name: 'Banner Publicitario'
    },
    'empresa': {
        selector: '.empresa-container, .empresa-external-title',
        name: 'Datos de la Empresa'
    }
};

// ===== FUNCIÓN PARA DETECTAR SI ESTAMOS EN MODO RESPONSIVE =====
function isResponsiveMode() {
    return window.innerWidth <= 768;
}

// ===== FUNCIÓN PARA MOSTRAR EL MENÚ =====
function showMenu() {
    console.log('📱 Mostrando menú responsive...');
    
    const menu = document.getElementById('responsive-menu');
    const backButton = document.getElementById('back-to-menu');
    
    if (menu) {
        menu.style.display = 'block';
        isMenuVisible = true;
        currentContainer = null;
        
        // Oculta el botón de retorno
        if (backButton) {
            backButton.style.display = 'none';
        }
        
        // Oculta todos los containers
        hideAllContainers();
        
        console.log('✅ Menú mostrado correctamente');
    }
}

// ===== FUNCIÓN PARA OCULTAR EL MENÚ =====
function hideMenu() {
    console.log('📱 Ocultando menú responsive...');
    
    const menu = document.getElementById('responsive-menu');
    const backButton = document.getElementById('back-to-menu');
    
    if (menu) {
        menu.style.display = 'none';
        isMenuVisible = false;
        
        // Muestra el botón de retorno
        if (backButton) {
            backButton.style.display = 'block';
        }
        
        console.log('✅ Menú ocultado correctamente');
    }
}

// ===== FUNCIÓN PARA OCULTAR TODOS LOS CONTAINERS =====
function hideAllContainers() {
    console.log('🔒 Ocultando todos los containers...');

    if (isResponsiveMode()) {
        Object.values(containerMap).forEach(container => {
            const elements = document.querySelectorAll(container.selector);
            elements.forEach(element => {
                if (element) {
                    element.classList.add('responsive-hidden');
                }
            });
        });
    }

    console.log('✅ Todos los containers ocultados');
}

// ===== FUNCIÓN PARA MOSTRAR UN CONTAINER ESPECÍFICO =====
function showContainer(containerKey) {
    console.log(`📦 Mostrando container: ${containerKey}`);

    if (!containerMap[containerKey]) {
        console.error(`❌ Container no encontrado: ${containerKey}`);
        return;
    }

    if (!isResponsiveMode()) {
        console.log('⚠️ No estamos en modo responsive, ignorando');
        return;
    }

    // Oculta todos los containers primero
    hideAllContainers();

    // Muestra el container seleccionado
    const container = containerMap[containerKey];
    const elements = document.querySelectorAll(container.selector);

    elements.forEach(element => {
        if (element) {
            element.classList.remove('responsive-hidden');
        }
    });

    // Actualiza el estado
    currentContainer = containerKey;

    // Oculta el menú y muestra el botón de retorno
    hideMenu();

    // Scroll al top para mostrar el container desde el inicio
    window.scrollTo(0, 0);

    console.log(`✅ Container ${container.name} mostrado correctamente`);
}

// ===== FUNCIÓN PARA MANEJAR CLICKS EN BOTONES DEL MENÚ =====
function handleMenuButtonClick(event) {
    const button = event.currentTarget;
    const containerKey = button.getAttribute('data-container');
    
    console.log(`🖱️ Click en botón del menú: ${containerKey}`);
    
    if (containerKey && isResponsiveMode()) {
        showContainer(containerKey);
    }
}

// ===== FUNCIÓN PARA MANEJAR CLICKS EN OPCIONES DE NAVEGACIÓN =====
function handleNavOptionClick(event) {
    const button = event.currentTarget;
    const navOption = button.getAttribute('data-nav');
    
    console.log(`🖱️ Click en opción de navegación: ${navOption}`);
    
    // Aquí puedes agregar la lógica para manejar las opciones de navegación
    // Por ahora solo mostramos un mensaje
    alert(`Funcionalidad "${navOption}" - En desarrollo`);
}

// ===== FUNCIÓN PARA MANEJAR EL BOTÓN DE RETORNO =====
function handleBackButtonClick() {
    console.log('🔙 Click en botón de retorno al menú');
    
    if (isResponsiveMode()) {
        showMenu();
    }
}

// ===== FUNCIÓN PARA MANEJAR CAMBIOS DE TAMAÑO DE VENTANA =====
function handleWindowResize() {
    console.log('📏 Cambio de tamaño de ventana detectado');

    if (isResponsiveMode()) {
        // Estamos en modo responsive
        if (!isMenuVisible && !currentContainer) {
            // Si no hay menú visible ni container, muestra el menú
            showMenu();
        }
    } else {
        // Estamos en modo desktop
        const menu = document.getElementById('responsive-menu');
        const backButton = document.getElementById('back-to-menu');

        // Oculta el menú y botón de retorno
        if (menu) menu.style.display = 'none';
        if (backButton) backButton.style.display = 'none';

        // Remueve todas las clases responsive-hidden para mostrar containers en desktop
        Object.values(containerMap).forEach(container => {
            const elements = document.querySelectorAll(container.selector);
            elements.forEach(element => {
                if (element) {
                    element.classList.remove('responsive-hidden');
                }
            });
        });

        // Resetea el estado
        isMenuVisible = false;
        currentContainer = null;
    }
}

// ===== FUNCIÓN DE INICIALIZACIÓN =====
function initializeResponsiveMenu() {
    console.log('🚀 Inicializando menú responsive...');

    // Agrega listeners a los botones del menú
    const menuButtons = document.querySelectorAll('.menu-btn');
    menuButtons.forEach(button => {
        button.addEventListener('click', handleMenuButtonClick);
    });

    // Agrega listeners a las opciones de navegación
    const navButtons = document.querySelectorAll('.nav-option-btn');
    navButtons.forEach(button => {
        button.addEventListener('click', handleNavOptionClick);
    });

    // Agrega listener al botón de retorno
    const backButton = document.getElementById('back-btn');
    if (backButton) {
        backButton.addEventListener('click', handleBackButtonClick);
    }

    // Agrega listener para cambios de tamaño de ventana
    window.addEventListener('resize', handleWindowResize);

    // Configuración inicial basada en el tamaño actual
    if (isResponsiveMode()) {
        // En responsive, oculta todos los containers y muestra el menú
        hideAllContainers();
        showMenu();
    } else {
        // En desktop, asegura que todos los containers estén visibles
        Object.values(containerMap).forEach(container => {
            const elements = document.querySelectorAll(container.selector);
            elements.forEach(element => {
                if (element) {
                    element.classList.remove('responsive-hidden');
                }
            });
        });
    }

    console.log('✅ Menú responsive inicializado correctamente');
}

// ===== INICIALIZACIÓN CUANDO EL DOM ESTÉ LISTO =====
document.addEventListener('DOMContentLoaded', function() {
    console.log('📄 DOM cargado, inicializando menú responsive...');
    
    // Espera un poco para asegurar que todos los elementos estén listos
    setTimeout(initializeResponsiveMenu, 100);
});

// ===== INICIALIZACIÓN ADICIONAL CUANDO LA VENTANA ESTÉ COMPLETAMENTE CARGADA =====
window.addEventListener('load', function() {
    console.log('🌐 Ventana completamente cargada, verificando menú responsive...');

    // Verifica que todo esté funcionando correctamente
    setTimeout(function() {
        if (isResponsiveMode()) {
            hideAllContainers();
            showMenu();
        } else {
            // Asegura que en desktop todos los containers estén visibles
            Object.values(containerMap).forEach(container => {
                const elements = document.querySelectorAll(container.selector);
                elements.forEach(element => {
                    if (element) {
                        element.classList.remove('responsive-hidden');
                    }
                });
            });
        }
    }, 300);
});

// ===== FUNCIÓN DE DEBUG =====
function debugContainerStatus() {
    console.log('🔍 Estado actual de containers:');
    console.log(`📱 Modo responsive: ${isResponsiveMode()}`);
    console.log(`📋 Menú visible: ${isMenuVisible}`);
    console.log(`📦 Container actual: ${currentContainer || 'ninguno'}`);

    Object.entries(containerMap).forEach(([key, container]) => {
        const elements = document.querySelectorAll(container.selector);
        elements.forEach((element, index) => {
            const hasHiddenClass = element.classList.contains('responsive-hidden');
            const displayStyle = window.getComputedStyle(element).display;
            console.log(`  ${key}[${index}]: hidden-class=${hasHiddenClass}, display=${displayStyle}`);
        });
    });
}

// ===== EXPORTAR FUNCIONES PARA USO EXTERNO =====
window.ResponsiveMenu = {
    showMenu,
    hideMenu,
    showContainer,
    isResponsiveMode,
    handleWindowResize,
    debugContainerStatus
};
